name: apim-apps build with tests

on:
  pull_request:
    branches:
      - main
  workflow_dispatch:
concurrency:
  group: ${{ github.event.number }}
jobs:
  build-carbon:
    runs-on: ubuntu-latest
    steps:
      - name: Run hostname
        run: hostname
      - name: Fix host entry
        run: sudo echo "127.0.0.1 $(hostname)" | sudo tee -a /etc/hosts
      - name: Show hosts
        run: cat /etc/hosts
      - name: Checkout master
        uses: actions/checkout@v2
        with:
          fetch-depth: '10'
      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: 11
          distribution: 'temurin'
      - uses: actions/setup-node@v4
        with:
          node-version: '22'
      - name: Cache Maven packages
        uses: actions/cache@v3
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2
          restore-keys: ${{ runner.os }}-m2
      - name: Build apim-apps
        run: mvn clean install --file pom.xml
