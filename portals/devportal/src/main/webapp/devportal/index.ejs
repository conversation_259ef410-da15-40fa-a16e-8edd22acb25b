<!DOCTYPE html>
<!--
  ~ Copyright (c) 2021, WSO2 Inc. (http://www.wso2.org) All Rights Reserved.
  ~
  ~ WSO2 Inc. licenses this file to you under the Apache License,
  ~ Version 2.0 (the "License"); you may not use this file except
  ~ in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~ http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing,
  ~ software distributed under the License is distributed on an
  ~ "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  ~ KIND, either express or implied.  See the License for the
  ~ specific language governing permissions and limitations
  ~ under the License.
  -->

<html lang="en">
<head>
  <base href="/" />
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
  <title></title>

  <link rel="shortcut icon" href="/site/public/images/favicon.ico">
  <link rel="icon" type="image/x-icon" href="/site/public/images/_favicon.ico">
  <link href="/site/public/css/main.css" type="text/css" rel="stylesheet" />
</head>

<body dir="ltr">
  <div id="react-root">
    <div class="apim-dual-ring"></div>
  </div>
  <script type="text/javascript" src="/site/public/theme/userTheme.js"></script>
  <script type="text/javascript" src="/services/settings/settings.js"></script>
  <script type="text/javascript">
    Settings.app.customUrl.tenantDomain = 'carbon.super';
  </script>
  <script src="/site/public/fonts/iconfont/MaterialIcons.js"></script>
  <script src="/<%= htmlWebpackPlugin.files.js[0] %>"></script>
  <link rel="stylesheet" href="/site/public/fonts/iconfont/material-icons.css">
  <link rel="stylesheet" href="/site/public/css/overrides.css">
</body>

</html>
