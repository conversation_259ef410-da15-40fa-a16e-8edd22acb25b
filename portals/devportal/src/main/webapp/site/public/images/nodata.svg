<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="24.870832mm"
   height="24.870832mm"
   viewBox="0 0 24.870832 24.870832"
   version="1.1"
   id="svg3332"
   inkscape:version="0.92.2 5c3e80d, 2017-08-06"
   sodipodi:docname="nodata.svg">
  <defs
     id="defs3326" />
  <sodipodi:namedview
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1.0"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="1"
     inkscape:cx="54.597119"
     inkscape:cy="6.011219"
     inkscape:document-units="mm"
     inkscape:current-layer="layer1"
     showgrid="false"
     fit-margin-top="0"
     fit-margin-left="0"
     fit-margin-right="0"
     fit-margin-bottom="0"
     inkscape:window-width="1680"
     inkscape:window-height="957"
     inkscape:window-x="0"
     inkscape:window-y="1"
     inkscape:window-maximized="1" />
  <metadata
     id="metadata3329">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title></dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     inkscape:label="Layer 1"
     inkscape:groupmode="layer"
     id="layer1"
     transform="translate(-102.46934,-51.731249)">
    <g
       transform="matrix(2.9375,0,0,2.9375,344.11257,-71.534878)"
       inkscape:label="tmp"
       id="layer2"
       style="fill:#000000;fill-opacity:1">
      <g
         style="display:inline;fill:#000000;fill-opacity:1"
         transform="matrix(0.83116869,0,0,0.83116885,193.24183,4.455589)"
         id="g5764"
         inkscape:export-filename="/Users/<USER>/Documents/C5/design/icons/api.png"
         inkscape:export-xdpi="89.941704"
         inkscape:export-ydpi="89.941704">
        <path
           style="fill:#000000;fill-opacity:1;stroke:none;stroke-width:0.936625;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
           d="m -326.92369,45.126027 c -0.0789,0 -0.14264,0.06373 -0.14264,0.142626 v 1.111562 a 3.9026041,3.9026041 0 0 0 -1.52651,0.635619 l -0.78859,-0.788582 c -0.0558,-0.05579 -0.14576,-0.05579 -0.20154,0 l -0.78084,0.78083 c -0.0558,0.05579 -0.0558,0.145751 0,0.201538 l 0.786,0.785998 a 3.9026041,3.9026041 0 0 0 -0.63098,1.529107 h -1.1136 c -0.0789,0 -0.14264,0.06373 -0.14264,0.142626 v 1.10381 c 0,0.07889 0.0637,0.142626 0.14264,0.142626 h 1.11154 a 3.9026041,3.9026041 0 0 0 0.63564,1.526522 l -0.7886,0.788582 c -0.0558,0.05579 -0.0558,0.145751 0,0.201539 l 0.78084,0.78083 c 0.0558,0.05579 0.14576,0.05579 0.20154,0 l 0.78599,-0.785998 a 3.9026041,3.9026041 0 0 0 1.52911,0.630968 v 1.113629 c 0,0.07889 0.0637,0.142626 0.14264,0.142626 h 1.10379 c 0.0789,0 0.14263,-0.06373 0.14263,-0.142626 v -1.11156 a 3.9026041,3.9026041 0 0 0 1.52652,-0.635622 l 0.78859,0.788583 c 0.0558,0.05579 0.14576,0.05579 0.20153,0 l 0.78084,-0.78083 c 0.0558,-0.05579 0.0558,-0.145751 0,-0.201539 l -0.786,-0.785997 a 3.9026041,3.9026041 0 0 0 0.63098,-1.529107 h 1.1136 c 0.0789,0 0.14264,-0.06373 0.14264,-0.142626 v -1.10381 c 0,-0.07889 -0.0637,-0.142626 -0.14264,-0.142626 h -1.11154 a 3.9026041,3.9026041 0 0 0 -0.63563,-1.526522 l 0.78859,-0.788583 c 0.0558,-0.05579 0.0558,-0.145751 0,-0.201538 l -0.78084,-0.78083 c -0.0558,-0.05579 -0.14576,-0.05579 -0.20153,0 l -0.786,0.785997 a 3.9026041,3.9026041 0 0 0 -1.52911,-0.630967 v -1.113629 c 0,-0.07889 -0.0637,-0.142626 -0.14263,-0.142626 z m 0.55189,2.081527 a 3.0119976,3.0119976 0 0 1 3.01223,3.011702 3.0119976,3.0119976 0 0 1 -3.01223,3.01222 3.0119976,3.0119976 0 0 1 -3.01223,-3.01222 3.0119976,3.0119976 0 0 1 3.01223,-3.011702 z"
           id="path5728"
           inkscape:connector-curvature="0" />
        <g
           id="g5762"
           transform="matrix(0.01024417,0,0,0.01024417,-328.3413,48.178117)"
           style="fill:#000000;fill-opacity:1">
          <path
             id="path5730"
             d="m 364.427,259.214 c -14.051,-14.052 -32.733,-21.79 -52.605,-21.79 -10.619,0 -20.888,2.23 -30.302,6.431 -1.076,-1.701 -2.341,-3.298 -3.799,-4.756 l -54.188,-54.188 95.825,-95.825 c 2.645,2.529 5.363,3.085 7.197,3.085 4.06,0 6.8,-2.535 7.308,-3.042 L 356.06,66.932 c 3.771,-3.771 5.563,-7.434 5.477,-11.197 -0.135,-5.897 -4.599,-9.968 -9.767,-14.683 -1.53,-1.395 -3.111,-2.838 -4.7,-4.426 -1.588,-1.588 -3.03,-3.169 -4.426,-4.699 -4.782,-5.243 -8.912,-9.771 -14.939,-9.771 -3.676,0 -7.255,1.793 -10.941,5.48 l -22.281,22.281 c -2.746,2.746 -5.103,9.066 0.122,14.426 l -95.821,95.821 -56.131,-56.131 c 4.012,-9.232 6.137,-19.269 6.137,-29.638 0,-19.872 -7.739,-38.555 -21.791,-52.607 C 112.95,7.737 94.27,0 74.4,0 67.768,0 61.168,0.881 54.785,2.62 c -2.513,0.684 -4.569,2.752 -5.238,5.268 -0.683,2.565 0.096,5.206 2.121,7.232 0.266,0.267 26.668,26.873 35.46,35.665 1.31,1.31 1.193,4.015 1.058,4.81 l -0.069,0.489 c -1.005,10.964 -3.034,24.215 -4.565,27.493 -3.303,1.581 -16.767,3.637 -27.911,4.633 l -0.149,-0.013 -0.302,0.072 c -0.082,0.009 -0.26,0.024 -0.508,0.024 -1.253,0 -3.096,-0.349 -4.758,-2.011 C 40.773,77.13 15.387,51.932 15.145,51.692 13.085,49.632 10.998,49.2 9.609,49.2 6.384,49.2 3.497,51.549 2.587,54.913 -4.357,80.592 3,108.214 21.786,127 c 14.051,14.051 32.733,21.79 52.606,21.79 10.369,0 20.407,-2.126 29.639,-6.137 l 56.131,56.131 -22.013,22.013 c -3.536,-1.853 -7.497,-2.839 -11.618,-2.839 -6.693,0 -12.972,2.592 -17.678,7.298 l -12.282,12.283 c -1.175,-0.166 -2.369,-0.254 -3.578,-0.254 -6.692,0 -12.971,2.592 -17.677,7.298 l -64.351,64.35 c -4.707,4.706 -7.299,10.984 -7.299,17.678 0,6.693 2.592,12.971 7.299,17.678 l 28.44,28.44 c 4.706,4.706 10.984,7.298 17.678,7.298 6.692,0 12.971,-2.592 17.677,-7.298 l 64.35,-64.35 c 4.707,-4.706 7.299,-10.984 7.299,-17.678 0,-1.209 -0.087,-2.404 -0.254,-3.579 l 12.282,-12.282 c 4.707,-4.707 7.299,-10.984 7.299,-17.678 0,-4.121 -0.986,-8.082 -2.839,-11.618 l 22.013,-22.013 54.188,54.188 c 1.458,1.457 3.055,2.723 4.755,3.798 -4.201,9.414 -6.431,19.684 -6.431,30.302 0,19.873 7.739,38.555 21.791,52.607 14.048,14.048 32.729,21.785 52.6,21.786 0.001,0 0.001,0 0.003,0 6.63,0 13.23,-0.882 19.614,-2.62 2.513,-0.684 4.568,-2.752 5.236,-5.268 0.682,-2.565 -0.097,-5.206 -2.122,-7.23 -0.266,-0.267 -26.667,-26.874 -35.459,-35.666 -1.31,-1.31 -1.193,-4.015 -1.058,-4.811 l 0.069,-0.489 c 1.005,-10.964 3.034,-24.214 4.565,-27.493 3.303,-1.581 16.767,-3.637 27.911,-4.633 l 0.149,0.013 0.301,-0.072 c 0.083,-0.009 0.261,-0.024 0.508,-0.024 1.253,0 3.097,0.349 4.76,2.012 9.15,9.151 34.536,34.349 34.778,34.589 2.06,2.06 4.147,2.493 5.536,2.493 3.225,0 6.112,-2.349 7.023,-5.713 6.945,-25.677 -0.413,-53.299 -19.2,-72.086 z m -278.221,70.499 -7.226,7.226 c -7.778,7.778 -20.506,7.778 -28.284,0 l -3.94,-3.941 c -7.778,-7.778 -7.778,-20.506 0,-28.284 l 7.226,-7.226 c 7.778,-7.778 20.506,-7.778 28.284,0 l 3.941,3.941 c 7.777,7.778 7.777,20.506 -10e-4,28.284 z m 159.465,-84.042 c -1.465,1.464 -3.385,2.197 -5.304,2.197 -1.92,0 -3.839,-0.732 -5.304,-2.197 l -88.414,-88.414 c -2.929,-2.929 -2.929,-7.678 0,-10.607 2.931,-2.93 7.679,-2.929 10.607,0 l 27.519,27.519 v 0 l 24.749,24.749 v 0 l 36.146,36.147 c 2.93,2.929 2.93,7.678 0.001,10.606 z"
             inkscape:connector-curvature="0"
             style="fill:#000000;fill-opacity:1" />
          <g
             id="g5732"
             style="fill:#000000;fill-opacity:1" />
          <g
             id="g5734"
             style="fill:#000000;fill-opacity:1" />
          <g
             id="g5736"
             style="fill:#000000;fill-opacity:1" />
          <g
             id="g5738"
             style="fill:#000000;fill-opacity:1" />
          <g
             id="g5740"
             style="fill:#000000;fill-opacity:1" />
          <g
             id="g5742"
             style="fill:#000000;fill-opacity:1" />
          <g
             id="g5744"
             style="fill:#000000;fill-opacity:1" />
          <g
             id="g5746"
             style="fill:#000000;fill-opacity:1" />
          <g
             id="g5748"
             style="fill:#000000;fill-opacity:1" />
          <g
             id="g5750"
             style="fill:#000000;fill-opacity:1" />
          <g
             id="g5752"
             style="fill:#000000;fill-opacity:1" />
          <g
             id="g5754"
             style="fill:#000000;fill-opacity:1" />
          <g
             id="g5756"
             style="fill:#000000;fill-opacity:1" />
          <g
             id="g5758"
             style="fill:#000000;fill-opacity:1" />
          <g
             id="g5760"
             style="fill:#000000;fill-opacity:1" />
        </g>
      </g>
    </g>
    <rect
       style="opacity:1;fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:1.22706378;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:0.66666667;paint-order:stroke markers fill"
       id="rect9687"
       width="2.412755"
       height="4.0383749"
       x="113.81531"
       y="71.049309"
       ry="0" />
    <path
       inkscape:connector-curvature="0"
       d="m 109.35199,76.602082 h 11.10553 l -5.55277,-9.591146 z m 6.05756,-1.514396 h -1.00959 v -1.009589 h 1.00959 z m 0,-2.019184 h -1.00959 v -2.019191 h 1.00959 z"
       id="path9671"
       style="fill:#000000;fill-opacity:1;stroke-width:0.50479716" />
  </g>
  <style
     id="style4833"
     type="text/css">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     id="style4833-0"
     type="text/css">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     type="text/css"
     id="style4833-9">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     type="text/css"
     id="style4833-0-6">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     type="text/css"
     id="style4833-1">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     type="text/css"
     id="style4833-0-0">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     id="style4833-9-3"
     type="text/css">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     id="style4833-0-6-0"
     type="text/css">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     id="style4833-2"
     type="text/css">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     id="style4833-0-2"
     type="text/css">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     type="text/css"
     id="style4833-9-2">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     type="text/css"
     id="style4833-0-6-8">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     id="style4833-94"
     type="text/css">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     id="style4833-0-67"
     type="text/css">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     type="text/css"
     id="style4833-9-20">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     type="text/css"
     id="style4833-0-6-6">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     id="style4833-4"
     type="text/css">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     id="style4833-0-25"
     type="text/css">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     type="text/css"
     id="style4833-9-4">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     type="text/css"
     id="style4833-0-6-3">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     id="style4833-5"
     type="text/css">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     id="style4833-0-9"
     type="text/css">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     type="text/css"
     id="style4833-9-0">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     type="text/css"
     id="style4833-0-6-80">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     id="style4833-00"
     type="text/css">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     id="style4833-0-3"
     type="text/css">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     type="text/css"
     id="style4833-9-27">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     type="text/css"
     id="style4833-0-6-5">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     id="style4833-53"
     type="text/css">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     id="style4833-0-24"
     type="text/css">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     type="text/css"
     id="style4833-9-26">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     type="text/css"
     id="style4833-0-6-2">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     id="style4833-44"
     type="text/css">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     id="style4833-0-38"
     type="text/css">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     type="text/css"
     id="style4833-9-22">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
  <style
     type="text/css"
     id="style4833-0-6-64">
	.st0{display:none;fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st1{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st2{fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st3{fill:none;stroke:#1D56A0;stroke-width:0.75;}
	.st4{clip-path:url(#SVGID_2_);fill:#FFFFFF;stroke:#1D56A0;stroke-width:0.75;}
	.st5{fill:none;stroke:#1D56A0;stroke-width:0.75;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;stroke:#E73847;stroke-width:0.75;stroke-miterlimit:10;}
</style>
</svg>
