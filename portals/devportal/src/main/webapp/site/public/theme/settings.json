{"app": {"context": "/devportal", "customUrl": {"enabled": false, "forwardedHeader": "X-Forwarded-For"}, "origin": {"host": "localhost"}, "subscriptionLimit": 1000, "subscribeApplicationLimit": 5000, "isPassive": true, "singleLogout": {"enabled": true, "timeout": 4000}, "logoutSessionStateAppender": "OIDC", "propertyDisplaySuffix": "__display", "markdown": {"skipHtml": true, "syntaxHighlighterProps": {"showLineNumbers": false}, "syntaxHighlighterDarkTheme": false}, "sanitizeHtml": {"allowedTags": false, "allowedAttributes": false}, "reactHTMLParser": {"decodeEntries": true, "tagsNotAllowed": []}}, "grantTypes": {"authorization_code": "Code", "implicit": "Implicit", "refresh_token": "Refresh <PERSON>", "password": "Password", "iwa:ntlm": "IWA-NTLM", "client_credentials": "Client Credentials", "urn:ietf:params:oauth:grant-type:saml2-bearer": "SAML2", "urn:ietf:params:oauth:grant-type:jwt-bearer": "JWT", "kerberos": "<PERSON><PERSON><PERSON>", "urn:ietf:params:oauth:grant-type:device_code": "Device Code"}, "passwordChange": {"guidelinesEnabled": false, "policyList": ["Policy 1", "Policy 2", "Policy 3"]}, "apis": {"tileDisplayInfo": {"showMonetizedState": false, "showBusinessDetails": false, "showTechnicalDetails": false}}}