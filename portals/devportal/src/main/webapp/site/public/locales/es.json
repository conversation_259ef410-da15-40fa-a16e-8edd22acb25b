{"Apis.Details.ApiConsole.ApiConsole.title": "Poner a prueba o probar", "Apis.Details.ApiConsole.SelectAppPanel.applications": "Aplicaciones", "Apis.Details.ApiConsole.SelectAppPanel.sandbox.radio": "<PERSON><PERSON><PERSON>", "Apis.Details.Comments.CommentAdd.btn.add.comment": "Agregar comentario", "Apis.Details.Comments.CommentAdd.btn.cancel": "<PERSON><PERSON><PERSON>", "Apis.Details.ApiConsole.SelectAppPanel.production.radio": "Producción", "Apis.Details.Comments.CommentEdit.btn.cancel": "<PERSON><PERSON><PERSON>", "Apis.Details.Comments.CommentEdit.btn.save": "<PERSON><PERSON>", "Apis.Details.ApiConsole.security.scheme.apikey": "Clave API", "Apis.Details.ApiConsole.environment": "Ambiente", "Apis.Details.Comments.CommentAdd.write.comment.label": "Escribir un comentario", "Apis.Details.Comments.CommentAdd.write.comment.help": "Escribir un comentario", "Apis.Details.Comments.CommentEdit.general": "General", "Apis.Details.ApiConsole.security.scheme.basic": "Básico", "Apis.Details.Comments.CommentEdit.feature.request": "Solicitud de función", "Apis.Details.Comments.CommentEdit.bug.report": "Informe de error", "Apis.Details.Comments.CommentOptions.delete": "Eliminar", "Apis.Details.Comments.no.comments": "Sin comentarios aún", "Apis.Details.ApiConsole.SelectAppPanel.select.subscribed.application": "Aplicaciones suscritas", "Apis.Details.ApiConsole.SelectAppPanel.environment": "Por favor seleccione un ambiente", "Apis.Details.ApiCOnsole.generate.test.key": "OBTENER CLAVE DE PRUEBA", "Apis.Details.Credentials.Credentials.api.credentials.subscribed.apps.name": "Nombre de la aplicación", "Apis.Details.ApiConsole.ApiConsole.subscribe.to.application": "Por favor suscríbase a una aplicación", "Apis.Details.ApiConsole.SelectAppPanel.keyManagers": "Gerentes clave", "Apis.Details.Comments.CommentEdit.write.a.comment": "Escribir un comentario", "Apis.Details.Credentials.Credentials.api.credentials.subscribed.apps.status": "Estado de la aplicación", "Apis.Details.Credentials.Credentials.subscribe.to.application": "Suscribir", "Apis.Details.Credentials.SubscibeButtonPanel.subscribe.btn": "Suscribir", "Apis.Details.Credentials.Credentials.generate": "Suscribir", "Apis.Details.APIConsole.APIConsole.download.swagger": "Swagger (/swagger.json)", "Apis.Details.Comments.title": "Comentarios", "Apis.Details.ApiConsole.SelectAppPanel.select.key.type.heading": "<PERSON><PERSON><PERSON> clave", "Apis.Details.Comments.showing.comments": "Mostrando comentarios", "Apis.Details.ApiConsole.security.scheme.oauth": "OAuth", "Apis.Details.Comments.load.previous.comments": "Cargar comentarios anteriores", "Apis.Details.ApiConsole.SelectAppPanel.select.registered.keyManagers": "Gerentes clave registrados", "Apis.Details.Credentials.Credentials.api.credentials": "Suscripciones", "Apis.Details.Credentials.Credentials.api.credentials.subscribed.apps.title": "Suscripciones", "Apis.Details.Credentials.Credentials.api.credentials.subscribed.apps.tier": "Nivel de estrangulamiento", "Apis.Details.Creadentials.credetials.mutualssl": "No se requiere suscripción para API SSL mutuas o API con solo autenticación básica.", "Apis.Details.Comments.CommentEdit.blank.comment.error": "No puedes ingresar un comentario en blanco", "Apis.Details.Comments.CommentAdd.error.blank.comment": "No puedes ingresar un comentario en blanco", "Apis.Details.Credentials.Credentials.api.credentials.with.subscribe.message": "Suscríbase a una aplicación y genere credenciales", "Apis.Details.Comments.no.comments.content": "Aún no hay comentarios disponibles para esta API", "Apis.Details.Comments.CommentEdit.something.went.wrong": "Algo salió mal al agregar el comentario", "Apis.Details.Comments.CommentAdd.something.went.wrong": "Algo salió mal al agregar el comentario", "Apis.Details.Credentials.Credentials.api.credentials.generate": "Asistente de suscripción y generación de claves", "Apis.Details.Credentials.Credentials.": "Una aplicación\n                                        se utiliza principalmente para desacoplar al consumidor de las API. Te permite\n                                        generar y usar una sola clave para múltiples API y suscribirse varias veces a\n                                        Una única API con diferentes niveles de SLA.", "Apis.Details.Credentials.Wizard.GenerateKeysStep.list.grantTypes": "Tipos de subvención", "Apis.Details.Credentials.Wizard.CreateAppStep.application.name.is.required": "Se requiere el nombre de la aplicación", "Apis.Details.Credentials.Wizard.CreateAppStep.error.while.creating.the.application": "Error al crear la aplicación", "Apis.Details.Credentials.SubscriptionTableRow.prod.keys": "TECLAS DE PROD", "Apis.Details.Credentials.SubscriptionTableRow.manage.app": "ADMINISTRAR APLICACIÓN", "Apis.Details.Credentials.Credentials.subscribe.to.application.sign.in": "Inicia sesión para suscribirte", "Apis.Details.Credentials.Credentials.subscribed.successfully": "Suscrito exitosamente", "Apis.Details.Credentials.Wizard.GenerateKeysStep.error.keymanager": "Error al seleccionar el administrador de claves", "Apis.Details.Credentials.SubscriptionTableRow.unsubscribe": "SUSCRIBIRSE", "Apis.Details.Credentials.Credentials.subscription.deleted.successfully": "Suscripción eliminada con éxito!", "Apis.Details.Credentials.Credentials.api.credentials.subscribed.apps.description": "(Aplicaciones suscritas a esta API)", "Apis.Details.Credentials.SubscibeButtonPanel.subscribe.wizard.with.new.app": "Asistente de suscripción y generación de claves", "Apis.Details.Credentials.Wizard.GenerateKeysStep.key.configuration.help": "Estas configuraciones se establecen para el propósito del asistente. Usted tiene más control sobre ellas cuando va a la vista de la aplicación.", "Apis.Details.Credentials.Wizard.Wizard.finish": "Terminar", "Apis.Details.Credentials.Credentials.something.went.wrong.with.subscription": "¡Algo salió mal al eliminar la suscripción!", "Apis.Details.Credentials.Credentials.api.credentials.with.wizard.message": "Utilice el Asistente de suscripción y generación de claves. Cree una nueva aplicación -> Suscribirse -> Genere claves y Token de acceso para invocar esta API.", "Apis.Details.Credentials.Credentials.subscribe.to.application.msg": "Debe suscribirse a una aplicación para acceder a esta API", "Apis.Details.Credentials.Credentials.you.do.not.need.credentials.to.access.prototyped.api": "No necesita credenciales para acceder a las API de prototipos", "Apis.Details.Credentials.Wizard.Wizard.generate.keys": "<PERSON><PERSON> claves", "Apis.Details.Credentials.Wizard.Wizard.approval.request.for.this.step.has": "Se ha enviado una solicitud para registrar este paso.", "Apis.Details.Credentials.Wizard.GenerateAccessTokenStep": "Generar toke de acceso para el entorno {keyType}", "Apis.Details.Credentials.Wizard.GenerateKeysStep.list.environment": "Ambiente", "Apis.Details.Credentials.Wizard.GenerateKeysStep.config.km.name": "<PERSON><PERSON><PERSON> de <PERSON>lav<PERSON>", "Apis.Details.Credentials.Wizard.Wizard.next": "próximo", "Apis.Details.Credentials.Wizard.Wizard.Cancel": "CANCELAR", "Apis.Details.Credentials.Wizard.Wizard.rest": "Reiniciar", "Apis.Details.Credentials.Wizard.SubscribeToAppStep.subscribed.successfully": "Suscrito exitosamente", "Apis.Details.Credentials.Wizard.Wizard.test": "Prueba", "Apis.Details.Credentials.Wizard.GenerateKeysStep.key.configuration": "Configuración clave", "Apis.Details.Credentials.SubscriptionTableRow.sandbox.keys": "LLAVES DE CAJA DE ARENA", "Apis.Details.Documents.View.btn.download": "<PERSON><PERSON><PERSON>", "Apis.Details.Documents.Documentation.hide": "ESCONDER", "Apis.Details.GraphQLConsole.GraphQLConsole.title": "Poner a prueba o probar", "Apis.Details.Documents.Documentation.show": "ESPECTÁCULO", "Apis.Details.Credentials.Wizard.GenerateKeysStep.list.userInfoEndpoint": "Punto final de información del usuario", "Apis.Details.Credentials.Wizard.Wizard.generate.access.token": "Generar token de acceso", "Apis.Details.Documents.Documentation.title": "Documentación API", "Apis.Details.Credentials.Wizard.Wizard.subscribe.to.new.application": "Suscríbase a la nueva aplicación", "Apis.Details.Credentials.Wizard.GenerateKeysStep.list.tokenEndpoint": "Punto final de token", "Apis.Details.Credentials.Wizard.Wizard.create": "Crear aplicación", "Apis.Details.Credentials.Wizard.Wizard.copy.access.token": "Copiar token de acceso", "Apis.Details.Credentials.Wizard.GenerateKeysStep.list.revokeEndpoint": "Revocar punto final", "Apis.Details.Environments.download.swagger": "<PERSON><PERSON><PERSON><PERSON>", "Apis.Details.Documents.Documentation.no.docs.content": "No hay documentos disponibles para esta API", "Apis.Details.Documents.View.file.availability": "No hay ningún archivo disponible", "Apis.Details.Environments.copy.to.clipboard": "Copiar al portapapeles", "Apis.Details.Environments.copied": "Copiado", "Apis.Details.Environments.download.swagger.error": "Error al descargar el Swagger", "Apis.Details.Documents.Documentation.no.docs": "No hay documentos disponibles", "Apis.Details.Documents.View.error.downloading": "Error al descargar el archivo", "Apis.Details.Environments.download.wsdl.error": "Error al descargar el Swagger", "Apis.Details.Environments.download.graphql.error": "Error al descargar el GraphQL", "Apis.Details.GraphQLConsole.GraphQLUI.URLs": "URL de puerta de enlace", "Apis.Details.Environments.download.wsdl": "WSDL", "Apis.Details.InfoBar.download.Schema": "<PERSON><PERSON><PERSON>", "Apis.Details.InfoBar.gateway.urls": "URL de puerta de enlace", "Apis.Details.GraphQLConsole.QueryComplexityView.title": "Valores de complejidad personalizados", "Apis.Details.InfoBar.provider": "<PERSON><PERSON><PERSON><PERSON>", "Apis.Details.InfoBar.list.tags": "Etiquetas", "Apis.Details.InfoBar.graphQL.schema": "Esquema GraphQL", "Apis.Details.InfoBar.default.gateway.urls": "URL de puerta de enlace predeterminadas", "Apis.Details.InfoBar.available.mgLabels": "Microgateways disponibles", "Apis.Details.InfoBar.keyManagers": "Gerentes clave", "Apis.Details.InfoBar.less": "MENOS", "Apis.Details.InfoBar.owner": "Propietario", "Apis.Details.InfoBar.list.context": "Contexto", "Apis.Details.InfoBar.list.context.rating": "Clasificación", "Apis.Details.Overview.comments.title": "Comentarios", "Apis.Details.InfoBar.gateway.environments": "Entornos de puerta de enlace", "Apis.Details.Overview.documents.list.title.prefix": "Demostración", "Apis.Details.InfoBar.more": "MÁS", "Apis.Details.Overview.api.subscriptions": "Suscripciones", "Apis.Details.InfoBar.technical": "Propietario técnico", "Apis.Details.InfoBar.ingress.urls": "URL de acceso de Kubernetes", "Apis.Details.InfoBar.list.version": "Versión", "Apis.Details.Labels.copy.to.clipboard": "Copiar al portapapeles", "Apis.Details.Operations.notFound": "Operaciones no encontradas", "Apis.Details.Overview.comments.no.content": "Sin comentarios aún", "Apis.Details.Labels.copied": "Copiado", "Apis.Details.Overview.documents.no.content": "No hay documentos disponibles", "Apis.Details.Overview.mutualssl.basicauth": "No se requiere suscripción para API SSL mutuas o API con solo autenticación básica.", "Apis.Details.Overview.documents.title": "Documentos", "Apis.Details.Overview.documents.list.title.sufix.documents": " Documentos", "Apis.Details.Overview.comments.show.more": "Mostrar más >>", "Apis.Details.InfoBar.microgateway.urls": "URL de Microgateway", "Apis.Details.Overview.no.subscription.message": "No se permiten suscripciones", "Apis.Details.Overview.resources.show.more": "Prueba >>", "Apis.Details.Overview.operations.title": "Operaciones", "Apis.Details.Overview.sdk.generation.show.more": "Mostrar más >>", "Apis.Details.Overview.sdk.generation.title": "SDK Generation", "Apis.Details.Overview.subscribe.count.zero": "No hay suscripciones de aplicaciones.", "Apis.Details.Overview.subscribebtn.link": "Suscribir", "Apis.Details.index.documentation": "Documentación", "Apis.Details.Overview.subscriptions.title": "Suscripciones", "Apis.Details.Overview.resources.title": "Recursos", "Apis.Details.Overview.documents.list.title.sufix.documents.multiple": " Documentos fuera de", "Apis.Details.Sdk.search.sdk": "Buscar SDK", "Apis.Details.Sdk.no.sdks.content": "No hay SDK disponibles para esta API", "Apis.Details.Overview.subscribe.count.singular": "Solicitud suscrita.", "Apis.Details.Overview.subscribe.count.plural": "Aplicaciones suscritas.", "Apis.Details.Sdk.no.sdks": "Sin SDK", "Apis.Details.Overview.sdk.generation.description": "Si quieres crear una aplicación de software\n                                                     para consumir las API suscritas, puede generar el lado del cliente\n                                                      SDK para un lenguaje / marco compatible y úselo como inicio\n                                                       apunte a escribir la aplicación de software.", "Apis.Details.index.all.apis": "TODAS las API", "Apis.Details.Social.EmbadCode": "Embad", "Apis.Details.Sdk.title": "Kits de desarrollo de software (SDK)", "Apis.Details.index.try.out": "Poner a prueba o probar", "Apis.Details.Overview.subscribe.info": "La suscripción le permite recibir tokens de acceso y autenticarse para invocar esta API.", "Apis.Details.index.comments": "Comentarios", "Apis.Details.Overview.signin.subscribe.btn.link": "Inicia sesión para suscribirte", "Apis.Listing.APIList.id": "Carné de identidad", "Apis.Listing.APIList.name": "Nombre", "Apis.Details.index.invalid.tenant.domain": "Dominio de inquilino no válido", "Apis.Listing.APIList.policy": "Política", "Apis.Details.index.overview": "Visión general", "Apis.Listing.ApiTableView.rating": "Clasificación", "Apis.Listing.ApiTableView.type": "Tipo", "Apis.Listing.APICardView.already.subscribed": "Suscrito", "Apis.Details.index.subscriptions": "Suscripciones", "Apis.Details.TryOutConsole.access.token.tooltip": "Puede usar su token de acceso existente o puede generar una nueva clave de prueba.", "Apis.Details.index.sdk": "SDK", "Apis.Listing.ApiTableView.context": "Contexto", "Apis.Listing.ApiTableView.provider": "<PERSON><PERSON><PERSON><PERSON>", "Apis.Listing.ApiTableView.version": "Versión", "Apis.Listing.ApiTagCloud.title": "Etiquetas", "Apis.Listing.ApiTableView.error.loading": "Error al cargar las API", "Apis.Listing.ApiThumb.by": "Por", "Apis.Listing.ApiTableView.invalid.tenant.domain": "Dominio de inquilino no válido", "Apis.Listing.ApiTableView.name": "Nombre", "Apis.Listing.ApiBreadcrumbs.apigroups.main": "Grupos API", "Apis.Listing.ApiThumb.context": "Contexto", "Apis.Listing.ApiThumb.version": "Versión", "Apis.Listing.DocThumb.apiVersion": "Versión Api", "Apis.Listing.Recommendations.error.loading": "Error al cargar las API", "Apis.Listing.APIList.version": "Versión", "Apis.Listing.DocThumb.sourceType": "Tipo de fuente:", "Apis.Listing.StarRatingBar.user": "usuario", "Apis.Listing.Recommendations.rating": "Clasificación", "Apis.Listing.ApiThumb.by.colon": " :", "Apis.Listing.Listing.apis.main": "APIs", "Apis.Listing.Recommendations.invalid.tenant.domain": "Dominio de inquilino no válido", "Apis.Listing.SubscriptionPolicySelect.subscribe": "Suscribir", "Apis.Listing.Recommendations.name": "Nombre", "Apis.Listing.NoApi.nodata.content": "No hay API para mostrar en este momento.", "Apis.Listing.DocThumb.apiName": "Nombre de la API", "Apis.Listing.Listing.ApiTagCloud.title": "Etiquetas / Categorías API", "Apis.Listing.CategoryListingCategories.title": "Categorías API", "Apis.Settings.SettingsBase.header": "Configuraciones", "Apis.Listing.NoApi.nodata.title": "No hay API disponibles", "Apis.Listing.TableView.TableView.doc.flag": "[Doc]", "Apis.Listing.CategoryListingCategories.categoriesNotFound": "No se pueden encontrar las categorías.", "Apis.Listing.StarRatingBar.users": "los usuarios", "Apis.Listing.StarRatingBar.not.rated": "No clasificado", "Applications.ApplicationFormHandler.app.updated.success": "Aplicación actualizada con éxito", "Apis.Listing.TagCloudListingTags.tagsNotFound": "No se pueden encontrar grupos de API", "Apis.Listing.TagCloudListingTags.allApis": "Todas las apis", "Apis.Listing.TagCloudListing.apigroups.main": "Grupos API", "Apis.Listing.TagCloudListingTags.title": "Grupos de API", "Apis.Settings.Alerts.connection.error": "No se pudo conectar al servidor de análisis. Por favor verifique la conectividad.", "Applications.Create.ApplicationFormHandler.cancel": "CANCELAR", "Applications.Create.ApplicationFormHandler.Application.created.successfully": "Aplicación creada con éxito.", "Applications.Create.ApplicationFormHandler.create.application.heading": "Crea una aplicación", "Apis.Settings.SettingsBase.sub.header": "Ver y configurar los ajustes del portal para desarrolladores", "Applications.Create.ApplicationFormHandler.error.while.creating.the.application": "Error al crear la aplicación", "Applications.Create.ApplicationFormHandler.app.name.required": "Se requiere el nombre de la aplicación", "Applications.Details.InfoBar.throttling.tier": "Nivel de estrangulamiento", "Applications.Create.ApplicationFormHandler.save": "SALVAR", "Applications.Details.InfoBar.edit.text": "<PERSON><PERSON>", "Applications.Details.InfoBar.edit": "<PERSON><PERSON>", "Applications.Details.InfoBar.delete": "Eliminar", "Applications.Create.ApplicationFormHandler.edit.application.heading": "Editar aplicación", "Applications.Details.Invoice.no.data.available": "Datos no disponibles", "Applications.Details.InfoBar.text": "Eliminar", "Applications.Create.ApplicationFormHandler.create.application.sub.heading": "Cree una aplicación que proporcione parámetros de nombre, cuota y tipo de token. La descripción es opcional.", "Applications.Create.ApplicationFormHandler.edit.application.sub.heading": "Edita esta aplicación. El nombre, la cuota y el tipo de token son parámetros obligatorios y la descripción es opcional", "Applications.Details.InfoBar.application.deleted.successfully": "¡La aplicación {nombre} se eliminó correctamente!", "Applications.Details.Invoice.view.btn": "Mirar la factura", "Applications.Details.Invoice.close": "Cerca", "Applications.Details.InfoBar.subscriptions": "Suscripciones", "Applications.Create.Listing.add.new.application": "Agregar nueva aplicación", "Applications.Details.Invoice.pending.invoice.data": "Datos de factura pendientes no encontrados para esta suscripción.", "Applications.Details.SubscriptionTableData.cancel": "<PERSON><PERSON><PERSON>", "Applications.Details.InfoBar.application.deleting.error": "Error al eliminar la aplicación {nombre}", "Applications.Details.Overview.application.owner": "Propietario de la aplicación", "Applications.Details.InfoBar.listing.resource.not.found": "Recurso no encontrado", "Applications.Details.Overview.description": "Descripción", "Applications.Details.SubscriptionTableData.delete": "Eliminar", "Applications.Details.SubscriptionTableData.update": "Actualizar", "Applications.Details.Subscriptions.Status": "Estado de suscripción", "Applications.Details.SubscriptionTableData.edit.text": "<PERSON><PERSON>", "Applications.Details.SubscriptionTableData.delete.text": "Eliminar", "Applications.Details.SubscriptionTableData.update.subscription": "Actualizar suscripción", "Applications.Details.SubscriptionTableData.delete.subscription.confirmation": "¿Estás seguro de que deseas eliminar la suscripción?", "Applications.Details.SubscriptionTableData.update.throttling.policy.blocked": "La suscripción está en estado BLOQUEADO. Debes desbloquear la suscripción para editar el nivel", "Applications.Details.Overview.token.type": "Tipo de <PERSON>", "Applications.Details.Overview.workflow.status": "Estado del flujo de trabajo", "Applications.Details.Subscriptions\n                                                                        .subscription.state": "Estado del ciclo de vida", "Applications.Details.SubscriptionTableData.update.throttling.policy.name": "Nivel de estrangulamiento", "Applications.Details.SubscriptionTableData.update.throttling.policy": "Nivel de suscripción actual:", "Applications.Details.SubscriptionTableData.update.throttling.policy.tier.update": "Actualización de nivel pendiente:", "Applications.Details.Subscriptions.action": "Acción", "Applications.Details.SubscriptionTableData.update.throttling.policy.rejected": "La suscripción se encuentra actualmente en estado RECHAZADO. Debe obtener la aprobación de la suscripción antes de editar el nivel", "Applications.Details.SubscriptionTableData.update.throttling.policy.onHold": "La suscripción está actualmente en estado ON_HOLD. Debe obtener la aprobación de la suscripción antes de editar el nivel", "Applications.Details.Subscriptions.api.name": "API", "Applications.Details.Subscriptions.filter.msg": "API filtradas para", "Applications.Details.Subscriptions.subscription.management": "Gestión de suscripciones", "Applications.Details.Subscriptions\n                                                                        .subscription.tier": "Nivel de suscripción", "Applications.Details.Subscriptions.error.occurred.during.subscription.not.201": "Se produjo un error durante la suscripción.", "Applications.Details.Subscriptions.search": "API de búsqueda", "Applications.Details.menu.subscriptions": "Suscripciones", "Applications.Details.menu.api.key": "Clave API", "Applications.Details.SubscriptionTableData.update.throttling.policy.helper": "Asigne un nuevo nivel de política de limitación a la suscripción existente", "Applications.Details.api.keys.title": " Clave API", "Applications.Listing.ApplicationTableHead.workflow.status": "Estado del flujo de trabajo", "Applications.Details.Subscriptions.select.a.subscription.policy": "Seleccione una política de suscripción", "Applications.Details.Subscriptions.error.occurred.during.subscription": "Se produjo un error durante la suscripción.", "Applications.Details.menu.oauth.tokens": "Fichas OAuth2", "Applications.Details.Subscriptions.no.subscriptions": "No hay suscripciones disponibles", "Applications.Edit.app.update.error.no.required.attribute": "Por favor complete todos los atributos de aplicación requeridos", "Applications.Listing.ApplicationTableHead.actions": "Comportamiento", "Applications.Details.applications.all": "Todas las aplicaciones", "Applications.Listing.ApplicationTableHead.subscriptions": "Suscripciones", "Applications.Listing.ApplicationTableHead.name": "Nombre", "Applications.Details.Subscriptions.subscription.successful": "Suscripción exitosa", "Applications.Details.oauth2.keys.main.title": " OAuth2 Keys", "Applications.Details.Subscriptions.no.subscriptions.content": "No hay suscripciones disponibles para esta aplicación", "Applications.Details.Subscriptions.filter.msg.all.apis": "Mostrar todas las API", "Applications.Listing.ApplicationTableHead.policy": "Política", "Applications.Listing.AppsTableContent.rejected": "RECHAZADO", "Applications.Listing.DeleteConfirmation.dialog,delete": "Eliminar", "Applications.Details.menu.overview": "Visión general", "Applications.Listing.AppsTableContent.active": "ACTIVO", "Applications.Listing.AppsTableContent.delete.tooltip": "Eliminar", "Applications.Listing.ApplicationTableHead.owner": "Propietario", "Applications.Listing.AppsTableContent.inactive": "INACTIVO", "Applications.Details.Subscriptions.subscription.management.add": "Suscribir API", "Applications.Details.menu.sandbox.keys": "Sandbox Keys", "Applications.Details.menu.prod.keys": "Claves de producción", "Applications.Listing.Listing.applications": "Aplicaciones", "Applications.Listing.Listing.application.deleted.successfully": "¡La aplicación {nombre} se eliminó correctamente!", "Applications.Listing.AppsTableContent.wait.approval": "A la espera de la aprobación", "Applications.Listing.DeleteConfirmation.dialog.cancel": "<PERSON><PERSON><PERSON>", "Base.Errors.ResourceNotFound.applications": "Aplicaciones", "Applications.Listing.Listing.application.deleting.error": "Error al eliminar la aplicación {nombre}", "Applications.Listing.DeleteConfirmation.dialog.title": "Eliminar aplicación", "Applications.Listing.DeleteConfirmation.dialog.text.description": "La aplicación será eliminada", "Applications.Listing.Listing.applications.no.search.results.title": "No hay aplicaciones coincidentes", "Base.Errors.ResourceNotfound.default_tittle": "Página no encontrada", "Base.Errors.SubscriptionNotFound.default_title": "La página de información de Solace no se muestra sin suscripciones a la API. Suscríbase a la API", "Applications.Listing.Listing.clear.search": "<PERSON><PERSON><PERSON>", "Applications.Listing.Listing.noapps.display.link.text": "Agregar nueva aplicación", "Applications.Listing.Listing.applications.search": "Buscar", "Base.Header.GlobalNavbar.menu.home": "<PERSON><PERSON>", "Applications.Listing.Listing.applications.no.search.results.body.prefix": "Revisa la ortografía o intenta", "Applications.Listing.Listing.noapps.display.title": "No hay aplicaciones disponibles", "Base.Errors.ResourceNotFound.api.list": "Lista de API", "Applications.Listing.Listing.applications.no.search.results.body.sufix": "borrar la búsqueda", "Base.Errors.ResourceNotfound.default_body": "La página que estás buscando no está disponible.", "Base.Errors.ResourceNotFound.more.links": "Puedes consultar los enlaces a continuación", "Applications.Listing.Listing.logical.description": "Una aplicación es una colección lógica de API.\n                                        Las aplicaciones le permiten usar un token de acceso único para invocar un\n                                         colección de API y suscribirse a una API varias veces\n                                          y permite acceso ilimitado por defecto.", "Base.Header.headersearch.HeaderSearch.search_api.tooltip": "API de búsqueda", "Base.Header.GlobalNavbar.menu.apis": "APIs", "Base.Header.GlobalNavbar.menu.applications": "Aplicaciones", "Base.Header.headersearch.HeaderSearch.tooltip.option3": "Por versión de API [Sintaxis - versión: xxxx]", "Base.Header.headersearch.HeaderSearch.tooltip.option6": "Por etiquetas [Sintaxis - etiquetas: xxxx]", "Base.Header.headersearch.HeaderSearch.tooltip.option5": "Por descripción [Sintaxis - descripción: xxxx]", "Base.Header.headersearch.SearchUtils.lcState.published": "Producción", "Base.Header.headersearch.HeaderSearch.tooltip.option1": "Por nombre de API [Predeterminado]", "Base.Header.headersearch.HeaderSearch.tooltip.option10": "Por propiedades de API [Sintaxis - nombre_propiedad: valor_propiedad]", "Base.Header.headersearch.HeaderSearch.tooltip.option2": "Por proveedor de API [Sintaxis - proveedor: xxxx]", "Base.Header.headersearch.SearchUtils.lcState.all": "<PERSON><PERSON>", "Base.Header.headersearch.HeaderSearch.tooltip.title": "Opciones de búsqueda", "Base.index.logout": "<PERSON><PERSON><PERSON>", "Base.index.settingsMenu.changePassword": "Cambia la contraseña", "Base.index.sign.in": " Registrarse", "Change.Password.password.length.short": "¡La contraseña es demasiado corta!", "Base.index.banner.alt": "Banner del portal de desarrollo", "Base.index.go.to.public.store": "Ir al portal de desarrollo público", "Base.Header.headersearch.SearchUtils.lcState.prototyped": "Prototipo", "Base.index.settingsMenu.alertConfiguration": "Configu<PERSON> <PERSON>as", "Base.Header.headersearch.HeaderSearch.tooltip.option4": "Por contexto [Sintaxis - contexto: xxxx]", "Base.index.copyright.text": "WSO2 API-M v4.1.0 | © 2022 WSO2 LLC", "Base.Header.headersearch.HeaderSearch.tooltip.option9": "Por etiqueta Microgateway [Sintaxis - etiqueta: xxxx]", "Base.Header.headersearch.HeaderSearch.tooltip.option7": "Por subcontexto [Sintaxis - subcontexto: xxxx]", "Change.Password.current.password.incorrect": "Contraseña actual incorrecta", "Change.Password.password.mismatch": "La contraseña no coincide", "Change.Password.description": "Cambia tu propia contraseña. Los campos obligatorios están marcados con un asterisco ( * )", "Change.Password.password.empty": "La contraseña esta vacía", "Change.Password.password.change.disabled": "Cambio de contraseña deshabilitado", "Change.Password.password.changed.success": "Cambió la contraseña exitosamente", "Change.Password.password.pattern.invalid": "Patrón de contraseña inválido", "Base.index.logo.alt": "Portal de desarrollo", "Change.Password.title": "Cambia la contraseña", "LoginDenied.logout": "<PERSON><PERSON><PERSON>", "Change.Password.password.policy": "Política de contraseñas:", "Change.Password.password.length.long": "¡La contraseña es demasiado larga!", "LandingPage.ApisWithTag.invalid.tenant.domain": "Dominio de inquilino no válido", "LoginDenied.anonymousview": "<PERSON>r al portal público", "Settings.Alert.AlertConfiguration.add": "Nueva configuración", "Settings.Alerts.AlertConfiguration.app.name": "Nombre de la aplicación", "LoginDenied.title": "Error 403 - <PERSON><PERSON><PERSON><PERSON>", "Login.RedirectToLogin.you.will.be.redirected.to": "<PERSON><PERSON> redirigido a {página}", "Settings.Alerts.AlertConfiguration.applications.label": "Solicitud", "Settings.Alerts.Alerts.cancel.btn": "<PERSON><PERSON><PERSON>", "Settings.Alerts.AlertConfiguration.api.name": "Nombre de API", "Settings.Alert.AlertConfiguration.alert.config.add.success.msg": "Configuración de alerta agregada correctamente", "Settings.Alert.AlertConfiguration.alert.config.add.error.msg": "Se produjo un error al agregar la configuración de alerta", "Settings.Alert.AlertConfiguration.alert.config.delete.error.msg": "Se produjo un error al eliminar la configuración.", "Settings.Alert.AlertConfiguration.alert.config.delete.success.msg": "Configuración de alerta eliminada correctamente", "LoginDenied.message": "No tiene privilegios suficientes para acceder al Portal del desarrollador.", "Settings.Alerts.AlertConfiguration.api.name.label": "Nombre de API", "Settings.Alerts.AlertConfiguration.api.version": "Versión API", "Settings.Alerts.AlertConfiguration.api.version.label": "Versión API", "Settings.Alerts.AlertConfiguration.no.config.message": "No tienes ninguna configuración. Haga clic en el botón {newConfig} para agregar una configuración.", "Settings.Alerts.AlertConfiguration.select.api.helper": "Seleccione el nombre de la API", "Settings.Alerts.AlertConfiguration.threshold.value.helper": "Ingrese el recuento de solicitudes.", "Settings.Alerts.AlertConfiguration.select.application.helper": "Seleccionar aplicación", "Settings.Alerts.Alerts.confirm.btn": "Cancelar suscripción a todo", "Settings.Alerts.AlertConfiguration.request.count.label": "Recuento de solicitudes.", "Settings.Alerts.Alerts.abnormal.response.time": "Solicitudes anormales por minuto", "Settings.Alerts.AlertConfiguration.configuration": "Configuraciones de {name}", "Settings.Alerts.Alerts.abnormal.backend.time": "Acceso a recursos anormales", "Settings.Alerts.Alerts.configure.alert": "Configuraciones", "Settings.Alerts.Alerts.close.btn": "Cerca", "Settings.Alerts.Alerts.numusual.ip": "Acceso IP inusual", "Settings.Alerts.AlertConfiguration.select.version.helper": "Seleccionar versión de API", "Settings.Alerts.Alerts.enable.analytics.message": "Habilite Analytics para configurar alertas", "Settings.Alerts.Alerts.abnormal.request.pattern.description": "Esta alerta se activa si hay un cambio en el patrón de acceso a recursos de un usuario de una aplicación en particular. Estas alertas podrían tratarse como una indicación de una actividad sospechosa realizada por un usuario a través de su aplicación.", "Settings.Alerts.Alerts.abnormal.request.per.min.description": "Esta alerta se activa si hay un pico repentino en el recuento de solicitudes dentro de un período de un minuto de forma predeterminada para una API particular para una aplicación. Estas alertas podrían tratarse como una indicación de un posible alto tráfico, actividad sospechosa, posible mal funcionamiento de la aplicación del cliente, etc.", "Settings.Alerts.Alerts.subscribe.to.alerts.heading": "Administrar suscripciones de alertas", "Settings.Alerts.Alerts.frequent.tier": "Go<PERSON>pe de límite de nivel frecuente", "Settings.Alerts.Alerts.loading.error.msg": "Se produjo un error al cargar alertas", "Settings.Alerts.Alerts.unsubscribe.confirm.dialog.heading": "Confirmar baja de todas las alertas", "Settings.Alerts.Alerts.subscribe.error.msg": "Se produjo un error al suscribirse a las alertas.", "Settings.ChangePasswordForm.enter.current.password": "Introducir la contraseña actual", "Settings.Alerts.Alerts.unsubscribe.error.msg": "Se produjo un error al cancelar la suscripción.", "Settings.Alerts.Alerts.subscribe.to.alerts.subheading": "Seleccione los tipos de alerta para suscribirse / cancelar suscripción y haga clic en Guardar.", "Settings.ChangePasswordForm.enter.new.password": "Introduzca una nueva contraseña", "Settings.Alerts.Alerts.subscribe.success.msg": "Suscrito a alertas con éxito.", "Settings.ChangePasswordForm.confirm.new.password": "Confirmar nueva contraseña", "Settings.Alerts.Alerts.unsubscribe.confirm.dialog.message": "Esto eliminará todas las suscripciones de alertas y correos electrónicos existentes. Esta acción no se puede deshacer.", "Settings.ChangePasswordForm.current.password": "contraseña actual", "Settings.ChangePasswordForm.Cancel.Button.text": "<PERSON><PERSON><PERSON>", "Settings.ChangePasswordForm.Save.Button.text": "<PERSON><PERSON>", "Settings.ChangePasswordForm.confirmationOf.new.password": "Confirmación de nueva contraseña", "Shared.AppsAndKeys.ApplicationCreateForm.application.name": "Nombre de la aplicación", "Settings.Alerts.Alerts.unsubscribe.success.msg": "<PERSON><PERSON><PERSON> de todas las alertas con éxito.", "Settings.Alerts.Alerts.tier.limit.hitting.description": "Esta alerta se activa si al menos uno de los dos casos siguientes se cumple. si una aplicación en particular se limita por alcanzar el límite de nivel suscrito de esa aplicación más de 10 veces (por defecto) dentro de una hora (por defecto) o si un usuario particular de una aplicación se limita por alcanzar el límite de nivel suscrito de un API particular más de 10 veces (por defecto) dentro de un día (por defecto)", "Shared.AppsAndKeys.ApplicationCreateForm.my.mobile.application.placeholder": "Mi aplicación móvil", "Shared.AppsAndKeys.ApplicationCreateForm.application.description.label": "Descripción de la aplicación", "Settings.ChangePasswordForm.new.password": "Nueva contraseña", "Shared.AppsAndKeys.ApplicationCreateForm.describe.the.application.help": "Describa la aplicación.", "Shared.AppsAndKeys.ApiKeyManager.generate.key.help": "Use el botón Generar clave para generar un token JWT autónomo.", "Shared.AppsAndKeys.ApplicationCreateForm.assign.api.request": "Asignar cuota de solicitud de API por token de acceso.\n                            La cuota asignada se compartirá entre todos\n                            Las API suscritas de la aplicación.", "Shared.AppsAndKeys.ApplicationCreateForm.add.groups.label": "Grupos de aplicaciones", "Shared.AppsAndKeys.ImportExternalApp.consumer.secret.of.application": "Secreto del consumidor de la aplicación OAuth", "Shared.AppsAndKeys.ImportExternalApp.consumer.key": "Clave del consumidor", "Shared.AppsAndKeys.ImportExternalApp.consumer.key.title": "Clave del consumidor de la aplicación OAuth", "Shared.AppsAndKeys.ImportExternalApp.provide.oauth.button.provide": "Proporcionar", "Shared.AppsAndKeys.ApplicationCreateForm.my.mobile.application": "Mi aplicación", "Shared.AppsAndKeys.ImportExternalApp.key.provide.user.owner": "Solo el propietario puede proporcionar llaves", "Shared.ApiKeyRestriction.key.restrictions": "Restricciones Claves", "Shared.AppsAndKeys.ApplicationCreateForm.enter.a.name": "Ingrese un nombre para identificar la aplicación.\n                                    Podrá elegir esta aplicación al suscribirse a las API", "Shared.AppsAndKeys.ImportExternalApp.cancel": "<PERSON><PERSON><PERSON>", "Settings.Alerts.Alerts.unusual.ip.access.description": "Esta alerta se activa si un usuario modifica un IP de origen de la solicitud para una aplicación en particular o si la solicitud proviene de una IP utilizada antes de un período de tiempo de 30 días (predeterminado). Estas alertas podrían tratarse como una indicación de una actividad sospechosa realizada por un usuario a través de una aplicación.", "Shared.AppsAndKeys.KeyConfCiguration.Invalid.callback.url.error.text": "URL invalida. Por favor introduzca un URL válido.", "Shared.AppsAndKeys.ApplicationCreateForm.per.token.quota": "Por cuota de token.", "Shared.AppsAndKeys.KeyConfiguration.copy.to.clipboard": "Copiar al portapapeles", "Shared.AppsAndKeys.KeyConfCiguration.Invalid.callback.empty.error.text": "La URL de devolución de llamada no puede estar vacía cuando se seleccionan las concesiones implícitas o del código de autorización.", "Shared.AppsAndKeys.KeyConfiguration.token.endpoint.label": "Punto final de token", "Shared.AppsAndKeys.SubscribeToApi.application": "Solicitud", "Shared.AppsAndKeys.ApplicationCreateForm.type.a.group.and.enter": "Escriba un grupo e ingrese", "Shared.AppsAndKeys.ImportExternalApp.provide.\n                                                    oauth.button.update": "Actualizar", "Shared.AppsAndKeys.ImportExternalApp.provide.oauth.button.update": "Actualizar", "Shared.AppsAndKeys.ImportExternalApp.consumer.secret": "Secreto del consumidor", "Shared.AppsAndKeys.KeyConfiguration.revoke.endpoint.label": "Revocar punto final", "Shared.AppsAndKeys.KeyConfiguration.userinfo.endpoint.label": "Punto final de información del usuario", "Shared.AppsAndKeys.KeyConfiguration.grant.types": "Tipos de subvención", "Shared.AppsAndKeys.KeyConfiguration.copied": "Copiado", "Shared.AppsAndKeys.KeyConfiguration.callback.url.label": "URL de devolución de llamada", "Shared.AppsAndKeys.SubscribeToApi.available.policies": "Políticas Disponibles -", "Shared.AppsAndKeys.ImportExternalApp.provide.oauth": "Proporcionar claves OAuth existentes", "Shared.AppsAndKeys.KeyConfiguration.the.application.can": "La aplicación puede usar los siguientes tipos de concesión para generar\n                            Fichas de acceso. Según los requisitos de la aplicación, puede habilitar o deshabilitar\n                            Tipos de concesión para esta aplicación.", "Shared.AppsAndKeys.TokenManager.cleanup": "Limpiar", "Shared.AppsAndKeys.KeyConfCiguration.callback.url.helper.text": "La URL de devolución de llamada es un URI de redireccionamiento en el cliente\n                            aplicación que utiliza el servidor de autorización para enviar el\n                            El agente de usuario del cliente (generalmente el navegador web) regresa después de otorgar acceso.", "Shared.AppsAndKeys.TokenManager.cleanup.text": "¡Error! Tienes claves creadas parcialmente.\n                            Haga clic en el botón \"Limpiar\" e intente nuevamente.", "Shared.AppsAndKeys.TokenManager.key.cleanup.success": "Claves de aplicación limpiadas con éxito", "Shared.AppsAndKeys.KeyConfiguration.url.to.webapp": "http: // url-a-webapp", "Shared.AppsAndKeys.SubscribeToApi.throttling.policy": "Política de estrangulamiento", "Shared.AppsAndKeys.SubscribeToApi.select.an.application.to.subscribe": "Seleccione una aplicación para suscribirse", "Shared.AppsAndKeys.TokenManager.key.and.secret": "Clave y secreto", "Shared.AppsAndKeys.TokenManager.key.configuration": "Configuración clave", "Shared.AppsAndKeys.TokenManager.key.generate.error.callbackempty": "La URL de devolución de llamada no puede estar vacía cuando se seleccionan los tipos de concesión de Código de aplicación o implícito", "Shared.AppsAndKeys.TokenManager.key.provide.success": "Claves de aplicación proporcionadas con éxito", "Shared.AppsAndKeys.TokenManager.key.provide.error": "Se produjo un error al proporcionar claves de aplicación", "Shared.AppsAndKeys.TokenManager.key.cleanup.error": "Se produjo un error al limpiar las claves de la aplicación", "Shared.AppsAndKeys.TokenManager.key.update.error": "Se produjo un error al actualizar las claves de la aplicación", "Shared.AppsAndKeys.TokenManager.key.generate.error": "Se produjo un error al generar claves de aplicación", "Shared.AppsAndKeys.TokenManager.update.configuration": "Configuraciones clave", "Shared.AppsAndKeys.TokenManager.key.update.success": "Claves de aplicación actualizadas con éxito", "Shared.AppsAndKeys.TokenManager.key.generate.success": "Claves de aplicación generadas con éxito", "Shared.AppsAndKeys.TokenManagerSummary": "¡Error! Tienes claves creadas parcialmente. Utilice la opción \"Limpiar\".", "Shared.AppsAndKeys.Tokens.apiKeyRestriction.enter.ip": "Ingrese la dirección IP", "Shared.AppsAndKeys.Tokens.apiKeyRestriction.ip.validity.error": "Dirección IP inválida", "Shared.AppsAndKeys.Tokens.apiKeyRestriction.enter.referer": "Ingrese Http Referer", "Shared.AppsAndKeys.Tokens.apikey": "Periodo de validez de API Key", "Shared.AppsAndKeys.Tokens.apikey.enter.time": "Ingrese el tiempo en segundos", "Shared.AppsAndKeys.Tokens.apiKeyRestriction.referer.validity.error": "Referente HTTP inválido", "Shared.AppsAndKeys.Tokens.apikey.set.validity.error": "Utilice un número válido para el tiempo de vencimiento de la clave API", "Shared.AppsAndKeys.Tokens.apikey.set.validity.help": "Puede establecer un período de vencimiento para determinar el período de validez del token después de la generación. Establezca esto como -1 para asegurarse de que la apikey nunca caduque.", "Shared.AppsAndKeys.ViewCurl.copied": "Copiado", "Shared.AppsAndKeys.Tokens.when.you.generate": "Cuando genera tokens de acceso a API protegidas por ámbito / s, puede seleccionar el / los ámbito / s y luego generar el token para ello. Los ámbitos permiten un control de acceso preciso a los recursos de la API en función de los roles de los usuarios. Define ámbitos a un recurso API. Cuando un usuario invoca la API, su token de portador OAuth 2 no puede otorgar acceso a ningún recurso API más allá de sus ámbitos asociados.", "Shared.AppsAndKeys.ViewCurl.copy.to.clipboard": "Copiar al portapapeles", "Shared.AppsAndKeys.Tokens.when.you.generate.scopes": "Alcances", "Shared.AppsAndKeys.ViewCurl.help": "El siguiente comando cURL muestra cómo generar un token de acceso usando\n                            el tipo de concesión de contraseña.", "Shared.AppsAndKeys.ViewCurl.help.in.a.similar": "De manera similar, puede generar un token de acceso utilizando el\n                    Tipo de concesión de credenciales de cliente con el siguiente comando cURL.", "Shared.AppsAndKeys.ViewKeys.consumer.key": "Clave del consumidor", "Shared.AppsAndKeys.ViewKeys.consumer.secret": "Secreto del consumidor", "Shared.AppsAndKeys.ViewKeys.consumer.generate.btn": "Generar", "Shared.AppsAndKeys.ViewKeys.consumer.close.btn": "Cerca", "Shared.AppsAndKeys.ViewKeys.apiKeyRestriction.ip.example.heading": "Ejemplos de direcciones IP permitidas", "Shared.AppsAndKeys.ViewKeys.access.token": "Token de acceso", "Shared.AppsAndKeys.ViewKeys.consumer.key.title": "Clave del consumidor de la aplicación", "Shared.AppsAndKeys.ViewKeys.consumer.secret.button.regenerate": "Regenerar el secreto del consumidor", "Shared.AppsAndKeys.ViewKeys.apiKeyRestriction.ip.example.content": "Especifique un IPv4 o IPv6 o una subred utilizando la notación CIDR {linebreak} Ejemplos: {ip1}, {ip2}, {ip3} o {ip4}", "Shared.AppsAndKeys.ViewKeys.client.enable.client.credentials": "Habilite el tipo de concesión Credenciales de cliente para generar tokens de acceso de prueba", "Shared.AppsAndKeys.ViewKeys.apiKeyRestriction.referer.example.heading": "Ejemplos de URL permitidas para restringir sitios web", "Shared.AppsAndKeys.ViewKeys.consumer.secret.of.application": "Secreto del consumidor de la aplicación", "Shared.AppsAndKeys.ViewKeys.apiKeyRestriction.ip.example.content.message": "Una URL específica con una ruta exacta: {url1} {linebreak} Cualquier URL en un solo subdominio, usando un asterisco comodín (*): {url2} {linebreak} Cualquier URL de subdominio o ruta en un solo dominio, usando asteriscos comodín (* ): {url3}", "Shared.AppsAndKeys.ViewKeys.generate.access.token": "Generar token de acceso", "Shared.AppsAndKeys.ViewKeys.copied": "Copiado", "Shared.AppsAndKeys.ViewKeys.copy.to": "Copiar al portapapeles", "Shared.AppsAndKeys.ViewSecret.consumer.secret": "Secreto del consumidor", "Shared.AppsAndKeys.ViewToken.access.token": "Token de acceso", "Shared.AppsAndKeys.ViewToken.apikey": "Clave API", "Shared.AppsAndKeys.ViewToken.info.second": " segundos", "Shared.AppsAndKeys.ViewKeys.curl.to.generate": "CURL para generar token de acceso", "Shared.AppsAndKeys.ViewToken.please.copy": "Copie el token de acceso", "Shared.AppsAndKeys.ViewKeys.key.secret.title": "Clave y secreto no se genera para esta aplicación", "Shared.AppsAndKeys.ViewSecret.please.copy.secret": "Copie el secreto del consumidor", "Shared.AppsAndKeys.ViewToken.info.third": " y el token tiene (", "Shared.AppsAndKeys.ViewToken.info.fourth": ") ámb<PERSON>s", "Shared.AppsAndKeys.ViewSecret.please.copy.secret.help": "Por favor tome nota del consumidor regenerado\n                            valor secreto ya que se mostrará solo una vez.", "Shared.AppsAndKeys.ViewToken.info.first": "El token anterior tiene un período de validez de", "Shared.AppsAndKeys.ViewToken.please.copy.apikey": "Por favor copie la clave API", "Shared.AppsAndKeys.ViewToken.please.copy.help": "Copie este valor de token generado, ya que se mostrará solo para la sesión actual del navegador. (El token no será visible en la interfaz de usuario después de actualizar la página).", "Shared.ConfirmDialog.cancel": "<PERSON><PERSON><PERSON>", "Shared.ConfirmDialog.ok": "Okay", "Shared.AppsAndKeys.WaitingForApproval.msg.reject": "Esta aplicación ha sido rechazada de generar claves", "Shared.AppsAndKeys.WaitingForApproval.msg.ok": "Se ha enviado una solicitud para registrar esta solicitud y está pendiente de aprobación.", "Shared.ConfirmDialog.please.confirm.sure": "¿Estás seguro?", "api.console.security.type.heading": "Tipo de seguridad", "Shared.ConfirmDialog.please.confirm": "Por favor confirmar", "api.console.gateway.heading": "<PERSON><PERSON><PERSON>", "notice": "aviso", "api.console.security.heading": "Seguridad", "TenantListing.title": "Portales de desarrolladores de inquilinos", "password": "Contraseña", "api.console.require.access.token": "Necesita un token de acceso para probar la API. Inicie sesión y suscríbase a la API para generar un token de acceso. Si ya tiene un token de acceso, proporciónelo a continuación.", "username": "Nombre de usuario"}