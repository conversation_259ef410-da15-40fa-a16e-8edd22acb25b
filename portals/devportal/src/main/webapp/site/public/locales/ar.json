{"Apis.Details.ApiCOnsole.generate.test.key": "احصل على مفتاح الاختبار", "Apis.Details.ApiConsole.ApiConsole.title": "جرب أو حاول", "Apis.Details.ApiConsole.SelectAppPanel.select.registered.keyManagers": "مدراء المفاتيح المسجلين", "Apis.Details.APIConsole.APIConsole.download.swagger": "Swagger (/swagger.json)", "Apis.Details.ApiConsole.SelectAppPanel.select.key.type.heading": "نوع المفتاح", "Apis.Details.ApiConsole.SelectAppPanel.environment": "يرجى تحديد بيئة", "Apis.Details.ApiConsole.ApiConsole.subscribe.to.application": "يرجى الاشتراك في تطبيق", "Apis.Details.ApiConsole.SelectAppPanel.production.radio": "إنتاج", "Apis.Details.ApiConsole.SelectAppPanel.applications": "التطبيقات", "Apis.Details.ApiConsole.SelectAppPanel.keyManagers": "المدراء الرئيسيون", "Apis.Details.Comments.CommentAdd.btn.add.comment": "<PERSON><PERSON><PERSON> تعليق", "Apis.Details.Comments.CommentAdd.something.went.wrong": "حد<PERSON> خطأ ما أثناء إضافة التعليق", "Apis.Details.ApiConsole.security.scheme.apikey": "مفتاح API", "Apis.Details.Comments.CommentEdit.general": "جنرال لواء", "Apis.Details.ApiConsole.security.scheme.oauth": "OAuth", "Apis.Details.ApiConsole.security.scheme.basic": "الأساسي", "Apis.Details.ApiConsole.SelectAppPanel.sandbox.radio": "وضع الحماية", "Apis.Details.Comments.CommentAdd.write.comment.label": "أكتب تعليقا", "Apis.Details.Comments.CommentEdit.blank.comment.error": "لا يمكنك إدخال تعليق فارغ", "Apis.Details.Comments.CommentAdd.btn.cancel": "إلغاء", "Apis.Details.ApiConsole.environment": "بيئة", "Apis.Details.Comments.CommentAdd.write.comment.help": "أكتب تعليقا", "Apis.Details.Comments.CommentEdit.feature.request": "طلب المواصفات", "Apis.Details.ApiConsole.SelectAppPanel.select.subscribed.application": "طلبات الاشتراك", "Apis.Details.Comments.CommentAdd.error.blank.comment": "لا يمكنك إدخال تعليق فارغ", "Apis.Details.Comments.CommentEdit.something.went.wrong": "حد<PERSON> خطأ ما أثناء إضافة التعليق", "Apis.Details.Comments.CommentEdit.btn.cancel": "إلغاء", "Apis.Details.Comments.CommentEdit.btn.save": "<PERSON><PERSON><PERSON>", "Apis.Details.Documents.Documentation.no.docs.content": "لا توجد وثائق متاحة لواجهة برمجة التطبيقات هذه", "Apis.Details.Comments.CommentEdit.bug.report": "تقرير الشوائب", "Apis.Details.Documents.View.btn.download": "تحميل", "Apis.Details.Documents.Documentation.title": "وثائق API", "Apis.Details.Environments.copy.to.clipboard": "نسخ إلى الحافظة", "Apis.Details.Environments.download.swagger.error": "خطأ في تنزيل Swagger", "Apis.Details.InfoBar.default.gateway.urls": "عناوين URL الافتراضية للبوابة", "Apis.Details.InfoBar.graphQL.schema": "مخطط GraphQL", "Apis.Details.Documents.View.error.downloading": "خطأ في تنزيل الملف", "Apis.Details.Documents.View.file.availability": "لا يوجد ملف متاح", "Apis.Details.Environments.copied": "نسخ", "Apis.Details.Environments.download.wsdl.error": "خطأ في تنزيل Swagger", "Apis.Details.Environments.download.graphql.error": "خطأ في تنزيل GraphQL", "Apis.Details.GraphQLConsole.GraphQLConsole.title": "جرب أو حاول", "Apis.Details.InfoBar.keyManagers": "المدراء الرئيسيون", "Apis.Details.Environments.download.wsdl": "WSDL", "Apis.Details.InfoBar.gateway.urls": "عناوين URL للبوابة", "Apis.Details.GraphQLConsole.GraphQLUI.URLs": "عناوين URL للبوابة", "Apis.Details.InfoBar.available.mgLabels": "طرق Microgatew المتاحة", "Apis.Details.InfoBar.download.Schema": "تنزيل المخطط", "Apis.Details.InfoBar.gateway.environments": "بيئات البوابة", "Apis.Details.Environments.download.swagger": "اختيال", "Apis.Details.InfoBar.list.version": "الإصدار", "Apis.Details.InfoBar.list.context": "سياق الكلام", "Apis.Details.InfoBar.ingress.urls": "الوصول إلى عناوين URL الخاصة بـ Kubernetes", "Apis.Details.GraphQLConsole.QueryComplexityView.title": "قيم التعقيد المخصص", "Apis.Details.InfoBar.owner": "صاحب", "Apis.Details.InfoBar.less": "أقل", "Apis.Details.InfoBar.microgateway.urls": "عناوين URL للبوابة المصغرة", "Apis.Details.InfoBar.list.tags": "العلامات", "Apis.Details.Labels.copy.to.clipboard": "نسخ إلى الحافظة", "Apis.Details.Overview.comments.no.content": "لا تعليقات حتى الآن", "Apis.Details.Labels.copied": "نسخ", "Apis.Details.InfoBar.technical": "المالك الفني", "Apis.Details.InfoBar.more": "أكثر", "Apis.Details.InfoBar.provider": "مزود", "Apis.Details.Overview.documents.list.title.sufix.document": " المستند", "Apis.Details.Overview.comments.show.more": "إظهار المزيد >>", "Apis.Details.Overview.documents.list.title.sufix.documents": " مستندات", "Apis.Details.Overview.api.subscriptions": "الاشتراكات", "Apis.Details.Overview.documents.list.title.prefix": "<PERSON><PERSON><PERSON>", "Apis.Details.Overview.comments.title": "تعليقات", "Apis.Details.Overview.documents.list.title.sufix.documents.multiple": " المستندا<PERSON> خارج", "Apis.Details.Operations.notFound": "لم يتم العثور على العمليات", "Apis.Details.Overview.documents.no.content": "لا توجد وثائق متاحة", "Apis.Details.Overview.resources.title": "مصادر", "Apis.Details.Overview.no.subscription.message": "الاشتراكات غير مسموح بها", "Apis.Details.Overview.resources.show.more": "اختبار >>", "Apis.Details.Overview.mutualssl.basicauth": "الاشتراك غير مطلوب لواجهة برمجة تطبيقات أو SSL المتبادلة مع المصادقة الأساسية فقط.", "Apis.Details.Overview.sdk.generation.description": "إذا كنت ترغب في إنشاء تطبيق برمجي\n                                                     لاستهلاك واجهات برمجة التطبيقات المشتركة ، يمكنك إنشاء جانب العميل\n                                                      SDK للغة / إطار عمل مدعوم واستخدامها كبداية\n                                                       أشر إلى كتابة تطبيق البرنامج.", "Apis.Details.Overview.sdk.generation.show.more": "إظهار المزيد >>", "Apis.Details.Overview.documents.title": "مستندات", "Apis.Details.Overview.signin.subscribe.btn.link": "تسجيل الدخول للاشتراك", "Apis.Details.Overview.subscribe.count.plural": "تم الاشتراك في الطلبات.", "Apis.Details.Overview.sdk.generation.title": "<PERSON>ي<PERSON>", "Apis.Details.Overview.subscribe.info": "يمكّنك الاشتراك من تلقي الرموز المميزة للدخول والمصادقة لاستدعاء واجهة برمجة التطبيقات هذه.", "Apis.Details.Overview.subscribebtn.link": "الإشتراك", "Apis.Details.Overview.subscriptions.title": "الاشتراكات", "Apis.Details.Sdk.title": "مجموعات تطوير البرامج (SDK)", "Apis.Details.Sdk.no.sdks.content": "لا تتوفر حزم SDK لواجهة برمجة التطبيقات هذه", "Apis.Details.Social.EmbadCode": "امباد", "Apis.Details.Sdk.search.sdk": "ب<PERSON><PERSON> SDK", "Apis.Details.index.comments": "تعليقات", "Apis.Details.Overview.subscribe.count.singular": "تم الاشتراك في التطبيق.", "Apis.Details.TryOutConsole.access.token.tooltip": "يمكنك استخدام رمز الدخول الحالي أو يمكنك إنشاء مفتاح اختبار جديد.", "Apis.Details.Sdk.no.sdks": "لا توجد حزم SDK", "Apis.Listing.APICardView.already.subscribed": "تم الاشتراك", "Apis.Details.index.documentation": "توثيق", "Apis.Details.index.all.apis": "جميع واجهات برمجة التطبيقات", "Apis.Details.index.try.out": "جرب أو حاول", "Apis.Details.index.subscriptions": "الاشتراكات", "Apis.Listing.APIList.id": "هوية شخصية", "Apis.Listing.ApiBreadcrumbs.apigroups.main": "مجموعات API", "Apis.Details.index.sdk": "حزم SDK", "Apis.Listing.APIList.version": "الإصدار", "Apis.Listing.ApiTableView.context": "سياق الكلام", "Apis.Details.index.overview": "نظرة عامة", "Apis.Details.index.invalid.tenant.domain": "مجال المستأجر غير صالح", "Apis.Listing.APIList.name": "اسم", "Apis.Listing.APIList.policy": "سياسات", "Apis.Listing.ApiTableView.type": "نوع", "Apis.Listing.ApiTableView.rating": "تقييم", "Apis.Listing.ApiTableView.error.loading": "خطأ أثناء تحميل واجهات برمجة التطبيقات", "Apis.Listing.ApiTableView.name": "اسم", "Apis.Listing.ApiTableView.invalid.tenant.domain": "مجال المستأجر غير صالح", "Apis.Listing.ApiTableView.provider": "مزود", "Apis.Listing.ApiThumb.by.colon": " :", "Apis.Listing.CategoryListingCategories.categoriesNotFound": "لا يمكن العثور على الفئات", "Apis.Listing.ApiThumb.by": "بواسطة", "Apis.Listing.ApiTagCloud.title": "العلامات", "Apis.Listing.ApiThumb.context": "سياق الكلام", "Apis.Listing.CategoryListingCategories.title": "فئات API", "Apis.Listing.ApiTableView.version": "الإصدار", "Apis.Listing.NoApi.nodata.content": "لا توجد واجهات برمجة تطبيقات لعرضها الآن.", "Apis.Listing.DocThumb.sourceType": "نوع المصدر:", "Apis.Listing.ApiThumb.version": "الإصدار", "Apis.Listing.NoApi.nodata.title": "لا يوجد API متاح", "Apis.Listing.Listing.apis.main": "واجهات برمجة التطبيقات", "Apis.Listing.Listing.ApiTagCloud.title": "فئات / API فئات", "Apis.Listing.StarRatingBar.user": "المستعمل", "Apis.Listing.StarRatingBar.not.rated": "غير مصنف", "Apis.Listing.Recommendations.error.loading": "خطأ أثناء تحميل واجهات برمجة التطبيقات", "Apis.Listing.DocThumb.apiName": "اسم <PERSON>", "Apis.Listing.DocThumb.apiVersion": "إصدار واجهة برمجة التطبيقات", "Apis.Listing.StarRatingBar.users": "المستخدمين", "Apis.Listing.Recommendations.rating": "تقييم", "Apis.Listing.Recommendations.invalid.tenant.domain": "مجال المستأجر غير صالح", "Apis.Settings.SettingsBase.header": "الإعدادات", "Apis.Listing.SubscriptionPolicySelect.subscribe": "الإشتراك", "Apis.Listing.Recommendations.name": "اسم", "Apis.Settings.Alerts.connection.error": "تعذر الاتصال بخادم التحليلات. يرجى التحقق من الاتصال.", "Apis.Listing.TagCloudListingTags.allApis": "كل أبيس", "Apis.Listing.TagCloudListing.apigroups.main": "مجموعات API", "Apis.Settings.SettingsBase.sub.header": "عرض وتكوين إعدادات مدخل المطور", "Apis.Listing.TagCloudListingTags.title": "مجموعات <PERSON>", "Apis.Listing.TableView.TableView.doc.flag": "[وثيقة]", "Applications.ApplicationFormHandler.app.updated.success": "تم تحديث التطبيق بنجاح", "Applications.Create.ApplicationFormHandler.Application.created.successfully": "تم إنشاء التطبيق بنجاح.", "Apis.Listing.TagCloudListingTags.tagsNotFound": "لا يمكن العثور على مجموعات API", "Applications.Create.ApplicationFormHandler.cancel": "إلغاء", "Applications.Details.InfoBar.application.deleted.successfully": "تم حذف التطبيق {name} بنجاح!", "Applications.Create.ApplicationFormHandler.create.application.heading": "قم بإنشاء تطبيق", "Applications.Create.ApplicationFormHandler.edit.application.sub.heading": "تحرير هذا التطبيق. الاسم والحصة ونوع الرمز المميز معلمات إلزامية والوصف اختياري", "Applications.Create.ApplicationFormHandler.app.name.required": "اسم التطبيق مطلوب", "Applications.Create.ApplicationFormHandler.error.while.creating.the.application": "حد<PERSON> خطأ أثناء إنشاء التطبيق", "Applications.Create.ApplicationFormHandler.edit.application.heading": "تحرير التطبيق", "Applications.Create.ApplicationFormHandler.create.application.sub.heading": "أنشئ تطبيقًا يوفر معلمات الاسم والحصة ونوع الرمز المميز. الوصف اختياري", "Applications.Details.InfoBar.edit": "تعديل", "Applications.Details.InfoBar.application.deleting.error": "<PERSON>ط<PERSON> أثناء حذف التطبيق {name}", "Applications.Details.InfoBar.text": "<PERSON><PERSON><PERSON>", "Applications.Details.InfoBar.subscriptions": "الاشتراكات", "Applications.Create.Listing.add.new.application": "إضافة تطبيق جديد", "Applications.Create.ApplicationFormHandler.save": "<PERSON><PERSON><PERSON>", "Applications.Details.InfoBar.delete": "<PERSON><PERSON><PERSON>", "Applications.Details.InfoBar.edit.text": "تعديل", "Applications.Details.Invoice.close": "قريب", "Applications.Details.InfoBar.throttling.tier": "الطبقة الخانقة", "Applications.Details.Invoice.no.data.available": "لا تتوافر بيانات", "Applications.Details.Invoice.pending.invoice.data": "لم يتم العثور على بيانات الفاتورة المعلقة لهذا الاشتراك.", "Applications.Details.Overview.token.type": "نوع الرمز", "Applications.Details.InfoBar.listing.resource.not.found": "المورد لا يصلح", "Applications.Details.Overview.application.owner": "مالك التطبيق", "Applications.Details.Overview.workflow.status": "حالة سير العمل", "Applications.Details.Invoice.view.btn": "عرض الفاتورة", "Applications.Details.Overview.description": "وصف", "Applications.Details.SubscriptionTableData.delete": "<PERSON><PERSON><PERSON>", "Applications.Details.SubscriptionTableData.edit.text": "تعديل", "Applications.Details.SubscriptionTableData.update.throttling.policy.helper": "قم بتعيين مستوى جديد لسياسة التحكم بالاشتراك الحالي", "Applications.Details.SubscriptionTableData.cancel": "إلغاء", "Applications.Details.SubscriptionTableData.delete.subscription.confirmation": "هل أنت متأكد أنك تريد حذف الاشتراك؟", "Applications.Details.SubscriptionTableData.delete.text": "<PERSON><PERSON><PERSON>", "Applications.Details.SubscriptionTableData.update.throttling.policy.blocked": "الاشتراك في حالة \"محظور\". تحتاج إلى إلغاء حظر الاشتراك في الداخل لتعديل المستوى", "Applications.Details.Subscriptions\n                                                                        .subscription.tier": "مستوى الاشتراك", "Applications.Details.SubscriptionTableData.update.subscription": "تحديث الاشتراك", "Applications.Details.SubscriptionTableData.update.throttling.policy.onHold": "الاشتراك في حالة ON_HOLD حاليًا. تحتاج إلى الحصول على الموافقة على الاشتراك قبل تحرير المستوى", "Applications.Details.SubscriptionTableData.update.throttling.policy": "مستوى الاشتراك الحالي:", "Applications.Details.Subscriptions.action": "<PERSON><PERSON><PERSON>", "Applications.Details.SubscriptionTableData.update": "تحديث", "Applications.Details.Subscriptions\n                                                                        .subscription.state": "حالة دورة الحياة", "Applications.Details.SubscriptionTableData.update.throttling.policy.name": "الطبقة الخانقة", "Applications.Details.Subscriptions.no.subscriptions": "لا توجد اشتراكات متاحة", "Applications.Details.Subscriptions.error.occurred.during.subscription": "حد<PERSON> خطأ أثناء الاشتراك", "Applications.Details.Subscriptions.error.occurred.during.subscription.not.201": "حد<PERSON> خطأ أثناء الاشتراك", "Applications.Details.SubscriptionTableData.update.throttling.policy.rejected": "الاشتراك حالة مرفوضة حاليا. تحتاج إلى الحصول على الموافقة على الاشتراك قبل تحرير المستوى", "Applications.Details.Subscriptions.api.name": "API", "Applications.Details.Subscriptions.Status": "حالة الاشتراك", "Applications.Details.SubscriptionTableData.update.throttling.policy.tier.update": "تحديث المستوى المعلق:", "Applications.Details.Subscriptions.no.subscriptions.content": "لا توجد اشتراكات متاحة لهذا التطبيق", "Applications.Details.Subscriptions.filter.msg.all.apis": "عرض جميع واجهات برمجة التطبيقات", "Applications.Details.Subscriptions.filter.msg": "واجهات برمجة التطبيقات المفلترة لـ", "Applications.Details.Subscriptions.subscription.management": "إدارة الاشتراك", "Applications.Details.Subscriptions.search": "بحث APIs", "Applications.Details.menu.overview": "نظرة عامة", "Applications.Details.applications.all": "كل التطبيقات", "Applications.Details.Subscriptions.select.a.subscription.policy": "حدد سياسة الاشتراك", "Applications.Details.Subscriptions.subscription.management.add": "اشترك واجهات برمجة التطبيقات", "Applications.Details.Subscriptions.subscription.successful": "تم الاشتراك بنجاح", "Applications.Details.oauth2.keys.main.title": " مفاتيح OAuth2", "Applications.Details.api.keys.title": " مفتاح API", "Applications.Details.menu.sandbox.keys": "مفاتيح وضع الحماية", "Applications.Details.menu.prod.keys": "مفا<PERSON><PERSON><PERSON> الإنتاج", "Applications.Details.menu.oauth.tokens": "رموز OAuth2", "Applications.Details.menu.subscriptions": "الاشتراكات", "Applications.Listing.ApplicationTableHead.owner": "صاحب", "Applications.Listing.ApplicationTableHead.workflow.status": "حالة سير العمل", "Applications.Edit.app.update.error.no.required.attribute": "يرجى ملء جميع سمات التطبيق المطلوبة", "Applications.Listing.ApplicationTableHead.subscriptions": "الاشتراكات", "Applications.Listing.ApplicationTableHead.actions": "أجراءات", "Applications.Listing.ApplicationTableHead.policy": "سياسات", "Applications.Listing.AppsTableContent.wait.approval": "بانتظار الموافقة", "Applications.Listing.AppsTableContent.active": "نشيط", "Applications.Listing.AppsTableContent.delete.tooltip": "<PERSON><PERSON><PERSON>", "Applications.Listing.AppsTableContent.inactive": "غير نشط", "Applications.Listing.ApplicationTableHead.name": "اسم", "Applications.Listing.AppsTableContent.rejected": "مرفو<PERSON>", "Applications.Listing.DeleteConfirmation.dialog.title": "<PERSON><PERSON><PERSON> التطبيق", "Applications.Listing.Listing.application.deleting.error": "<PERSON>ط<PERSON> أثناء حذف التطبيق {name}", "Applications.Listing.Listing.applications.no.search.results.body.sufix": "<PERSON><PERSON><PERSON> البحث", "Applications.Listing.DeleteConfirmation.dialog.cancel": "إلغاء", "Applications.Listing.Listing.applications.no.search.results.body.prefix": "تحقق من الإملاء أو حاول", "Applications.Listing.DeleteConfirmation.dialog.text.description": "سيتم إزالة التطبيق", "Applications.Listing.DeleteConfirmation.dialog,delete": "<PERSON><PERSON><PERSON>", "Applications.Listing.Listing.logical.description": "التطبيق عبارة عن مجموعة منطقية من واجهات برمجة التطبيقات.\n                                        تتيح لك التطبيقات استخدام رمز وصول واحد لاستدعاء\n                                         مجموعة من واجهات برمجة التطبيقات والاشتراك في واجهة برمجة تطبيقات واحدة عدة مرات\n                                          ويسمح بالوصول غير المحدود بشكل افتراضي.", "Applications.Listing.Listing.applications": "التطبيقات", "Applications.Listing.Listing.applications.no.search.results.title": "لا توجد تطبيقات مطابقة", "Applications.Listing.Listing.application.deleted.successfully": "تم حذف التطبيق {name} بنجاح!", "Applications.Listing.Listing.noapps.display.link.text": "إضافة تطبيق جديد", "Base.Errors.ResourceNotFound.applications": "التطبيقات", "Applications.Listing.Listing.applications.search": "ب<PERSON><PERSON>", "Base.Errors.ResourceNotFound.api.list": "قائمة API", "Applications.Listing.Listing.clear.search": "<PERSON><PERSON><PERSON> البحث", "Base.Errors.ResourceNotfound.default_body": "الصفحة التي تبحث عنها غير متاحة", "Applications.Listing.Listing.noapps.display.title": "لا توجد تطبيقات متاحة", "Base.Header.GlobalNavbar.menu.apis": "واجهات برمجة التطبيقات", "Base.Header.headersearch.HeaderSearch.tooltip.option1": "حسب اسم API [افتراضي]", "Base.Errors.ResourceNotfound.default_tittle": "الصفحة غير موجودة", "Base.Errors.SubscriptionNotFound.default_title": "الصفحة غير موجودة", "Base.Header.headersearch.HeaderSearch.search_api.tooltip": "بحث APIs", "Base.Errors.ResourceNotFound.more.links": "يمكنك التحقق من الروابط أدناه", "Base.Header.headersearch.HeaderSearch.tooltip.option5": "حسب الوصف [الوصف - الوصف: xxxx]", "Base.Header.GlobalNavbar.menu.home": "الصفحة الرئيسية", "Base.Header.headersearch.HeaderSearch.tooltip.option4": "حسب السياق [النحو - السياق: xxxx]", "Base.Header.headersearch.HeaderSearch.tooltip.option2": "بواسطة موفر واجهة برمجة التطبيقات [البنية - الموفر: xxxx]", "Base.Header.headersearch.HeaderSearch.tooltip.option3": "حسب إصدار API [البنية - الإصدار: xxxx]", "Base.Header.headersearch.HeaderSearch.tooltip.title": "خيارات البحث", "Base.Header.headersearch.SearchUtils.lcState.all": "الكل", "Base.Header.headersearch.HeaderSearch.tooltip.option7": "حسب السياق الفرعي [النحو - السياق الفرعي: xxxx]", "Base.Header.headersearch.HeaderSearch.tooltip.option10": "حسب خصائص واجهة برمجة التطبيقات [البنية - property_name: property_value]", "Base.Header.GlobalNavbar.menu.applications": "التطبيقات", "Base.Header.headersearch.HeaderSearch.tooltip.option6": "حسب العلامات [التركيب - العلامات: xxxx]", "Base.index.logo.alt": "بوابة التطوير", "Base.index.banner.alt": "شعار Dev Portal", "Base.index.copyright.text": "WSO2 API-M v4.1.0 | © 2022 WSO2 LLC", "Base.Header.headersearch.SearchUtils.lcState.published": "إنتاج", "Base.Header.headersearch.HeaderSearch.tooltip.option9": "بواسطة Microgateway Label [البنية - التسمية: xxxx]", "Base.Header.headersearch.SearchUtils.lcState.prototyped": "النموذج", "Change.Password.password.change.disabled": "تم تعطيل تغيير كلمة المرور", "Base.index.settingsMenu.alertConfiguration": "تكوين التنبيهات", "Base.index.go.to.public.store": "انتقل إلى بوابة التطوير العامة", "Change.Password.description": "قم بتغيير كلمة المرور الخاصة بك. الحقول الإلزامية مشار إليها بعلامة النجمة ( * )", "Base.index.sign.in": " تسجيل الدخول", "Change.Password.current.password.incorrect": "كلمة المرور الحالية غير صحيحة", "Change.Password.password.empty": "كلمة المرور فارغة", "Change.Password.password.length.long": "كلمة المرور طويلة جدا!", "Base.index.settingsMenu.changePassword": "<PERSON>ير كلمة السر", "Base.index.logout": "تسجيل خروج", "LandingPage.ApisWithTag.invalid.tenant.domain": "مجال المستأجر غير صالح", "Change.Password.password.changed.success": "تم تغيير كلمة المرور بنجاح", "Change.Password.password.mismatch": "كلمة المرور غير متطابقة", "LoginDenied.title": "خط<PERSON> 403 - م<PERSON><PERSON><PERSON><PERSON>", "Change.Password.password.policy": "سياسة كلمة المرور:", "Change.Password.title": "<PERSON>ير كلمة السر", "Login.RedirectToLogin.you.will.be.redirected.to": "ستتم إعادة توجيهك إلى {page}", "LoginDenied.anonymousview": "انتقل إلى البوابة العامة", "Change.Password.password.pattern.invalid": "نمط كلمة المرور غير صالح", "Change.Password.password.length.short": "كلمة المرور قصيرة جدا!", "Settings.Alert.AlertConfiguration.alert.config.add.error.msg": "حد<PERSON> خطأ أثناء إضافة تكوين التنبيه", "Settings.Alert.AlertConfiguration.alert.config.delete.success.msg": "تم حذف تكوين التنبيه بنجاح", "Settings.Alert.AlertConfiguration.alert.config.add.success.msg": "تمت إضافة تكوين التنبيه بنجاح", "Settings.Alert.AlertConfiguration.alert.config.delete.error.msg": "حد<PERSON> خطأ أثناء حذف التكوين.", "LoginDenied.message": "ليس لديك امتيازات كافية للوصول إلى بوابة المطور.", "LoginDenied.logout": "تسجيل خروج", "Settings.Alert.AlertConfiguration.add": "تكوين جديد", "Settings.Alerts.AlertConfiguration.api.name": "اسم API", "Settings.Alerts.AlertConfiguration.api.name.label": "اسم API", "Settings.Alerts.AlertConfiguration.app.name": "اسم التطبيق", "Settings.Alerts.AlertConfiguration.api.version": "إصدار API", "Settings.Alerts.AlertConfiguration.api.version.label": "إصدار API", "Settings.Alerts.Alerts.abnormal.response.time": "طلبات غير طبيعية في الدقيقة", "Settings.Alerts.AlertConfiguration.request.count.label": "ع<PERSON><PERSON> الطلبات.", "Settings.Alerts.AlertConfiguration.select.version.helper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Settings.Alerts.AlertConfiguration.select.application.helper": "<PERSON><PERSON><PERSON> التطبيق", "Settings.Alerts.AlertConfiguration.select.api.helper": "<PERSON><PERSON><PERSON> اسم API", "Settings.Alerts.AlertConfiguration.applications.label": "تطبيق", "Settings.Alerts.Alerts.close.btn": "قريب", "Settings.Alerts.Alerts.configure.alert": "التكوينات", "Settings.Alerts.Alerts.abnormal.backend.time": "الوصول إلى الموارد غير طبيعي", "Settings.Alerts.AlertConfiguration.configuration": "تكوينات {name}", "Settings.Alerts.Alerts.confirm.btn": "إلغاء الاشتراك في الكل", "Settings.Alerts.AlertConfiguration.threshold.value.helper": "<PERSON><PERSON><PERSON><PERSON> عدد الطلبات.", "Settings.Alerts.Alerts.cancel.btn": "إلغاء", "Settings.Alerts.AlertConfiguration.no.config.message": "ليس لديك أي تكوينات. انقر فوق الزر {newConfig} لإضافة تكوين.", "Settings.Alerts.Alerts.numusual.ip": "وصول IP غير عادي", "Settings.Alerts.Alerts.subscribe.to.alerts.heading": "إدارة اشتراكات التنبيه", "Settings.Alerts.Alerts.frequent.tier": "<PERSON>ر<PERSON> الحد المتكرر للمستوى", "Settings.Alerts.Alerts.loading.error.msg": "حد<PERSON> خطأ أثناء تحميل التنبيهات", "Settings.ChangePasswordForm.Cancel.Button.text": "إلغاء", "Settings.Alerts.Alerts.unsubscribe.confirm.dialog.heading": "تأكيد إلغاء الاشتراك من جميع التنبيهات", "Settings.Alerts.Alerts.enable.analytics.message": "تمكين Analytics لتكوين التنبيهات", "Settings.ChangePasswordForm.new.password": "كلمة سر جديدة", "Settings.Alerts.Alerts.subscribe.to.alerts.subheading": "حدد أنواع التنبيه للاشتراك / إلغاء الاشتراك وانقر فوق حفظ.", "Settings.ChangePasswordForm.enter.current.password": "إدخل كلمة السر الحالية", "Settings.ChangePasswordForm.Save.Button.text": "<PERSON><PERSON><PERSON>", "Settings.Alerts.Alerts.abnormal.request.per.min.description": "يتم تشغيل هذا التنبيه إذا كان هناك ارتفاع مفاجئ في عدد الطلبات في غضون دقيقة واحدة بشكل افتراضي لواجهة برمجة تطبيقات معينة لتطبيق ما. يمكن التعامل مع هذه التنبيهات على أنها إشارة إلى حركة مرور عالية محتملة ، أو نشاط مشبوه ، أو عطل محتمل في تطبيق العميل ، إلخ.", "Settings.Alerts.Alerts.abnormal.request.pattern.description": "يتم تشغيل هذا التنبيه إذا كان هناك تغيير في نمط الوصول إلى الموارد لمستخدم تطبيق معين. يمكن التعامل مع هذه التنبيهات على أنها مؤشر على نشاط مريب قام به مستخدم عبر تطبيقك.", "Settings.Alerts.Alerts.subscribe.success.msg": "تم الاشتراك في التنبيهات بنجاح.", "Settings.Alerts.Alerts.subscribe.error.msg": "حد<PERSON> خطأ أثناء الاشتراك في التنبيهات.", "Settings.Alerts.Alerts.unsubscribe.confirm.dialog.message": "سيؤدي هذا إلى إزالة جميع اشتراكات التنبيه ورسائل البريد الإلكتروني الموجودة. لا يمكن التراجع عن هذا الإجراء.", "Settings.Alerts.Alerts.unsubscribe.error.msg": "حد<PERSON> خطأ أثناء إلغاء الاشتراك.", "Settings.Alerts.Alerts.unusual.ip.access.description": "يتم تشغيل هذا التنبيه إذا كان هناك تغيير في عنوان IP لمصدر الطلب لتطبيق معين من قبل مستخدم أو إذا كان الطلب من عنوان IP مستخدم قبل فترة زمنية تبلغ 30 يومًا (افتراضي). يمكن التعامل مع هذه التنبيهات على أنها مؤشر على نشاط مريب قام به مستخدم عبر تطبيق.", "Settings.ChangePasswordForm.enter.new.password": "أدخل كلمة مرور جديدة", "Shared.AppsAndKeys.ApplicationCreateForm.describe.the.application.help": "صف التطبيق", "Settings.Alerts.Alerts.unsubscribe.success.msg": "تم إلغاء الاشتراك من جميع التنبيهات بنجاح.", "Settings.ChangePasswordForm.confirmationOf.new.password": "تأكيد كلمة المرور الجديدة", "Settings.Alerts.Alerts.tier.limit.hitting.description": "يتم تشغيل هذا التنبيه إذا تم استيفاء حالة واحدة على الأقل من الحالتين أدناه. في حالة اختناق تطبيق معين للوصول إلى حد المستوى المشترك لهذا التطبيق أكثر من 10 مرات (افتراضيًا) في غضون ساعة (افتراضيًا) أو إذا تم اختناق مستخدم معين لتطبيق ما للوصول إلى حد المستوى المشترك لـ واجهة برمجة تطبيقات معينة أكثر من 10 مرات (افتراضيًا) خلال يوم واحد (افتراضيًا)", "Settings.ChangePasswordForm.current.password": "كلمة المرور الحالي", "Shared.AppsAndKeys.ApplicationCreateForm.application.name": "اسم التطبيق", "Settings.ChangePasswordForm.confirm.new.password": "تأكيد كلمة المرور الجديدة", "Shared.AppsAndKeys.ApplicationCreateForm.per.token.quota": "لكل حصة رمزية.", "Shared.ApiKeyRestriction.key.restrictions": "القيود الرئيسية", "Shared.AppsAndKeys.ApplicationCreateForm.add.groups.label": "مجموعات التطبيقات", "Shared.AppsAndKeys.ApiKeyManager.generate.key.help": "استخدم زر توليد المفتاح لتوليد رمز مميز لـ JWT.", "Shared.AppsAndKeys.ApplicationCreateForm.my.mobile.application": "طلبي", "Shared.AppsAndKeys.ImportExternalApp.provide.\n                                                    oauth.button.update": "تحديث", "Shared.AppsAndKeys.ApplicationCreateForm.application.description.label": "وصف التطبيق", "Shared.AppsAndKeys.ImportExternalApp.cancel": "إلغاء", "Shared.AppsAndKeys.ImportExternalApp.provide.oauth.button.provide": "تزود", "Shared.AppsAndKeys.ImportExternalApp.consumer.secret": "سر المستهلك", "Shared.AppsAndKeys.ImportExternalApp.provide.oauth.button.update": "تحديث", "Shared.AppsAndKeys.ImportExternalApp.consumer.key": "مف<PERSON><PERSON><PERSON> المستهلك", "Shared.AppsAndKeys.ApplicationCreateForm.my.mobile.application.placeholder": "تطبيق My Mobile", "Shared.AppsAndKeys.ImportExternalApp.consumer.key.title": "مفتا<PERSON> المستهلك لتطبيق OAuth", "Shared.AppsAndKeys.ImportExternalApp.consumer.secret.of.application": "سر عميل تطبيق OAuth", "Shared.AppsAndKeys.ApplicationCreateForm.assign.api.request": "تعيين حصة طلب واجهة برمجة التطبيقات لكل رمز وصول.\n                            سيتم تقاسم الحصة المخصصة بين الجميع\n                            واجهات برمجة التطبيقات المشتركة للتطبيق.", "Shared.AppsAndKeys.ImportExternalApp.provide.oauth": "قدِّم مفاتي<PERSON> الحالية", "Shared.AppsAndKeys.ApplicationCreateForm.enter.a.name": "أدخل اسمًا لتعريف التطبيق.\n                                    ستتمكن من اختيار هذا التطبيق عند الاشتراك في واجهات برمجة التطبيقات", "Shared.AppsAndKeys.KeyConfiguration.callback.url.label": "عاود الاتصال بالعنوان", "Shared.AppsAndKeys.KeyConfCiguration.Invalid.callback.url.error.text": "URL غير صالح. أدخل رابط صحيح من فضلك.", "Shared.AppsAndKeys.ImportExternalApp.key.provide.user.owner": "يمكن للمالك فقط توفير المفاتيح", "Shared.AppsAndKeys.KeyConfCiguration.callback.url.helper.text": "عنوان URL لرد الاتصال هو عنوان URI لإعادة التوجيه في العميل\n                            التطبيق الذي يستخدمه خادم التفويض لإرسال\n                            وكيل مستخدم العميل (متصفح الويب عادة) مرة أخرى بعد منح الوصول.", "Shared.AppsAndKeys.KeyConfCiguration.Invalid.callback.empty.error.text": "لا يمكن أن يكون عنوان URL لإعادة الاتصال فارغًا عند تحديد منح ضمنية أو رمز التفويض.", "Shared.AppsAndKeys.KeyConfiguration.copied": "نسخ", "Shared.AppsAndKeys.KeyConfiguration.copy.to.clipboard": "نسخ إلى الحافظة", "Shared.AppsAndKeys.KeyConfiguration.grant.types": "أنواع المنح", "Shared.AppsAndKeys.KeyConfiguration.revoke.endpoint.label": "إبطال نقطة النهاية", "Shared.AppsAndKeys.KeyConfiguration.the.application.can": "يمكن للتطبيق استخدام أنواع المنح التالية لإنشاء\n                            رموز الوصول. بناءً على متطلبات التطبيق ، يمكنك تمكين أو تعطيل\n                            أنواع المنح لهذا التطبيق.", "Shared.AppsAndKeys.KeyConfiguration.userinfo.endpoint.label": "نقطة نهاية معلومات المستخدم", "Shared.AppsAndKeys.SubscribeToApi.application": "تطبيق", "Shared.AppsAndKeys.KeyConfiguration.token.endpoint.label": "الرمز المميز لنقطة النهاية", "Shared.AppsAndKeys.TokenManager.cleanup": "نظف", "Shared.AppsAndKeys.SubscribeToApi.available.policies": "السياسات المتاحة -", "Shared.AppsAndKeys.KeyConfiguration.url.to.webapp": "http: // url-to-webapp", "Shared.AppsAndKeys.TokenManager.key.and.secret": "مفتا<PERSON> وسر", "Shared.AppsAndKeys.TokenManager.cleanup.text": "خطأ! لديك مفاتيح تم إنشاؤها جزئيًا.\n                            يرجى النقر فوق الزر \"تنظيف\" وحاول مرة أخرى.", "Shared.AppsAndKeys.TokenManager.key.cleanup.error": "حد<PERSON> خطأ أثناء تنظيف مفاتيح التطبيق", "Shared.AppsAndKeys.SubscribeToApi.throttling.policy": "سياسة الاختناق", "Shared.AppsAndKeys.SubscribeToApi.select.an.application.to.subscribe": "حدد تطبيقًا للاشتراك", "Shared.AppsAndKeys.TokenManager.key.and.user.owner": "يمكن للمالك فقط إنشاء المفاتيح أو تحديثها", "Shared.AppsAndKeys.TokenManager.key.configuration": "تكوين المفتاح", "Shared.AppsAndKeys.TokenManager.key.cleanup.success": "تم تنظيف مفا<PERSON>يح التطبيق بنجاح", "Shared.AppsAndKeys.TokenManager.key.provide.error": "حد<PERSON> خطأ عند توفير مفاتيح التطبيق", "Shared.AppsAndKeys.TokenManager.key.generate.success": "تم إنشاء مفاتيح التطبيق بنجاح", "Shared.AppsAndKeys.TokenManager.key.generate.error": "حد<PERSON> خطأ عند إنشاء مفاتيح التطبيق", "Shared.AppsAndKeys.TokenManager.key.generate.error.callbackempty": "لا يمكن أن يكون عنوان URL لرد الاتصال فارغًا عند تحديد نوع منح الضمني أو رمز التطبيق", "Shared.AppsAndKeys.TokenManager.key.provide.success": "تم تقديم مفاتيح التطبيق بنجاح", "Shared.AppsAndKeys.TokenManager.key.update.error": "حد<PERSON> خطأ عند تحديث مفاتيح التطبيق", "Shared.AppsAndKeys.TokenManager.key.update.success": "تم تحديث مفاتيح التطبيق بنجاح", "Shared.AppsAndKeys.TokenManager.update.configuration": "التكوينات الرئيسية", "Shared.AppsAndKeys.TokenManagerSummary": "خطأ! لديك مفاتيح تم إنشاؤها جزئيًا. استخدم خيار `تنظيف '.", "Shared.AppsAndKeys.Tokens.apiKeyRestriction.enter.ip": "أد<PERSON>ل عنوان IP", "Shared.AppsAndKeys.Tokens.apiKeyRestriction.enter.referer": "أدخل مرجع Http", "Shared.AppsAndKeys.Tokens.apiKeyRestriction.ip.validity.error": "عنوان إب غير صالح", "Shared.AppsAndKeys.ViewCurl.copied": "نسخ", "Shared.AppsAndKeys.Tokens.when.you.generate.scopes": "نطاقات", "Shared.AppsAndKeys.ViewCurl.copy.to.clipboard": "نسخ إلى الحافظة", "Shared.AppsAndKeys.Tokens.apikey.set.validity.error": "يرجى استخدام رقم صالح لوقت انتهاء صلاحية API Key", "Shared.AppsAndKeys.ViewKeys.access.token": "رمز وصول", "Shared.AppsAndKeys.ViewCurl.help.in.a.similar": "بطريقة مماثلة ، يمكنك إنشاء رمز وصول باستخدام\n                    نوع منح بيانات اعتماد العميل باستخدام الأمر cURL التالي.", "Shared.AppsAndKeys.Tokens.apikey": "فترة صلاحية مفتاح API", "Shared.AppsAndKeys.Tokens.apiKeyRestriction.referer.validity.error": "مرجع Http غير صالح", "Shared.AppsAndKeys.Tokens.apikey.enter.time": "أد<PERSON>ل الوقت بالثواني", "Shared.AppsAndKeys.ViewCurl.help": "يوضح الأمر التالي cURL كيفية إنشاء رمز وصول باستخدام\n                            نوع منح كلمة المرور.", "Shared.AppsAndKeys.Tokens.apikey.set.validity.help": "يمكنك تحديد فترة انتهاء الصلاحية لتحديد فترة صلاحية الرمز المميز بعد التوليد. قم بتعيين هذا على أنه -1 لضمان عدم انتهاء صلاحية apikey.", "Shared.AppsAndKeys.ViewKeys.apiKeyRestriction.ip.example.content": "حدد IPv4 واحد أو IPv6 أو شبكة فرعية باستخدام تدوين CIDR {linebreak} أمثلة: {ip1} أو {ip2} أو {ip3} أو {ip4}", "Shared.AppsAndKeys.Tokens.when.you.generate": "عند إنشاء رموز وصول إلى واجهات برمجة التطبيقات المحمية بواسطة النطاق / النطاقات ، يمكنك تحديد النطاق / النطاقات ثم إنشاء الرمز المميز له. تتيح النطاقات التحكم في الوصول الدقيق إلى موارد واجهة برمجة التطبيقات استنادًا إلى أدوار المستخدم. يمكنك تحديد نطاقات لمورد API. عندما يستدعي المستخدم واجهة برمجة التطبيقات ، لا يستطيع رمز OAuth 2 لحامل OAuth الخاص به منح الوصول إلى أي مورد لواجهة برمجة التطبيقات خارج النطاقات المرتبطة به.", "Shared.AppsAndKeys.ViewKeys.apiKeyRestriction.ip.example.heading": "أمثلة من عناوين IP المسموح بها", "Shared.AppsAndKeys.ViewKeys.client.enable.client.credentials": "مكّن نوع منح بيانات اعتماد العميل لإنشاء رموز وصول تجريبية", "Shared.AppsAndKeys.ViewKeys.apiKeyRestriction.referer.example.heading": "أمثلة على عناوين URL المسموح بها لتقييد مواقع الويب", "Shared.AppsAndKeys.ViewKeys.consumer.generate.btn": "انشاء", "Shared.AppsAndKeys.ViewKeys.consumer.key": "مف<PERSON><PERSON><PERSON> المستهلك", "Shared.AppsAndKeys.ViewKeys.consumer.close.btn": "قريب", "Shared.AppsAndKeys.ViewKeys.apiKeyRestriction.ip.example.content.message": "عنوان URL محدد بمسار محدد: {url1} {linebreak} أي عنوان URL في نطاق فرعي واحد ، باستخدام علامة البدل (*): {url2} {linebreak} أي عنوان فرعي أو مسار في نطاق واحد ، باستخدام علامات البدل (* ): {url3}", "Shared.AppsAndKeys.ViewKeys.consumer.secret": "سر المستهلك", "Shared.AppsAndKeys.ViewKeys.consumer.key.title": "مف<PERSON><PERSON><PERSON> المستهلك للتطبيق", "Shared.AppsAndKeys.ViewKeys.copy.to": "نسخ إلى الحافظة", "Shared.AppsAndKeys.ViewKeys.copied": "نسخ", "Shared.AppsAndKeys.ViewKeys.consumer.secret.of.application": "سر المستهلك للتطبيق", "Shared.AppsAndKeys.ViewKeys.generate.access.token": "إنشاء رمز الوصول", "Shared.AppsAndKeys.ViewSecret.consumer.secret": "سر المستهلك", "Shared.AppsAndKeys.ViewToken.apikey": "مفتاح API", "Shared.AppsAndKeys.ViewToken.access.token": "رمز وصول", "Shared.ConfirmDialog.ok": "حسنا", "Shared.ConfirmDialog.cancel": "إلغاء", "Shared.AppsAndKeys.ViewToken.info.third": " وقد الرمز المميز (", "Shared.AppsAndKeys.ViewToken.please.copy": "يرجى نسخ رمز الوصول", "Shared.AppsAndKeys.ViewSecret.please.copy.secret.help": "يرجى ملاحظة المستهلك الذي تم تجديده\n                            قيمة سرية حيث سيتم عرضها مرة واحدة فقط.", "Shared.AppsAndKeys.ViewKeys.curl.to.generate": "CURL لإنشاء رمز دخول", "Shared.AppsAndKeys.ViewToken.info.second": " ثواني", "Shared.AppsAndKeys.ViewSecret.please.copy.secret": "يرجى نسخ سر المستهلك", "Shared.AppsAndKeys.ViewKeys.key.secret.title": "لم يتم إنشاء مفتاح وسر لهذا التطبيق", "Shared.AppsAndKeys.ViewToken.info.first": "الرمز المميز أعلاه له فترة صلاحية", "Shared.AppsAndKeys.ViewKeys.consumer.secret.button.regenerate": "تجديد سر المستهلك", "Shared.AppsAndKeys.ViewToken.please.copy.help": "يرجى نسخ قيمة الرمز المميز التي تم إنشاؤها حيث سيتم عرضها فقط لجلسة المتصفح الحالية. (لن يكون الرمز المميز مرئيًا في واجهة المستخدم بعد تحديث الصفحة.)", "Shared.AppsAndKeys.ViewToken.info.fourth": ") النطاقات", "Shared.AppsAndKeys.ViewToken.please.copy.apikey": "يرجى نسخ مفتاح API", "Shared.ConfirmDialog.please.confirm": "ير<PERSON>ى تأكيد", "Shared.ConfirmDialog.please.confirm.sure": "هل أنت واثق؟", "api.console.gateway.heading": "بوابة", "Shared.AppsAndKeys.WaitingForApproval.msg.ok": "تم إرسال طلب تسجيل هذا الطلب وهو في انتظار الموافقة.", "Shared.AppsAndKeys.WaitingForApproval.msg.reject": "تم رفض هذا التطبيق من إنشاء المفاتيح", "TenantListing.title": "بوابات المطور المستأجر", "api.console.security.heading": "الأمان", "api.console.require.access.token": "تحتاج إلى رمز وصول لمحاولة API. يرجى تسجيل الدخول والاشتراك في API لإنشاء رمز وصول. إذا كان لديك بالفعل رمز وصول ، فيرجى تقديمه أدناه.", "application.creation.pending": "تم إرسال طلب تسجيل هذا الطلب.", "api.console.security.type.heading": "نوع الأمن", "notice": "تنويه", "micro.gateways": "طرق مجهرية", "api.gateways": "بوابات API", "enter.access.token": "أدخل رمز الدخول", "password": "كلمه السر", "subscription.pending": "تم إرسال طلب الاشتراك الخاص بك وهو الآن في انتظار الموافقة.", "username": "اسم المستخدم"}