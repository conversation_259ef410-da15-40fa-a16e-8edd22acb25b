{"Apis.Chat.Chat.Messages.subject.line": "API Marketplace Assistant is an early feature and can make mistakes. Verify its outputs.", "Apis.Chat.ChatIcon.disable.chat.label": "Disable chat", "Apis.Chat.ChatIcon.tooltip.label": "Open Chat", "Apis.Chat.ChatInput.placeholder": "Type a message...", "Apis.Chat.Header.MarketplaceAssistantBanner.experimental": "Experimental", "Apis.Chat.Header.MarketplaceAssistantBanner.title": "API Marketplace Assistant", "Apis.Chat.Header.reset.chat.btn.tooltip": "<PERSON><PERSON>", "Apis.Details.APIConsole.APIConsole.download.postman": "Postman collection", "Apis.Details.APIConsole.APIConsole.download.swagger": "Swagger ( /swagger.json )", "Apis.Details.ApiChat.ApiChat.initialRequest.finalOutcome.taskCompletedOneItr": "Task completed in 1 iteration.", "Apis.Details.ApiChat.ApiChat.loadingSpecEnrichmentMessage": "We are in the process of preparing the API specification for API Chat.", "Apis.Details.ApiChat.ApiChat.subsequentRequset.finalOutcome.taskCompleted": "Task completed", "Apis.Details.ApiChat.components.ApiChatBanner.apiChatMainTextContent": "Effortlessly test your APIs in natural language with our API Chat Agent powered by Azure OpenAI's cutting-edge language models.", "Apis.Details.ApiChat.components.ApiChatBanner.apiChatMainTextHeader": "Your API is now equipped with an Intelligent Agent!", "Apis.Details.ApiChat.components.ApiChatExecute.disclaimer.label": "It is prudent to exercise a degree of caution and thoughtfulness, as language models may exhibit some degree of unpredictability at times.", "Apis.Details.ApiChat.components.ApiChatExecute.executeButton.label": "Execute", "Apis.Details.ApiChat.components.ApiChatExecute.queryInput.placeholder": "Type the test scenario here...", "Apis.Details.ApiChat.components.ApiChatExecute.rexecuteButton.label": "Re-execute", "Apis.Details.ApiChat.components.ApiChatExecute.stopButton.label": "Stop Execution", "Apis.Details.ApiChat.components.ApiChatPoweredBy.apiChatMainHeader": "API Chat", "Apis.Details.ApiChat.components.ApiChatPoweredBy.configureKey": "Configure Key", "Apis.Details.ApiChat.components.ApiChatPoweredBy.goBack": "Go Back", "Apis.Details.ApiChat.components.ApiChatPoweredBy.poweredByText": "Powered by Azure OpenAI", "Apis.Details.ApiChat.components.ApiChatResponse.CopyToClipboard.copiedText": "<PERSON>pied", "Apis.Details.ApiChat.components.ApiChatResponse.CopyToClipboard.copyText": "Copy cURL to Clipboard", "Apis.Details.ApiChat.components.ApiChatResponse.executionResults": "Certainly! Here are the results of the API calls I executed on your behalf:", "Apis.Details.ApiChat.components.ApiChatResponse.loadingMessage": "Loading next execution step...", "Apis.Details.ApiChat.components.ApiChatResponse.terminatingMessage": "Execution is terminating...", "Apis.Details.ApiChat.components.ConfigureKeyDrawer.cancel": "Cancel", "Apis.Details.ApiChat.components.ConfigureKeyDrawer.done": "Done", "Apis.Details.ApiChat.components.ConfigureKeyDrawer.title": "Configure Key", "Apis.Details.ApiChat.components.CopyToClipboard.copiedText": "<PERSON>pied", "Apis.Details.ApiChat.components.CopyToClipboard.copyText": "Copy to Clipboard", "Apis.Details.ApiChat.components.SampleQueryCard.executeButton": "Execute", "Apis.Details.ApiChat.components.finalOutcome.apiCommunicationError": "Error occurred while attempting to establish a connection with your API.", "Apis.Details.ApiChat.components.finalOutcome.cachingError": "Error occurred during query execution. Try again later.", "Apis.Details.ApiChat.components.finalOutcome.contentViolationError": "Your query seems to contain inappropriate content. Please try again with a different query.", "Apis.Details.ApiChat.components.finalOutcome.executionTerminated": "Execution was terminated.", "Apis.Details.ApiChat.components.finalOutcome.genericError": "Error occurred during query execution.", "Apis.Details.ApiChat.components.finalOutcome.invalidCommandError": "An invalid query is provided.", "Apis.Details.ApiChat.components.finalOutcome.llmConnectionError": "There was an error connecting to Azure OpenAI.", "Apis.Details.ApiChat.components.finalOutcome.llmError": "Error occurred during query execution. Try again.", "Apis.Details.ApiChat.components.finalOutcome.noQuery": "An invalid query is provided.", "Apis.Details.ApiChat.components.finalOutcome.responseParsingError": "Error occurred while attempting to extract the API response.", "Apis.Details.ApiChat.components.finalOutcome.taskExecutionDefault": "An error occurred during query execution.", "Apis.Details.ApiChat.components.finalOutcome.tokenLimitExceededError": "Execution has been terminated due to exceeding the token limit.", "Apis.Details.ApiChat.components.gatewayTimeout.error": "The request has timed out. Please try again later.", "Apis.Details.ApiChat.components.onPremKeyInvalid.error": "Provided token is invalid. Please use a valid token to start testing.", "Apis.Details.ApiChat.components.specEnrichmentError.contentViolationError": "The content in the OpenAPI specification violates the Azure OpenAI content policy.", "Apis.Details.ApiChat.components.specEnrichmentError.genericError": "Error occurred while loading API Chat.", "Apis.Details.ApiChat.components.specEnrichmentError.invalidResourcePathError": "The OpenAPI specification contain unsupported resource path definitions.", "Apis.Details.ApiChat.components.specEnrichmentError.invalidSpecificationError": "The OpenAPI specification could not be parsed. Ensure you are using a valid specification.", "Apis.Details.ApiChat.components.specEnrichmentError.llmConnectionError": "There was an error connecting to Azure OpenAI.", "Apis.Details.ApiChat.components.specEnrichmentError.llmError": "Failed to load API Chat.", "Apis.Details.ApiChat.components.specEnrichmentError.stackOverflowError": "The OpenAPI specification could not be parsed due to a cyclic reference or the excessive length of the specification.", "Apis.Details.ApiChat.components.specEnrichmentError.tokenLimitExceededError": "The OpenAPI specification exceeds the maximum limit.", "Apis.Details.ApiChat.components.specEnrichmentError.unsupportedMediaTypeError": "The OpenAPI specification includes non-JSON input types which are not currently supported.", "Apis.Details.ApiChat.components.specEnrichmentError.unsupportedSpecificationError": "Provided API specification is currently not supported. Only OpenAPI 3.x specifications with certain definitions are allowed for now.", "Apis.Details.ApiChat.components.throttledOut.error": "Your request has been throttled out. Please reach out to the administrator for assistance.", "Apis.Details.ApiChat.warning.apiAccessTokenNotFound": "You must provide an API access token. Configure one by navigating to {configureKeyLink}", "Apis.Details.ApiChat.warning.authTokenMissing": "You must provide a token to start testing. To obtain one, follow the steps provided under {apiChatDocLink}", "Apis.Details.ApiChat.warning.notSignedIn": "You must sign in if you wish to interact with API Chat bot.", "Apis.Details.ApiConsole.AdvertiseDetailsPanel.adv.auth.header": "Authorization Header", "Apis.Details.ApiConsole.AdvertiseDetailsPanel.adv.auth.header.value": "Authorization Header Value", "Apis.Details.ApiConsole.AdvertiseDetailsPanel.authentication.heading": "Authentication", "Apis.Details.ApiConsole.AdvertiseDetailsPanel.endpoint": "Endpoint type", "Apis.Details.ApiConsole.AdvertiseDetailsPanel.endpoint.heading": "API Endpoint", "Apis.Details.ApiConsole.AdvertiseDetailsPanel.endpoint.help": "Please select an endpoint type", "Apis.Details.ApiConsole.AdvertiseDetailsPanel.security.details": "Security Details", "Apis.Details.ApiConsole.ApiConsole.keys.not.generated": "Consumer key and secret not generated for the selected application on the {what} environment.", "Apis.Details.ApiConsole.ApiConsole.subscribe.to.application": "Please subscribe to an application", "Apis.Details.ApiConsole.SelectAppPanel.applications": "Applications", "Apis.Details.ApiConsole.SelectAppPanel.environment": "Please select an environment", "Apis.Details.ApiConsole.SelectAppPanel.environment.show.less": "Show Less", "Apis.Details.ApiConsole.SelectAppPanel.environment.show.more": "Show More", "Apis.Details.ApiConsole.SelectAppPanel.environment.show.more.http.URLs": "Gateway URLs", "Apis.Details.ApiConsole.SelectAppPanel.environment.show.more.subscription.URLs": "Subscription Gateway URLs", "Apis.Details.ApiConsole.SelectAppPanel.production.radio": "Production", "Apis.Details.ApiConsole.SelectAppPanel.sandbox.radio": "Sandbox", "Apis.Details.ApiConsole.SelectAppPanel.select.available.application": "Available applications", "Apis.Details.ApiConsole.SelectAppPanel.select.key.type.heading": "Key Type", "Apis.Details.ApiConsole.SelectAppPanel.select.subscribed.application": "Subscribed applications", "Apis.Details.ApiConsole.TryOutController.default.km.msg.one": "The Resident Key Manager is selected for try out console.", "Apis.Details.ApiConsole.TryOutController.default.km.msg.three": "Try it console is only accessible via the default key manager.Something went wrong while selecting the default Key manager.", "Apis.Details.ApiConsole.TryOutController.default.km.msg.two": "Try it console is only accessible via the default key manager.But the default key manager is disabled at the moment.", "Apis.Details.ApiConsole.environment": "Environment", "Apis.Details.ApiConsole.generate.test.key": "GET TEST KEY", "Apis.Details.ApiConsole.security.scheme.apikey": "API Key", "Apis.Details.ApiConsole.security.scheme.basic": "Basic", "Apis.Details.ApiConsole.security.scheme.oauth": "OAuth", "Apis.Details.Async.Definition.title": "AsyncAPI Specification", "Apis.Details.AsyncApi.error.occurred": "Error occurred while retrieving the API", "Apis.Details.AsyncApiConsole.AsyncApiConsole.Api.Unavailable": "API Not Found !", "Apis.Details.AsyncApiConsole.AsyncApiConsole.title": "Try Out", "Apis.Details.AsyncApiConsole.AsyncApiUI.topics.get.error": "Error while retrieving topics for the API.", "Apis.Details.AsyncApiConsole.Copied": "cURL copied", "Apis.Details.AsyncApiConsole.Copy": "<PERSON><PERSON>", "Apis.Details.AsyncApiConsole.Curl": "Generate Curl", "Apis.Details.AsyncApiConsole.Webhooks.Subscribe": "Subscribe", "Apis.Details.AsyncApiConsole.Webhooks.Unsubscribe": "Unsubscribe", "Apis.Details.AsyncApiConsole.Webhooks.callback": "Callback URL", "Apis.Details.AsyncApiConsole.Webhooks.curl": "cURL", "Apis.Details.AsyncApiConsole.Webhooks.curl.copied": "cURL copied", "Apis.Details.AsyncApiConsole.Webhooks.lease": "Lease Seconds", "Apis.Details.AsyncApiConsole.Webhooks.secret": "Secret", "Apis.Details.Breadcrumb.comments": "Comments", "Apis.Details.Breadcrumb.documents": "Documents", "Apis.Details.Breadcrumb.overview": "Overview", "Apis.Details.Breadcrumb.route.try.out": "Try Out", "Apis.Details.Breadcrumb.sdks": "SDKs", "Apis.Details.Breadcrumb.subscriptions": "Subscriptions", "Apis.Details.Breadcrumb.try.out.api-chat": "API Chat", "Apis.Details.Breadcrumb.try.out.api-console": "API Console", "Apis.Details.ChatMessages.warning.authTokenMissing": "You must provide a token to start using the API Marketplace Assistant. To obtain one, follow the steps provided under {marketplaceAssistantDocLink}", "Apis.Details.Comments.Comment.comment.deleted": "Comment has been successfully deleted", "Apis.Details.Comments.Comment.delete.dialog.label.cancel": "Cancel", "Apis.Details.Comments.Comment.delete.dialog.label.ok": "Yes", "Apis.Details.Comments.Comment.delete.dialog.message": "Are you sure you want to delete this comment?", "Apis.Details.Comments.Comment.delete.dialog.title": "Confirm Delete", "Apis.Details.Comments.Comment.load.more.replies": "Show More Replies", "Apis.Details.Comments.Comment.reply.comment.deleted": "Reply comment has been successfully deleted", "Apis.Details.Comments.Comment.something.went.wrong": "Something went wrong while deleting comment", "Apis.Details.Comments.CommentAdd.btn.add.comment": "Comment", "Apis.Details.Comments.CommentAdd.btn.cancel": "Cancel", "Apis.Details.Comments.CommentAdd.error.blank.comment": "You cannot enter a blank comment", "Apis.Details.Comments.CommentAdd.something.went.wrong": "Something went wrong while adding the comment", "Apis.Details.Comments.CommentAdd.write.comment.help": "Write a comment", "Apis.Details.Comments.CommentAdd.write.comment.label": "Write a comment", "Apis.Details.Comments.CommentEdit.blank.comment.error": "You cannot enter a blank comment", "Apis.Details.Comments.CommentEdit.btn.cancel": "Cancel", "Apis.Details.Comments.CommentEdit.btn.save": "Save", "Apis.Details.Comments.CommentEdit.bug.report": "Bug Report", "Apis.Details.Comments.CommentEdit.feature.request": "Feature Request", "Apis.Details.Comments.CommentEdit.general": "General", "Apis.Details.Comments.CommentEdit.something.went.wrong": "Something went wrong while adding the comment", "Apis.Details.Comments.CommentEdit.write.a.comment": "Write a comment", "Apis.Details.Comments.CommentOptions.delete": "Delete", "Apis.Details.Comments.CommentOptions.reply": "Reply", "Apis.Details.Comments.load.previous.comments": "Show More", "Apis.Details.Comments.no.comments": "No Comments Yet", "Apis.Details.Comments.no.comments.content": "No comments available for this API yet", "Apis.Details.Comments.title": "Comments", "Apis.Details.Comments.write.a.new.comment": "Write a New Comment", "Apis.Details.Creadentials.credetials.mutualssl": "Subscription is not required for Mutual SSL APIs or APIs with only Basic Authentication.", "Apis.Details.Creadentials.credetials.no.tiers": "No tiers are available for the API.", "Apis.Details.Credentials.Credentials.": "An application is primarily used to decouple the consumer from the APIs. It allows you to generate and use a single key for multiple APIs and subscribe multiple times to a single API with different SLA levels.", "Apis.Details.Credentials.Credentials.api.credentials.generate": "Subscription & Key Generation Wizard", "Apis.Details.Credentials.Credentials.api.credentials.subscribed.apps.action": "Actions", "Apis.Details.Credentials.Credentials.api.credentials.subscribed.apps.description": "( Applications Subscribed to this Api )", "Apis.Details.Credentials.Credentials.api.credentials.subscribed.apps.name": "Application Name", "Apis.Details.Credentials.Credentials.api.credentials.subscribed.apps.status": "Application Status", "Apis.Details.Credentials.Credentials.api.credentials.subscribed.apps.tier": "Throttling Tier", "Apis.Details.Credentials.Credentials.api.credentials.subscribed.apps.title": "Subscriptions", "Apis.Details.Credentials.Credentials.api.credentials.with.subscribe.message": "Subscribe to an application and generate credentials", "Apis.Details.Credentials.Credentials.api.credentials.with.wizard.message": "Use the Subscription and Key Generation Wizard. Create a new application -> Subscribe -> Generate keys and Access Token to invoke this API.", "Apis.Details.Credentials.Credentials.generate": "Subscribe", "Apis.Details.Credentials.Credentials.something.went.wrong.with.subscription": "Something went wrong while deleting the Subscription!", "Apis.Details.Credentials.Credentials.subscribe.to.application": "Subscribe", "Apis.Details.Credentials.Credentials.subscribe.to.application.msg": "You need to subscribe to an application to access this API", "Apis.Details.Credentials.Credentials.subscribe.to.application.sign.in": "Sign In to Subscribe", "Apis.Details.Credentials.Credentials.subscribed.successfully": "Subscribed successfully", "Apis.Details.Credentials.Credentials.subscription.deleted.successfully": "Subscription deleted successfully!", "Apis.Details.Credentials.Credentials.subscription.request.created": "Subscription Deletion Request Created!", "Apis.Details.Credentials.Credentials.visit.original.developer.portal": "Visit Original Developer Portal", "Apis.Details.Credentials.OriginalDevportalDetails.original.developer.portal.title": "Original Developer Portal", "Apis.Details.Credentials.OriginalDevportalDetails.visit.original.developer.portal": "Visit Original Developer Portal", "Apis.Details.Credentials.SubscibeButtonPanel.subscribe.btn": "Subscribe", "Apis.Details.Credentials.SubscibeButtonPanel.subscribe.wizard.with.new.app": "Subscription & Key Generation Wizard", "Apis.Details.Credentials.SubscriptionTableRow.manage.app": "MANAGE APP", "Apis.Details.Credentials.SubscriptionTableRow.prod.keys": "PROD KEYS", "Apis.Details.Credentials.SubscriptionTableRow.sandbox.keys": "SANDBOX KEYS", "Apis.Details.Credentials.SubscriptionTableRow.unsubscribe": "UNSUBSCRIBE", "Apis.Details.Credentials.Wizard.CreateAppStep.application.name.is.required": "Application name is required", "Apis.Details.Credentials.Wizard.CreateAppStep.cancel": "Cancel", "Apis.Details.Credentials.Wizard.CreateAppStep.default.km.msg": "Wizard is only accessible via the Resident Key Manager.But the Resident Key Manager is disabled at the moment.", "Apis.Details.Credentials.Wizard.CreateAppStep.error.404": "Resource not found", "Apis.Details.Credentials.Wizard.CreateAppStep.error.while.creating.the.application": "Error while creating the application", "Apis.Details.Credentials.Wizard.GenerateAccessTokenStep": "Generate Access Token for {keyType} environment", "Apis.Details.Credentials.Wizard.GenerateAccessTokenStep.error.404": "Resource not found", "Apis.Details.Credentials.Wizard.GenerateKeysStep.config.km.name": "Key Manager", "Apis.Details.Credentials.Wizard.GenerateKeysStep.error.404": "Resource not found.", "Apis.Details.Credentials.Wizard.GenerateKeysStep.error.keymanager": "Error while selecting the key manager", "Apis.Details.Credentials.Wizard.GenerateKeysStep.key.configuration": "Key Configuration", "Apis.Details.Credentials.Wizard.GenerateKeysStep.key.configuration.help": "These configurations are set for the purpose of the wizard.You have more control over them when you go to the application view.", "Apis.Details.Credentials.Wizard.GenerateKeysStep.list.environment": "Environment", "Apis.Details.Credentials.Wizard.GenerateKeysStep.list.grantTypes": "Grant Types", "Apis.Details.Credentials.Wizard.GenerateKeysStep.list.revokeEndpoint": "Revoke Endpoint", "Apis.Details.Credentials.Wizard.GenerateKeysStep.list.tokenEndpoint": "Token Endpoint", "Apis.Details.Credentials.Wizard.GenerateKeysStep.list.userInfoEndpoint": "User Info Endpoint", "Apis.Details.Credentials.Wizard.SubscribeToAppStep.subscribed.successfully": "Subscribed successfully", "Apis.Details.Credentials.Wizard.Wizard.Cancel": "CANCEL", "Apis.Details.Credentials.Wizard.Wizard.approval.request.for.this.step.has": "A request to register this step has been sent.", "Apis.Details.Credentials.Wizard.Wizard.copy.access.token": "Copy Access Token", "Apis.Details.Credentials.Wizard.Wizard.create": "Create application", "Apis.Details.Credentials.Wizard.Wizard.finish": "Finish", "Apis.Details.Credentials.Wizard.Wizard.generate.access.token": "Generate Access Token", "Apis.Details.Credentials.Wizard.Wizard.generate.keys": "Generate Keys", "Apis.Details.Credentials.Wizard.Wizard.next": "Next", "Apis.Details.Credentials.Wizard.Wizard.rest": "Reset", "Apis.Details.Credentials.Wizard.Wizard.subscribe.to.new.application": "Subscribe to new application", "Apis.Details.Credentials.Wizard.Wizard.test": "Test", "Apis.Details.Documents.Documentation.error.occurred": "Error occurred", "Apis.Details.Documents.Documentation.no.docs": "No Documents Available", "Apis.Details.Documents.Documentation.no.docs.content": "No documents are available for this API", "Apis.Details.Documents.Documentation.select.label": "Select Documents", "Apis.Details.Documents.Documentation.title": "API Documentation", "Apis.Details.Documents.Documentation.type.api.msg.format": "API_MESSAGE_FORMAT", "Apis.Details.Documents.Documentation.type.how.to": "HOWTO", "Apis.Details.Documents.Documentation.type.other": "OTHER", "Apis.Details.Documents.Documentation.type.public.forum": "PUBLIC_FORUM", "Apis.Details.Documents.Documentation.type.samples": "SAMPLES", "Apis.Details.Documents.Documentation.type.support.forum": "SUPPORT_FORUM", "Apis.Details.Documents.Documentation.type.swagger.doc": "SWAGGER_DOC", "Apis.Details.Documents.View.btn.download": "Download", "Apis.Details.Documents.View.error.downloading": "Error downloading the file", "Apis.Details.Documents.View.file.availability": "No file available", "Apis.Details.Environments.Gateway.URL": "Gateway URL", "Apis.Details.Environments.GraphQL.HTTP.Gateway.URL": "Gateway HTTP URL for GraphQL Queries and Mutations", "Apis.Details.Environments.GraphQL.WS.Gateway.URL": "Gateway Websocket URL for GraphQL Subscriptions", "Apis.Details.Environments.apiExternalProductionEndpoint": "External Production Endpoint", "Apis.Details.Environments.apiExternalSandboxEndpoint": "External Sandbox Endpoint", "Apis.Details.Environments.copied": "<PERSON>pied", "Apis.Details.Environments.copy.to.clipboard": "Copy to clipboard", "Apis.Details.Environments.default.url": "( Default Version )", "Apis.Details.Environments.download.asyncapi": "AsyncAPI Specification", "Apis.Details.Environments.download.asyncapi.error": "Error downloading the AsyncAPI Specification", "Apis.Details.Environments.download.asyncapi.text": "Download AsyncAPI Specification", "Apis.Details.Environments.download.graphQL": "GraphQL", "Apis.Details.Environments.download.graphql.error": "Error downloading the GraphQL Schema", "Apis.Details.Environments.download.graphql.text": "Download GraphQL", "Apis.Details.Environments.download.swagger": "Swagger", "Apis.Details.Environments.download.swagger.error": "Error downloading the Swagger", "Apis.Details.Environments.download.swagger.text": "Download Swagger", "Apis.Details.Environments.download.wsdl": "WSDL", "Apis.Details.Environments.download.wsdl.error": "Error downloading the WSDL", "Apis.Details.Environments.download.wsdl.text": "Download WSDL", "Apis.Details.Environments.externalEndpoint.label.url": "URL", "Apis.Details.Environments.label.noendpoint": "No endpoints yet.", "Apis.Details.Environments.label.url": "URL", "Apis.Details.GoToTryOut.btn.tryout": "Try Out", "Apis.Details.GoToTryOut.btn.view.definition": "View Definition", "Apis.Details.GoToTryOut.continue.on.close": "Close", "Apis.Details.GoToTryOut.error.404": "Resource not found.", "Apis.Details.GoToTryOut.error.keymanager": "Key Generation is Blocked.", "Apis.Details.GoToTryOut.popup.final.message": "All set to try out. Use the \"Generate Keys\" button to get an access token while you are on the Try Out page.", "Apis.Details.GoToTryOut.popup.generate.complete": "Consumer key and secret generated successfully!", "Apis.Details.GoToTryOut.popup.generate.inprogress": "Generating Consumer key and secret ...", "Apis.Details.GoToTryOut.popup.key.secret": "Consumer key and secret", "Apis.Details.GoToTryOut.popup.prepare.complete": "Getting ready to generate keys", "Apis.Details.GoToTryOut.popup.prepare.inprogress": "Gathering information to generate keys ...", "Apis.Details.GoToTryOut.popup.subscribe.complete": "API subscribe to DefaultApplication", "Apis.Details.GoToTryOut.popup.subscribe.complete.success": "API subscribe to DefaultApplication successfully!", "Apis.Details.GoToTryOut.popup.subscribe.inprogress": "API subscribing to DefaultApplication ...", "Apis.Details.GraphQLConsole.GraphQLConsole.title": "Try Out", "Apis.Details.GraphQLConsole.QueryComplexityView.title": "Custom Complexity Values", "Apis.Details.Graphql.Console.form.cancel.btn": "Cancel", "Apis.Details.Graphql.console.delete.btn": "Delete", "Apis.Details.Graphql.console.delete.title": "Delete Additional Header", "Apis.Details.NewOverview.Endpoints.blank": "-", "Apis.Details.Overview.additional.properties": "Additonal properties", "Apis.Details.Overview.api.credentials.subscribed.apps.name": "Application Name", "Apis.Details.Overview.api.credentials.subscribed.apps.status": "Application Status", "Apis.Details.Overview.api.credentials.subscribed.apps.tier": "Throttling Tier", "Apis.Details.Overview.business.info": "Business Info", "Apis.Details.Overview.business.plans.requests.unit": "Requests/{timeUnit}", "Apis.Details.Overview.business.plans.time.unit.days": "days", "Apis.Details.Overview.business.plans.time.unit.hours": "hours", "Apis.Details.Overview.business.plans.time.unit.min": "min", "Apis.Details.Overview.business.plans.time.unit.months": "months", "Apis.Details.Overview.business.plans.time.unit.years": "years", "Apis.Details.Overview.business.plans.title": "Business Plans", "Apis.Details.Overview.comments.show.more.more": "more", "Apis.Details.Overview.comments.title": "Comments", "Apis.Details.Overview.description.less": "less", "Apis.Details.Overview.description.more": "more", "Apis.Details.Overview.documents.error.occurred": "Error occurred", "Apis.Details.Overview.documents.no.content": "No Documents Available", "Apis.Details.Overview.documents.title": "Documents", "Apis.Details.Overview.error.occurred": "Error occurred", "Apis.Details.Overview.error.occurred.docs": "Error occurred when fetching documents", "Apis.Details.Overview.error.occurred.subs": "Error occurred when fetching subscription policies", "Apis.Details.Overview.key.manager": "Key Managers", "Apis.Details.Overview.list.provider": "By", "Apis.Details.Overview.list.tags.not": "Not Tagged", "Apis.Details.Overview.list.version": "Version", "Apis.Details.Overview.select.env.error": "Error Selecting Environment", "Apis.Details.Overview.source": "Source", "Apis.Details.Overview.subscriptions.not.required": "No subscriptions required", "Apis.Details.Overview.subscriptions.not.required.content": "Subscriptions are not required for this API. You can consume this without subscribing to it.", "Apis.Details.Overview.subscriptions.title": "Subscriptions", "Apis.Details.Overview.tags.title": "Tags", "Apis.Details.PubTopic.copied": "<PERSON>pied", "Apis.Details.PubTopic.copy.to.clipboard": "Copy to clipboard", "Apis.Details.Resources.components.Operation.scopes": "<PERSON><PERSON><PERSON>", "Apis.Details.Resources.components.Operation.security": "Security", "Apis.Details.Resources.components.Operation.security.disabled": "Disabled", "Apis.Details.Resources.components.Operation.security.enabled": "Enabled", "Apis.Details.Resources.components.Operation.security.operation": "Security", "Apis.Details.Sdk.download.btn": "Download", "Apis.Details.Sdk.no.sdks": "No SDKs", "Apis.Details.Sdk.no.sdks.content": "No SDKs available for this API", "Apis.Details.Sdk.search.sdk": "Search SDK", "Apis.Details.Sdk.title": "Software Development Kits (SDKs)", "Apis.Details.Social.EmbedCode": "Embed", "Apis.Details.SolaceTopicsInfo.SelectAppPanel.select.\n                                                        deployed.environment": "Deployed Environments", "Apis.Details.SolaceTopicsInfo.SelectAppPanel.select.\n                                                        environment.protocol": "Available Protocols", "Apis.Details.SolaceTopicsInfo.SelectAppPanel.select.subscribed.\n                                                    application": "Subscribed applications", "Apis.Details.StarRatingSummary.not.rated": "Not Rated", "Apis.Details.StarRatingSummary.user": "user", "Apis.Details.StarRatingSummary.users": "users", "Apis.Details.SubTopic.copied": "<PERSON>pied", "Apis.Details.SubTopic.copy.to.clipboard": "Copy to clipboard", "Apis.Details.Swagger.URL.copied": "<PERSON>pied", "Apis.Details.Swagger.URL.copy.to.clipboard": "Copy to clipboard", "Apis.Details.TryOutConsole.access.token.tooltip": "You can use your existing Access Token or you can generate a new Test Key.", "Apis.Details.index.all.apis": "ALL APIs", "Apis.Details.index.comments": "Comments", "Apis.Details.index.definition": "Definition", "Apis.Details.index.documentation": "Documents", "Apis.Details.index.invalid.tenant.domain": "Invalid tenant domain", "Apis.Details.index.overview": "Overview", "Apis.Details.index.sdk": "SDKs", "Apis.Details.index.secondary.navigation": "Secondary Navigation", "Apis.Details.index.solaceTopicsInfo": "Solace Info", "Apis.Details.index.subscriptions": "Subscriptions", "Apis.Details.index.try.out.": "Try Out", "Apis.Details.index.try.out.api.chat": "API Chat", "Apis.Details.index.try.out.api.console": "API Console", "Apis.Details.protocols.and.endpoints": "Protocols & Endpoints", "Apis.Listing.APICardView.already.subscribed": "Subscribed", "Apis.Listing.APICardView.not.allowed": "Not Allowed", "Apis.Listing.APICardView.rows.per.page": "Rows per page", "Apis.Listing.APIList.id": "Id", "Apis.Listing.APIList.isSubscriptionAvailable": "Is Subscription Available", "Apis.Listing.APIList.name": "Name", "Apis.Listing.APIList.subscription.status": "Subscription Status", "Apis.Listing.APIList.version": "Version", "Apis.Listing.ApiBreadcrumbs.apigroups.main": "API Groups", "Apis.Listing.ApiTableView.business.owner.caption": "(Business Owner)", "Apis.Listing.ApiTableView.context": "Context", "Apis.Listing.ApiTableView.download.csv": "Download CSV", "Apis.Listing.ApiTableView.error.loading": "Error While Loading APIs", "Apis.Listing.ApiTableView.invalid.tenant.domain": "Invalid tenant domain", "Apis.Listing.ApiTableView.items.per.page": "Items per page", "Apis.Listing.ApiTableView.name": "Name", "Apis.Listing.ApiTableView.print": "Print", "Apis.Listing.ApiTableView.provider": "Provider/Business Owner", "Apis.Listing.ApiTableView.provider.caption": "(Provider)", "Apis.Listing.ApiTableView.rating": "Rating", "Apis.Listing.ApiTableView.type": "Type", "Apis.Listing.ApiTableView.version": "Version", "Apis.Listing.ApiTableView.view.columns": "View Columns", "Apis.Listing.ApiTagCloud.title": "Tags", "Apis.Listing.ApiThumb.by": "By", "Apis.Listing.ApiThumb.by.colon": ":", "Apis.Listing.ApiThumb.context": "Context", "Apis.Listing.ApiThumb.owners": "Owners", "Apis.Listing.ApiThumb.owners.business": "Business", "Apis.Listing.ApiThumb.owners.technical": "Technical", "Apis.Listing.ApiThumb.version": "Version", "Apis.Listing.CategoryListingCategories.categoriesNotFound": "Categories cannot be found", "Apis.Listing.CategoryListingCategories.title": "API Categories", "Apis.Listing.DefThumb.apiName": "API Name", "Apis.Listing.DefThumb.apiVersion": "API Version", "Apis.Listing.DocThumb.apiName": "API Name", "Apis.Listing.DocThumb.apiVersion": "API Version", "Apis.Listing.DocThumb.sourceType": "Source Type:", "Apis.Listing.Listing.ApiTagCloud.title": "Tags / API Categories", "Apis.Listing.Listing.apis.main": "APIs", "Apis.Listing.NoApi.nodata.content": "There are no APIs to display right now.", "Apis.Listing.NoApi.nodata.title": "No APIs Available", "Apis.Listing.Recommendations.error.loading": "Error While Loading APIs", "Apis.Listing.Recommendations.invalid.tenant.domain": "Invalid tenant domain", "Apis.Listing.Recommendations.name": "Name", "Apis.Listing.Recommendations.rating": "Rating", "Apis.Listing.StarRatingBar.error.occurred": "Error occurred while removing ratings", "Apis.Listing.StarRatingBar.error.occurred.adding": "Error occurred while adding ratings", "Apis.Listing.StarRatingBar.rate.this": "Rate This", "Apis.Listing.StarRatingBar.user": "user", "Apis.Listing.StarRatingBar.users": "users", "Apis.Listing.StarRatingBar.you": "You", "Apis.Listing.SubscriptionPolicySelect.subscribe": "Subscribe", "Apis.Listing.TableView.TableView.def.flag": "[Def]", "Apis.Listing.TableView.TableView.doc.flag": "[Doc]", "Apis.Listing.TagCloudListing.apigroups.main": "API Groups", "Apis.Listing.TagCloudListingTags.allApis": "All Apis", "Apis.Listing.TagCloudListingTags.tagsNotFound": "API groups cannot be found", "Apis.Listing.TagCloudListingTags.title": "API Groups", "Apis.Listing.TaskState.generic.error.prefix": "Error while", "Applications.ApplicationFormHandler.app.updated.success": "Application updated successfully", "Applications.Create.ApplicationFormHandler.Application.created.successfully": "Application created successfully.", "Applications.Create.ApplicationFormHandler.app.desc.long": "Exceeds maximum length limit of 512 characters", "Applications.Create.ApplicationFormHandler.app.name.required": "Application name is required", "Applications.Create.ApplicationFormHandler.cancel": "CANCEL", "Applications.Create.ApplicationFormHandler.create.application.heading": "Create an application", "Applications.Create.ApplicationFormHandler.create.application.sub.heading": "Create an application providing name and quota parameters. Description is optional.", "Applications.Create.ApplicationFormHandler.create.application.sub.heading.required": "Required fields are marked with an asterisk ( * )", "Applications.Create.ApplicationFormHandler.edit.application.heading": "Edit application", "Applications.Create.ApplicationFormHandler.edit.application.sub.heading": "Edit this application. Name and quota are mandatory parameters and description is optional.", "Applications.Create.ApplicationFormHandler.error.while.creating.the.application": "Error while creating the application", "Applications.Create.ApplicationFormHandler.save": "SAVE", "Applications.Create.Listing.add.new.application": "Add New Application", "Applications.Details.InfoBar.application.deleted.successfully": "In Application {name} deleted successfully!", "Applications.Details.InfoBar.application.deleting.error": "Error while deleting application {name}", "Applications.Details.InfoBar.business.plan": "Business Plan", "Applications.Details.InfoBar.delete": "Delete", "Applications.Details.InfoBar.edit": "Edit", "Applications.Details.InfoBar.edit.text": "Edit", "Applications.Details.InfoBar.listing.resource.not.found": "Resource Not Fount", "Applications.Details.InfoBar.subscriptions": "Subscriptions", "Applications.Details.InfoBar.text": "Delete", "Applications.Details.Invoice.close": "Close", "Applications.Details.Invoice.no.data.available": "No Data Available", "Applications.Details.Invoice.pending.invoice.data": "Pending invoice data not found for this subscription.", "Applications.Details.Invoice.view.btn": "View Invoice", "Applications.Details.Overview.application.owner": "Application Owner", "Applications.Details.Overview.application.reset": "Reset", "Applications.Details.Overview.application.reset.text": "RESET", "Applications.Details.Overview.application.reset.tooltip": "Reset the Application Throttle Policy for a Specific User", "Applications.Details.Overview.description": "Description", "Applications.Details.Overview.reset.error": "Error while resetting application policy for {name}", "Applications.Details.Overview.reset.successful": "Application Policy Reset request for {name} has been triggered successfully", "Applications.Details.Overview.workflow.status": "Workflow Status", "Applications.Details.SubscriptionTableData.cancel": "Cancel", "Applications.Details.SubscriptionTableData.delete": "Delete", "Applications.Details.SubscriptionTableData.delete.subscription.confirmation": "Are you sure you want to delete the Subscription?", "Applications.Details.SubscriptionTableData.delete.subscription.confirmation.dialog.title": "Confirm", "Applications.Details.SubscriptionTableData.delete.text": "Delete", "Applications.Details.SubscriptionTableData.edit.text": "Edit", "Applications.Details.SubscriptionTableData.policy.default.tooltip": "This is the default subscription policy used when subscription validation was disabled.", "Applications.Details.SubscriptionTableData.update": "Update", "Applications.Details.SubscriptionTableData.update.business.plan": "Current Business Plan :", "Applications.Details.SubscriptionTableData.update.business.plan.name": "Business Plan", "Applications.Details.SubscriptionTableData.update.subscription": "Update Subscription", "Applications.Details.SubscriptionTableData.update.throttling.policy.blocked": "Subscription is in BLOCKED state. You need to unblock the subscription in order to edit the tier", "Applications.Details.SubscriptionTableData.update.throttling.policy.helper": "Assign a new Business plan to the existing subscription", "Applications.Details.SubscriptionTableData.update.throttling.policy.onHold": "Subscription is currently ON_HOLD state. You need to get approval to the subscription before editing the tier", "Applications.Details.SubscriptionTableData.update.throttling.policy.rejected": "Subscription is currently REJECTED state. You need to get approval to the subscription before editing the tier", "Applications.Details.SubscriptionTableData.update.throttling.policy.tier.update": "Pending Tier Update :", "Applications.Details.SubscriptionTableData.update.throttling.policy.tierUpdatePending": "Subscription is currently TIER_UPDATE_PENDING state. You need to get approval to the existing subscription edit request before editing the tier", "Applications.Details.Subscriptions\n                                                                            .business.plan": "Business Plan", "Applications.Details.Subscriptions\n                                                                            .subscription.state": "Lifecycle State", "Applications.Details.Subscriptions.Status": "Subscription Status", "Applications.Details.Subscriptions.action": "Action", "Applications.Details.Subscriptions.api.name": "API", "Applications.Details.Subscriptions.api.webhooks": "Webhooks", "Applications.Details.Subscriptions.api.webhooks.delivery.time.unavailable": "Delivery data unavailable", "Applications.Details.Subscriptions.api.webhooks.subscriptions.unavailable": "No Webhook subscriptions available at this time.", "Applications.Details.Subscriptions.business.plan.updated": "Business Plan updated successfully!", "Applications.Details.Subscriptions.delete.success": "Subscription deleted successfully!", "Applications.Details.Subscriptions.error.occurred.during.subscription": "Error occurred during subscription", "Applications.Details.Subscriptions.error.occurred.during.subscription.not.201": "Error occurred during subscription", "Applications.Details.Subscriptions.error.occurred.webhook.subscription": "Error while retrieving webhook subscriptions", "Applications.Details.Subscriptions.error.when.updating": "Error occurred when updating subscription", "Applications.Details.Subscriptions.error.while.deleting": "Error occurred when deleting subscription", "Applications.Details.Subscriptions.filter.msg": "Filtered APIs for", "Applications.Details.Subscriptions.filter.msg.all.apis": "Displaying all APIs", "Applications.Details.Subscriptions.no.subscriptions": "No Subscriptions Available", "Applications.Details.Subscriptions.no.subscriptions.content": "No subscriptions are available for this Application", "Applications.Details.Subscriptions.request.created": "Subscription Deletion Request Created!", "Applications.Details.Subscriptions.search": "Search APIs", "Applications.Details.Subscriptions.select.a.subscription.policy": "Select a subscription policy", "Applications.Details.Subscriptions.something.went.wrong": "Something went wrong while deleting the Subscription!", "Applications.Details.Subscriptions.subscription.management": "Subscription Management", "Applications.Details.Subscriptions.subscription.management.add": "Subscribe APIs", "Applications.Details.Subscriptions.subscription.successful": "Subscription successful", "Applications.Details.Subscriptions.wrong.with.subscription": "Something went wrong while updating the Subscription!", "Applications.Details.api.keys.title": "API Key", "Applications.Details.applications.all": "ALL APPs", "Applications.Details.index.secondary.navigation": "Secondary Navigation", "Applications.Details.menu.api.key": "API Key", "Applications.Details.menu.oauth.tokens": "OAuth2 Tokens", "Applications.Details.menu.overview": "Overview", "Applications.Details.menu.prod.keys": "Production Keys", "Applications.Details.menu.sandbox.keys": "Sandbox Keys", "Applications.Details.menu.subscriptions": "Subscriptions", "Applications.Edit.app.update.error.no.required.attribute": "Please fill all required application attributes", "Applications.Listing.ApplicationTableHead.actions": "Actions", "Applications.Listing.ApplicationTableHead.name": "Name", "Applications.Listing.ApplicationTableHead.owner": "Owner", "Applications.Listing.ApplicationTableHead.policy": "Policy", "Applications.Listing.ApplicationTableHead.subscriptions": "Subscriptions", "Applications.Listing.ApplicationTableHead.workflow.status": "Workflow Status", "Applications.Listing.AppsTableContent.active": "ACTIVE", "Applications.Listing.AppsTableContent.delete.tooltip": "Delete", "Applications.Listing.AppsTableContent.delete.tooltip.disabled.button": "Not allowed to delete shared applications", "Applications.Listing.AppsTableContent.deletePending": "DELETE PENDING", "Applications.Listing.AppsTableContent.edit.tooltip": "Edit", "Applications.Listing.AppsTableContent.edit.tooltip.disabled.button": "Not allowed to modify shared applications", "Applications.Listing.AppsTableContent.inactive": "INACTIVE", "Applications.Listing.AppsTableContent.rejected": "REJECTED", "Applications.Listing.AppsTableContent.wait.approval": "waiting for approval", "Applications.Listing.DeleteConfirmation.clean.keys.dialog.content": "This will remove only the key entries stored in devportal, gateway and will not remove the service proveder keys.", "Applications.Listing.DeleteConfirmation.dialog,delete": "Delete", "Applications.Listing.DeleteConfirmation.dialog.cancel": "Cancel", "Applications.Listing.DeleteConfirmation.dialog.text.description": "The application will be removed", "Applications.Listing.DeleteConfirmation.dialog.title": "Delete Application", "Applications.Listing.DeleteConfirmation.remove.keys.dialog.Cancel": "Cancel", "Applications.Listing.DeleteConfirmation.remove.keys.dialog.Delete": "Delete", "Applications.Listing.DeleteConfirmation.remove.keys.dialog.content": "This will remove the key entries stored in devportal, gateway as well as in the service provider.", "Applications.Listing.DeleteConfirmation.remove.keys.dialog.title": "Do you really want to remove keys?", "Applications.Listing.Listing.application.deleted.successfully": "Application {name} deleted successfully!", "Applications.Listing.Listing.application.deleting.error": "Error while deleting application {name}", "Applications.Listing.Listing.application.deleting.requested": "Delete request created for application {name}", "Applications.Listing.Listing.applications": "Applications", "Applications.Listing.Listing.applications.no.search.results.body.prefix": "Check the spelling or try to", "Applications.Listing.Listing.applications.no.search.results.body.sufix": "clear the search", "Applications.Listing.Listing.applications.no.search.results.title": "No Matching Applications", "Applications.Listing.Listing.applications.search": "Search", "Applications.Listing.Listing.applications.search.label": "Search", "Applications.Listing.Listing.applications.search.placeholder": "Search application by name", "Applications.Listing.Listing.clear.search": "Clear Search", "Applications.Listing.Listing.logical.description": "An application is a logical collection of APIs. Applications allow you to use a single access token to invoke a collection of APIs and to subscribe to one API multiple times and allows unlimited access by default.", "Applications.Listing.Listing.noapps.display.link.text": "Add New Application", "Applications.Listing.Listing.noapps.display.title": "No Applications Available", "Applications.Listing.ResetPolicyDialog.dialog.back": "Back", "Applications.Listing.ResetPolicyDialog.dialog.cancel": "Cancel", "Applications.Listing.ResetPolicyDialog.dialog.confirmation.text": "Are you sure you want to reset the throttle policy for user {user}?", "Applications.Listing.ResetPolicyDialog.dialog.confirmation.title": "Confirm Reset", "Applications.Listing.ResetPolicyDialog.dialog.next": "Next", "Applications.Listing.ResetPolicyDialog.dialog.required.alt": "Required", "Applications.Listing.ResetPolicyDialog.dialog.reset": "Reset", "Applications.Listing.ResetPolicyDialog.dialog.text.description": "Enter the user of the application", "Applications.Listing.ResetPolicyDialog.dialog.text.description.tooltip": "Check the username properly before submitting since User will not be validated. Only a Policy Reset Request will be generated for the specified user", "Applications.Listing.ResetPolicyDialog.dialog.title": "Reset Application Throttle Policy", "Applications.Listing.ResetPolicyDialog.dialog.user": "User", "Applications.Listing.ResetPolicyDialog.dialog.user.required": "Application name is required", "Base.Errors.ResourceNotFound.api.list": "API List", "Base.Errors.ResourceNotFound.applications": "Applications", "Base.Errors.ResourceNotFound.more.links": "You may check the links below", "Base.Errors.ResourceNotfound.default_body": "The page you are looking for is not available", "Base.Errors.ResourceNotfound.default_tittle": "Page Not Found", "Base.Errors.ScopeNotFound.message.first": "Sorry, the page you are looking for", "Base.Errors.ScopeNotFound.message.second": "is not allowed under logged in user role scopes. Please login with different user with relevant permission to access this resource.", "Base.Errors.ScopeNotFound.title": "Un-authorized Access", "Base.Errors.SubscriptionNotFound.default_title": "Solace Info Page is not displayed without subscriptions to the API. Please Subscribed to the API", "Base.Header.GlobalNavbar.menu.apis": "APIs", "Base.Header.GlobalNavbar.menu.applications": "Applications", "Base.Header.GlobalNavbar.menu.home": "Home", "Base.Header.headersearch.HeaderSearch.search_api.tooltip": "Search APIs", "Base.Header.headersearch.HeaderSearch.tooltip.option0": "Content [ Default ]", "Base.Header.headersearch.HeaderSearch.tooltip.option1": "Name [ Syntax - name:xxxx ]", "Base.Header.headersearch.HeaderSearch.tooltip.option10": "By API Properties [Syntax - property_name:property_value]", "Base.Header.headersearch.HeaderSearch.tooltip.option12": "By Api Category [ Syntax - api-category:xxxx ]", "Base.Header.headersearch.HeaderSearch.tooltip.option2": "By API Provider [ Syntax - provider:xxxx ]", "Base.Header.headersearch.HeaderSearch.tooltip.option3": "By API Version [ Syntax - version:xxxx ]", "Base.Header.headersearch.HeaderSearch.tooltip.option4": "By Context [ Syntax - context:xxxx ]", "Base.Header.headersearch.HeaderSearch.tooltip.option5": "By Description [ Syntax - description:xxxx ]", "Base.Header.headersearch.HeaderSearch.tooltip.option6": "By Tags [ Syntax - tags:xxxx ]", "Base.Header.headersearch.HeaderSearch.tooltip.title": "Search Options", "Base.Header.headersearch.SearchUtils.lcState.all": "All", "Base.Header.headersearch.SearchUtils.lcState.prototyped": "Prototyped", "Base.Header.headersearch.SearchUtils.lcState.published": "Production", "Base.index.banner.alt": "Dev Portal Banner", "Base.index.copyright.text": "WSO2 API-M v4.5.0 | © 2025 WSO2 LLC", "Base.index.go.to.public.store": "Switch Dev Portals", "Base.index.logo.alt": "Dev Portal", "Base.index.logout": "Logout", "Base.index.settingsMenu.changePassword": "Change Password", "Base.index.sign.in": "Sign-in", "Change.Password.current.password.incorrect": "Current password is incorrect", "Change.Password.description": "Change your own password. Required fields are marked with an asterisk ( * )", "Change.Password.password.change.disabled": "Password change disabled", "Change.Password.password.changed.success": "User password changed successfully. Please use the new password on next sign in", "Change.Password.password.empty": "Password is empty", "Change.Password.password.length.long": "Password is too long!", "Change.Password.password.length.short": "Password is too short!", "Change.Password.password.mismatch": "Password doesn't match", "Change.Password.password.pattern.invalid": "Invalid password pattern", "Change.Password.password.policy": "Password policy:", "Change.Password.title": "Change Password", "GraphQL.Devportal.Tryout.Additional.header.add.new": "Add New", "GraphQL.Devportal.Tryout.Additional.header.dialog.btn.save": "Save", "GraphQL.Devportal.Tryout.Additional.header.dialog.trigger.add": "Add", "GraphQL.Devportal.Tryout.Additional.header.edit": "Edit", "GraphQL.Devportal.Tryout.Additional.header.form.name": "Header Name", "GraphQL.Devportal.Tryout.Additional.header.form.name.help": "Provide Name", "GraphQL.Devportal.Tryout.Additional.header.form.value": "Header Value", "GraphQL.Devportal.Tryout.Additional.header.form.value.help": "Provide Value", "GraphQL.Devportal.Tryout.Additional.headers.header.name": "Header Name", "GraphQL.Devportal.Tryout.Additional.headers.header.value": "Header Value", "GraphQL.Devportal.Tryout.Addtional.headers.expand.group": "Expand to edit", "GraphQL.Devportal.Tryout.Addtional.headers.help": "This configuration is used to add additional Headers.", "GraphQL.Devportal.Tryout.Addtional.headers.hide.group": "Hide group", "GraphQL.Devportal.Tryout.Addtional.headers.title.text": "Add additional headers", "LandingPage.ApisWithTag.invalid.tenant.domain": "Invalid tenant domain", "Login.RedirectToLogin.you.will.be.redirected.to": "You will be redirected to {page}", "LoginDenied.anonymousview": "Go To Public Portal", "LoginDenied.logout": "Logout", "LoginDenied.message": "You don't have sufficient privileges to access the Developer Portal.", "LoginDenied.title": "Error 403 : Forbidden", "Settings.ChangePasswordForm.Cancel.Button.text": "Cancel", "Settings.ChangePasswordForm.Save.Button.text": "Save", "Settings.ChangePasswordForm.confirm.new.password": "Confirm new Password", "Settings.ChangePasswordForm.confirmationOf.new.password": "Confirmation of new Password", "Settings.ChangePasswordForm.current.password": "Current Password", "Settings.ChangePasswordForm.enter.current.password": "Enter Current Password", "Settings.ChangePasswordForm.enter.new.password": "Enter a New Password", "Settings.ChangePasswordForm.new.password": "New Password", "Shared.ApiKeyRestriction.key.restrictions": "Key Restrictions", "Shared.ApiKeyRestriction.key.restrictions.delete.task.tooltip": "Delete task", "Shared.ApiKeyRestriction.key.restrictions.http.referrers": "HTTP Referrers (Web Sites)", "Shared.ApiKeyRestriction.key.restrictions.ip.addresses": "IP Addresses", "Shared.ApiKeyRestriction.key.restrictions.none": "None", "Shared.AppsAndKeyhandleCloses.TokenManager.oauth2.keys.main.title": "OAuth2 Keys", "Shared.AppsAndKeys.ApiKeyManager.generate.api.key.btn": "Generate API Key", "Shared.AppsAndKeys.ApiKeyManager.generate.key.btn": "Generate Key", "Shared.AppsAndKeys.ApiKeyManager.generate.key.help": "Use the Generate Key button to generate a self-contained JWT token.", "Shared.AppsAndKeys.AppConfiguration.application.access.token.expiry.time": "Application Access Token Expiry Time", "Shared.AppsAndKeys.AppConfiguration.application.access.token.expiry.time.tooltip": "Type Application Access Token Expiry Time", "Shared.AppsAndKeys.AppConfiguration.bypass.client.credentials": "Public client", "Shared.AppsAndKeys.AppConfiguration.bypass.client.credentials.tooltip": "Allow authentication without the client secret.", "Shared.AppsAndKeys.AppConfiguration.id.token.expiry.time": "<PERSON><PERSON> Expiry Time", "Shared.AppsAndKeys.AppConfiguration.id.token.expiry.time.tooltip": "Type ID Token Expiry Time", "Shared.AppsAndKeys.AppConfiguration.pkce.mandatory": "Enable PKCE", "Shared.AppsAndKeys.AppConfiguration.pkce.mandatory.tooltip": "Enable PKCE", "Shared.AppsAndKeys.AppConfiguration.pkce.support.plain": "Support PKCE Plain text", "Shared.AppsAndKeys.AppConfiguration.pkce.support.plain.tooltip": "S256 is recommended, plain text too can be used.", "Shared.AppsAndKeys.AppConfiguration.refresh.token.expiry.time": "Refresh Token Expiry Time", "Shared.AppsAndKeys.AppConfiguration.refresh.token.expiry.time.tooltip": "Type Refresh Token Expiry Time", "Shared.AppsAndKeys.AppConfiguration.user.access.token.expiry.time": "User Access Token Expiry Time", "Shared.AppsAndKeys.AppConfiguration.user.access.token.expiry.time.tooltip": "Type User Access Token Expiry Time", "Shared.AppsAndKeys.ApplicationCreateForm.add.groups.label": "Application Groups", "Shared.AppsAndKeys.ApplicationCreateForm.application.description.label": "Application Description", "Shared.AppsAndKeys.ApplicationCreateForm.application.name": "Application Name", "Shared.AppsAndKeys.ApplicationCreateForm.assign.api.request": "Assign API request quota per access token. Allocated quota will be shared among all the subscribed APIs of the application.", "Shared.AppsAndKeys.ApplicationCreateForm.describe.length.error.suffix": "characters remaining", "Shared.AppsAndKeys.ApplicationCreateForm.enable.share.app.with.org": "Share with the organization", "Shared.AppsAndKeys.ApplicationCreateForm.enable.share.app.with.org.label": "Share application with the organization", "Shared.AppsAndKeys.ApplicationCreateForm.enter.a.name": "Enter a name to identify the Application. You will be able to pick this application when subscribing to APIs", "Shared.AppsAndKeys.ApplicationCreateForm.my.mobile.application": "My Application", "Shared.AppsAndKeys.ApplicationCreateForm.my.mobile.application.placeholder": "My Mobile Application", "Shared.AppsAndKeys.ApplicationCreateForm.per.token.quota": "Shared Quota for Application Tokens", "Shared.AppsAndKeys.ApplicationCreateForm.required.alt": "Required", "Shared.AppsAndKeys.ApplicationCreateForm.type.a.group.and.enter": "Type a group and enter", "Shared.AppsAndKeys.ImportExternalApp.cancel": "Cancel", "Shared.AppsAndKeys.ImportExternalApp.consumer.key": "Consumer Key", "Shared.AppsAndKeys.ImportExternalApp.consumer.key.title": "Consumer Key of the OAuth application", "Shared.AppsAndKeys.ImportExternalApp.consumer.secret": "Consumer Secret", "Shared.AppsAndKeys.ImportExternalApp.consumer.secret.of.application": "Consumer Secret of the OAuth application", "Shared.AppsAndKeys.ImportExternalApp.key.provide.user.owner": "Only owner can provide keys", "Shared.AppsAndKeys.ImportExternalApp.provide.\n                                                    oauth.button.update": "Update", "Shared.AppsAndKeys.ImportExternalApp.provide.oauth": "Provide Existing O<PERSON>uth Keys", "Shared.AppsAndKeys.ImportExternalApp.provide.oauth.button.provide": "Provide", "Shared.AppsAndKeys.ImportExternalApp.provide.oauth.button.update": "Update", "Shared.AppsAndKeys.KeyConfCiguration.Invalid.callback.empty.error.text": "Call back URL can not be empty when Implicit or Authorization Code grants are selected.", "Shared.AppsAndKeys.KeyConfCiguration.callback.url.helper.text": "Callback URL is a redirection URI in the client application which is used by the authorization server to send the client's user-agent (usually web browser) back after granting access.", "Shared.AppsAndKeys.KeyConfiguration.API Invocation.Method.label": "API Invocation Method", "Shared.AppsAndKeys.KeyConfiguration.callback.url.label": "Callback URL", "Shared.AppsAndKeys.KeyConfiguration.copied": "<PERSON>pied", "Shared.AppsAndKeys.KeyConfiguration.copy.to.clipboard": "Copy to clipboard", "Shared.AppsAndKeys.KeyConfiguration.grant.types": "Grant Types", "Shared.AppsAndKeys.KeyConfiguration.revoke.endpoint.label": "Revoke Endpoint", "Shared.AppsAndKeys.KeyConfiguration.the.application.can": "The application can use the following grant types to generate Access Tokens. Based on the application requirement,you can enable or disable grant types for this application.", "Shared.AppsAndKeys.KeyConfiguration.token.endpoint.label": "Token Endpoint", "Shared.AppsAndKeys.KeyConfiguration.url.to.webapp": "http://url-to-webapp", "Shared.AppsAndKeys.KeyConfiguration.userinfo.endpoint.label": "User Info Endpoint", "Shared.AppsAndKeys.SubscribeToApi.application": "Application", "Shared.AppsAndKeys.SubscribeToApi.business.plan": "Business Plan", "Shared.AppsAndKeys.SubscribeToApi.select.an.application.to.subscribe": "Select an Application to subscribe", "Shared.AppsAndKeys.TokenExchangeKeyConfiguration.copied": "<PERSON>pied", "Shared.AppsAndKeys.TokenExchangeKeyConfiguration.copy.to.clipboard": "Copy to clipboard", "Shared.AppsAndKeys.TokenManager.ExchangeToken.key.configuration": "Token Generation", "Shared.AppsAndKeys.TokenManager.app.creation.disable\n                                                                                    .warn": "Oauth app creation disabled for {kmName} key manager", "Shared.AppsAndKeys.TokenManager.app.creation.disable.warn": "Oauth app creation disabled for {kmName} key manager", "Shared.AppsAndKeys.TokenManager.cleanup": "Clean up", "Shared.AppsAndKeys.TokenManager.cleanup.text": "Error! You have partially-created keys. Please click `Clean Up` button and try again.", "Shared.AppsAndKeys.TokenManager.generate.keys": "Generate Keys", "Shared.AppsAndKeys.TokenManager.get.keys.request.error": "Error while retrieving the keys", "Shared.AppsAndKeys.TokenManager.key.and.secret": "Key and Secret", "Shared.AppsAndKeys.TokenManager.key.and.user.owner": "Only owner can generate or update keys", "Shared.AppsAndKeys.TokenManager.key.cleanup.error": "Error occurred while cleaning up application keys", "Shared.AppsAndKeys.TokenManager.key.cleanup.success": "Application keys cleaned successfully", "Shared.AppsAndKeys.TokenManager.key.cleanupall.success": "Application keys removed successfully", "Shared.AppsAndKeys.TokenManager.key.configuration": "Key Configuration", "Shared.AppsAndKeys.TokenManager.key.generate.bad.request.error": "Error occurred when generating Access Token", "Shared.AppsAndKeys.TokenManager.key.generate.error": "Error occurred when generating application keys", "Shared.AppsAndKeys.TokenManager.key.generate.error.callbackempty": "Callback URL can not be empty when the Implicit or Application Code grant types selected", "Shared.AppsAndKeys.TokenManager.key.generate.success": "Application keys generated successfully", "Shared.AppsAndKeys.TokenManager.key.generate.success.blocked": "Application keys generate request is currently pending approval by the site administrator.", "Shared.AppsAndKeys.TokenManager.key.provide.error": "Error occurred when providing application keys", "Shared.AppsAndKeys.TokenManager.key.provide.success": "Application keys provided successfully", "Shared.AppsAndKeys.TokenManager.key.update.success": "Application keys updated successfully", "Shared.AppsAndKeys.TokenManager.no.km": "No Key Managers", "Shared.AppsAndKeys.TokenManager.no.km.content": "No Key Managers active to generate keys.", "Shared.AppsAndKeys.TokenManager.oauth2.keys.main.title": "OAuth2 Keys", "Shared.AppsAndKeys.TokenManager.update": "Update", "Shared.AppsAndKeys.TokenManager.update.configuration": "Key Configurations", "Shared.AppsAndKeys.TokenManager.update.keys": "Update Keys", "Shared.AppsAndKeys.TokenManagerSummary": "Error! You have partially-created keys. Use `Clean Up` option.", "Shared.AppsAndKeys.Tokens.apiKeyRestriction.enter.ip": "Enter IP Address", "Shared.AppsAndKeys.Tokens.apiKeyRestriction.enter.referer": "Enter Http Referer", "Shared.AppsAndKeys.Tokens.apiKeyRestriction.ip.address.label": "IP Address", "Shared.AppsAndKeys.Tokens.apiKeyRestriction.ip.validity.error": "Invalid IP Address", "Shared.AppsAndKeys.Tokens.apiKeyRestriction.referer.label": "<PERSON><PERSON><PERSON>", "Shared.AppsAndKeys.Tokens.apiKeyRestriction.referer.validity.error": "Invalid Http Referer", "Shared.AppsAndKeys.Tokens.apikey": "API Key validity period", "Shared.AppsAndKeys.Tokens.apikey.enter.time": "Enter time in seconds", "Shared.AppsAndKeys.Tokens.apikey.set.validity.error": "Please use a valid number for API Key expiry time", "Shared.AppsAndKeys.Tokens.apikey.set.validity.help": "You can set an expiration period to determine the validity period of the token after generation. Set this as -1 to ensure that the apikey never expires.", "Shared.AppsAndKeys.Tokens.apikey.validity.period.label": "API Key with infinite validity period", "Shared.AppsAndKeys.Tokens.when.you.generate": "When you generate access tokens to APIs protected by scope/s, you can select the scope/s and then generate the token for it. Scopes enable fine-grained access control to API resources based on user roles. You define scopes to an API resource. When a user invokes the API, his/her OAuth 2 bearer token cannot grant access to any API resource beyond its associated scopes.", "Shared.AppsAndKeys.Tokens.when.you.generate.scopes": "<PERSON><PERSON><PERSON>", "Shared.AppsAndKeys.ViewCurl.TokenExchange.help": "The following cURL command shows how to generate an access token using the token exchange grant type", "Shared.AppsAndKeys.ViewCurl.copied": "<PERSON>pied", "Shared.AppsAndKeys.ViewCurl.copy.to.clipboard": "Copy to clipboard", "Shared.AppsAndKeys.ViewCurl.error": "Please generate the Consumer Key and Secret for Residence Key Manager with selecting the urn:ietf:params:oauth:grant-type:token-exchange grant type in order to use the token Exchange Approach.", "Shared.AppsAndKeys.ViewCurl.help": "The following cURL command shows how to generate an access token using the Password Grant type.", "Shared.AppsAndKeys.ViewCurl.help.in.a.similar": "In a similar manner, you can generate an access token using the Client Credentials grant type with the following cURL command.", "Shared.AppsAndKeys.ViewKeys.apiKeyRestriction.ip.example.content": "Specify one IPv4 or IPv6 or a subnet using CIDR notation{linebreak}Examples: {ip1}, {ip2}, {ip3} or {ip4}", "Shared.AppsAndKeys.ViewKeys.apiKeyRestriction.ip.example.content.message": "A specific URL with an exact path: {url1}{linebreak}Any URL in a single subdomain, using a wildcard asterisk (*): {url2}{linebreak}Any subdomain or path URLs in a single domain, using wildcard asterisks (*): {url3}", "Shared.AppsAndKeys.ViewKeys.apiKeyRestriction.ip.example.heading": "Examples of IP Addresses allowed", "Shared.AppsAndKeys.ViewKeys.apiKeyRestriction.referer.example.heading": "Examples of URLs allowed to restrict websites", "Shared.AppsAndKeys.ViewKeys.client.enable.client.credentials": "Enable Client Credentials grant type to generate test access tokens", "Shared.AppsAndKeys.ViewKeys.consumer.close.btn": "Close", "Shared.AppsAndKeys.ViewKeys.consumer.generate.btn": "Generate", "Shared.AppsAndKeys.ViewKeys.consumer.key": "Consumer Key", "Shared.AppsAndKeys.ViewKeys.consumer.key.title": "Consumer Key of the application", "Shared.AppsAndKeys.ViewKeys.consumer.secret": "Consumer Secret", "Shared.AppsAndKeys.ViewKeys.consumer.secret.button.regenerate": "Regenerate Consumer Secret", "Shared.AppsAndKeys.ViewKeys.consumer.secret.of.application": "Consumer Secret of the application", "Shared.AppsAndKeys.ViewKeys.copied": "<PERSON>pied", "Shared.AppsAndKeys.ViewKeys.copy.to": "Copy to clipboard", "Shared.AppsAndKeys.ViewKeys.copy.to.clipboard": "Copy to clipboard", "Shared.AppsAndKeys.ViewKeys.curl.to.generate": "CURL to Generate Access Token", "Shared.AppsAndKeys.ViewKeys.generate.access.token": "Generate Access Token", "Shared.AppsAndKeys.ViewKeys.key.secret.title": "Key and Secret is not generated for this application", "Shared.AppsAndKeys.ViewKeys.remove.keys": "Remove Keys", "Shared.AppsAndKeys.ViewSecret.consumer.secret": "Consumer Secret", "Shared.AppsAndKeys.ViewSecret.copied": "<PERSON>pied", "Shared.AppsAndKeys.ViewSecret.copy.to.clipboard": "Copy to clipboard", "Shared.AppsAndKeys.ViewSecret.please.copy.secret": "Please Copy the Consumer Secret", "Shared.AppsAndKeys.ViewSecret.please.copy.secret.help": "Please make a note of the regenerated consumer secret value as it will be displayed only once.", "Shared.AppsAndKeys.ViewToken.access.token": "Access Token", "Shared.AppsAndKeys.ViewToken.apikey": "API Key", "Shared.AppsAndKeys.ViewToken.copied": "<PERSON>pied", "Shared.AppsAndKeys.ViewToken.copy.to.clipboard": "Copy to clipboard", "Shared.AppsAndKeys.ViewToken.info.first": "Above token has a validity period of", "Shared.AppsAndKeys.ViewToken.info.fourth": ") scopes", "Shared.AppsAndKeys.ViewToken.info.second": "seconds", "Shared.AppsAndKeys.ViewToken.info.third": "and the token has (", "Shared.AppsAndKeys.ViewToken.please.copy": "Please Copy the Access Token", "Shared.AppsAndKeys.ViewToken.please.copy.apikey": "Please Copy the API Key", "Shared.AppsAndKeys.ViewToken.please.copy.help": "If the token type is JWT or API Key, please copy this generated token value as it will be displayed only for the current browser session. ( The token will not be visible in the UI after the page is refreshed. )", "Shared.AppsAndKeys.WaitingForApproval.msg.ok": "A request to register this application has been sent and is pending approval.", "Shared.AppsAndKeys.WaitingForApproval.msg.reject": "This application has been rejected from generating keys", "Shared.ConfirmDialog.cancel": "Cancel", "Shared.ConfirmDialog.ok": "OK", "Shared.ConfirmDialog.please.confirm": "Please Confirm", "Shared.ConfirmDialog.please.confirm.sure": "Are you sure?", "TenantListing.title": "Tenant Developer Portals", "Throttling.Advanced.Delete.will.be.deleted": "will be deleted.", "access.token": "Access Token", "api.console.gateway.heading": "Gateway", "api.console.gql.additional.headers": "Additional Headers", "api.console.require.access.token": "You need an access token to try the API. Please log in and subscribe to the API to generate an access token. If you already have an access token, please provide it below.", "api.console.security.heading": "Security", "api.console.security.type.heading": "Security Type", "api.gateways": "API Gateways", "application.creation.pending": "A request to register this application has been sent.", "application.productionKeys.oAuth.externalIdp.tokenGeneration.audience": "Use the audience value \"{allowedAudience}\",", "application.productionKeys.oAuth.externalIdp.tokenGeneration.step1": "Step 1:", "application.productionKeys.oAuth.externalIdp.tokenGeneration.step1.description": "Obtain an access token from {selectedIdpType}.", "application.productionKeys.oAuth.externalIdp.tokenGeneration.token": "{selectedIdpType} Token", "application.productionKeys.oAuth.externalIdp.tokenGeneration.token.description": "In Exchange token flow, A JWT token has to be generated from the {selectedIdpType} idp and then exchange for a token with the Resident Key Manager which can used to invoke APIs", "application.productionKeys.oAuth.externalIdp.tokenGeneration.token.empty.helperText": "{selectedIdPType} token cannot be empty", "application.productionKeys.oAuth.tokenEndpoint": "Token Endpoint", "application.productionKeys.oAuth.tokenEndpoint.value": "{tokenEndpoint}", "application.productionKeys.oAuth.tokenGeneration.step2": "Step 2:", "application.productionKeys.oAuth.tokenGeneration.step2.description": "Obtain test token", "enter.access.token": "Enter access Token", "notice": "Notice", "password": "Password", "solace.application.available.topics.heading": "Available Topics", "solace.application.available.topics.subheading": "Topics permitted to access from solace applications", "solace.application.topics.publish": "Publish Topics", "solace.application.topics.publish.empty": "No Publish Topics to Display.", "solace.application.topics.subscribe": "Subscribe Topics", "solace.application.topics.subscribe.empty": "No Subscribe Topics to Display.", "subscription.pending": "Your subscription request has been submitted and is now awaiting approval.", "subscription.tierPending": "Your subscription update request has been submitted and is now awaiting approval.", "username": "Username"}