{"Apis.Details.ApiCOnsole.generate.test.key": "ටෙස්ට් කී ලබා ගන්න", "Apis.Details.APIConsole.APIConsole.download.swagger": "Swagger (/swagger.json)", "Apis.Details.ApiConsole.SelectAppPanel.keyManagers": "ප්‍රධාන කළමනාකරුවන්", "Apis.Details.ApiConsole.ApiConsole.subscribe.to.application": "කරුණාකර යෙදුමකට දායක වන්න", "Apis.Details.ApiConsole.ApiConsole.title": "උත්සාහ කර බලන්න", "Apis.Details.ApiConsole.SelectAppPanel.environment": "කරුණාකර පරිසරයක් තෝරන්න", "Apis.Details.ApiConsole.SelectAppPanel.select.registered.keyManagers": "ලියාපදිංචි යතුරු කළමනාකරුවන්", "Apis.Details.ApiConsole.security.scheme.apikey": "API යතුර", "Apis.Details.ApiConsole.SelectAppPanel.applications": "අයදුම්පත්", "Apis.Details.ApiConsole.SelectAppPanel.production.radio": "නිෂ්පාදනය", "Apis.Details.Comments.CommentAdd.something.went.wrong": "අදහස එකතු කිරීමේදී යමක් වැරදී ඇත", "Apis.Details.ApiConsole.security.scheme.basic": "මූලික", "Apis.Details.ApiConsole.SelectAppPanel.sandbox.radio": "සෑන්ඩ්බොක්ස්", "Apis.Details.ApiConsole.SelectAppPanel.select.subscribed.application": "දායක වූ අයදුම්පත්", "Apis.Details.ApiConsole.environment": "පරිසරය", "Apis.Details.Comments.CommentAdd.write.comment.help": "අදහසක් ලියන්න", "Apis.Details.Comments.CommentAdd.error.blank.comment": "ඔබට හිස් අදහසක් ඇතුළත් කළ නොහැක", "Apis.Details.Comments.CommentEdit.bug.report": "දෝෂ වාර්තාව", "Apis.Details.Comments.CommentEdit.btn.cancel": "අවලංගු කරන්න", "Apis.Details.Comments.CommentAdd.btn.cancel": "අවලංගු කරන්න", "Apis.Details.Comments.CommentAdd.write.comment.label": "අදහසක් ලියන්න", "Apis.Details.Comments.CommentEdit.blank.comment.error": "ඔබට හිස් අදහසක් ඇතුළත් කළ නොහැක", "Apis.Details.Comments.CommentEdit.write.a.comment": "අදහසක් ලියන්න", "Apis.Details.Comments.CommentEdit.something.went.wrong": "අදහස එකතු කිරීමේදී යමක් වැරදී ඇත", "Apis.Details.Comments.CommentEdit.general": "ජනරාල්", "Apis.Details.Comments.CommentOptions.delete": "මකන්න", "Apis.Details.Comments.load.previous.comments": "පෙර අදහස් පූරණය කරන්න", "Apis.Details.Comments.CommentEdit.feature.request": "විශේෂාංග ඉල්ලීම", "Apis.Details.ApiConsole.SelectAppPanel.select.key.type.heading": "යතුරු වර්ගය", "Apis.Details.Creadentials.credetials.mutualssl": "මූලික සත්‍යාපනය පමණක් ඇති අන්‍යෝන්‍ය SSL API හෝ API සඳහා දායකත්වය අවශ්‍ය නොවේ.", "Apis.Details.Comments.no.comments.content": "මෙම API සඳහා තවම අදහස් නොමැත", "Apis.Details.Comments.showing.comments": "අදහස් පෙන්වයි", "Apis.Details.Credentials.Credentials.api.credentials.generate": "දායකත්වය සහ යතුරු උත්පාදන විශාරද", "Apis.Details.Credentials.Credentials.api.credentials": "දායකත්වයන්", "Apis.Details.Comments.title": "අදහස්", "Apis.Details.Credentials.Credentials.api.credentials.subscribed.apps.tier": "තෙරපීමේ ස්ථරය", "Apis.Details.Credentials.Credentials.api.credentials.subscribed.apps.name": "අයදුම්පත්රයේ නම", "Apis.Details.ApiConsole.security.scheme.oauth": "OAuth", "Apis.Details.Credentials.Credentials.api.credentials.subscribed.apps.title": "දායකත්වයන්", "Apis.Details.Credentials.Credentials.api.credentials.subscribed.apps.status": "යෙදුම් තත්ත්වය", "Apis.Details.Comments.no.comments": "තවම අදහස් නැත", "Apis.Details.Comments.CommentEdit.btn.save": "සුරකින්න", "Apis.Details.Credentials.Credentials.subscribed.successfully": "සාර්ථකව දායක වී ඇත", "Apis.Details.Comments.CommentAdd.btn.add.comment": "අදහස් එක් කරන්න", "Apis.Details.Credentials.Credentials.generate": "දායක වන්න", "Apis.Details.Credentials.Credentials.api.credentials.with.subscribe.message": "යෙදුමකට දායක වී අක්තපත්‍ර ජනනය කරන්න", "Apis.Details.Credentials.Credentials.something.went.wrong.with.subscription": "දායකත්වය මකාදැමීමේදී යමක් වැරදී ඇත!", "Apis.Details.Credentials.Credentials.api.credentials.with.wizard.message": "දායකත්වය සහ යතුරු උත්පාදන විශාරද භාවිතා කරන්න. නව යෙදුමක් සාදන්න -> දායක වන්න -> යතුරු සාදන්න සහ ටෝකනය ප්‍රවේශ කරන්න මෙම API ක්‍රියාත්මක කිරීමට.", "Apis.Details.Credentials.Credentials.you.do.not.need.credentials.to.access.prototyped.api": "මූලාකෘති API වෙත ප්‍රවේශ වීමට ඔබට අක්තපත්‍ර අවශ්‍ය නොවේ", "Apis.Details.Credentials.Credentials.subscribe.to.application": "දායක වන්න", "Apis.Details.Credentials.SubscibeButtonPanel.subscribe.wizard.with.new.app": "දායකත්වය සහ යතුරු උත්පාදන විශාරද", "Apis.Details.Credentials.SubscibeButtonPanel.subscribe.btn": "දායක වන්න", "Apis.Details.Credentials.Credentials.subscribe.to.application.sign.in": "දායක වීමට පුරනය වන්න", "Apis.Details.Credentials.SubscriptionTableRow.sandbox.keys": "සැන්ඩ්බොක්ස් යතුරු", "Apis.Details.Credentials.SubscriptionTableRow.unsubscribe": "UNSUBSCRIBE", "Apis.Details.Credentials.Wizard.GenerateKeysStep.key.configuration": "යතුරු වින්‍යාසය", "Apis.Details.Credentials.Wizard.GenerateKeysStep.list.grantTypes": "ප්‍රදාන වර්ග", "Apis.Details.Credentials.Wizard.GenerateKeysStep.key.configuration.help": "මෙම වින්‍යාසයන් විශාරදයාගේ අරමුණ සඳහා සකසා ඇත. ඔබ යෙදුම් දර්ශනයට යන විට ඔබට ඒවා පාලනය කළ හැකිය.", "Apis.Details.Credentials.Wizard.GenerateKeysStep.list.userInfoEndpoint": "පරිශීලක තොරතුරු එන්ඩ්පොයින්ට්", "Apis.Details.Credentials.Wizard.Wizard.generate.keys": "යතුරු ජනනය කරන්න", "Apis.Details.Credentials.Wizard.GenerateKeysStep.list.environment": "පරිසරය", "Apis.Details.Credentials.Wizard.Wizard.create": "යෙදුම සාදන්න", "Apis.Details.Credentials.Wizard.SubscribeToAppStep.subscribed.successfully": "සාර්ථකව දායක වී ඇත", "Apis.Details.Credentials.Wizard.GenerateKeysStep.list.revokeEndpoint": "එන්ඩ්පොයින්ට් අවලංගු කරන්න", "Apis.Details.Credentials.Wizard.Wizard.subscribe.to.new.application": "නව යෙදුමට දායක වන්න", "Apis.Details.Credentials.Wizard.Wizard.generate.access.token": "ප්‍රවේශ ටෝකනය ජනනය කරන්න", "Apis.Details.Credentials.Wizard.CreateAppStep.error.while.creating.the.application": "යෙදුම නිර්මාණය කිරීමේදී දෝෂයකි", "Apis.Details.Credentials.Wizard.GenerateAccessTokenStep": "{KeyType} පරිසරය සඳහා ප්‍රවේශ ටෝක් ජනනය කරන්න", "Apis.Details.Credentials.SubscriptionTableRow.manage.app": "යෙදුම කළමනාකරණය කරන්න", "Apis.Details.Credentials.Wizard.CreateAppStep.application.name.is.required": "අයදුම්පත්රයේ නම අවශ්ය වේ", "Apis.Details.Credentials.Wizard.Wizard.rest": "නැවත සකසන්න", "Apis.Details.Credentials.Credentials.subscription.deleted.successfully": "දායකත්වය සාර්ථකව මකා දමන ලදි!", "Apis.Details.Credentials.Wizard.Wizard.next": "ඊලඟ", "Apis.Details.Credentials.Credentials.api.credentials.subscribed.apps.description": "(මෙම Api සඳහා දායක වූ අයදුම්පත්)", "Apis.Details.Credentials.Credentials.": "අයදුම්පතක්\n                                        මූලික වශයෙන් භාවිතා කරනුයේ පාරිභෝගිකයා ඒපීඅයි වලින් වෙන් කිරීම සඳහා ය. එය ඔබට ඉඩ දෙයි\n                                        බහු ඒපීඅයි සඳහා තනි යතුරක් ජනනය කර භාවිතා කරන්න සහ කිහිප වතාවක් දායක වන්න\n                                        විවිධ SLA මට්ටම් සහිත තනි API.", "Apis.Details.Credentials.Credentials.subscribe.to.application.msg": "මෙම API වෙත ප්‍රවේශ වීම සඳහා ඔබ යෙදුමකට දායක විය යුතුය", "Apis.Details.Documents.Documentation.hide": "සඟවන්න", "Apis.Details.Documents.Documentation.no.docs": "ලේඛන නොමැත", "Apis.Details.Documents.View.btn.download": "බාගත", "Apis.Details.Documents.View.error.downloading": "ගොනුව බාගත කිරීමේදී දෝෂයකි", "Apis.Details.Environments.copy.to.clipboard": "ක්ලිප් පුවරුවට පිටපත් කරන්න", "Apis.Details.Environments.download.swagger.error": "Swagger බාගත කිරීමේදී දෝෂයකි", "Apis.Details.Environments.copied": "පිටපත් කරන ලදි", "Apis.Details.Documents.Documentation.show": "පෙන්වන්න", "Apis.Details.Documents.View.file.availability": "ගොනුවක් නොමැත", "Apis.Details.Credentials.Wizard.Wizard.test": "පරීක්ෂණය", "Apis.Details.Environments.download.swagger": "ස්වැගර්", "Apis.Details.Credentials.Wizard.Wizard.approval.request.for.this.step.has": "මෙම පියවර ලියාපදිංචි කිරීම සඳහා ඉල්ලීමක් යවා ඇත.", "Apis.Details.Credentials.Wizard.Wizard.Cancel": "අවලංගු කරන්න", "Apis.Details.Credentials.Wizard.GenerateKeysStep.config.km.name": "යතුරු කළමනාකරු", "Apis.Details.Credentials.Wizard.GenerateKeysStep.error.keymanager": "යතුරු කළමනාකරු තේරීමේදී දෝෂයකි", "Apis.Details.Credentials.Wizard.Wizard.copy.access.token": "ප්‍රවේශ ටෝකනය පිටපත් කරන්න", "Apis.Details.Credentials.Wizard.GenerateKeysStep.list.tokenEndpoint": "ටෝකන් එන්ඩ්පොයින්ට්", "Apis.Details.Environments.download.wsdl.error": "Swagger බාගත කිරීමේදී දෝෂයකි", "Apis.Details.Environments.download.graphql.error": "GraphQL බාගත කිරීමේදී දෝෂයකි", "Apis.Details.Credentials.Wizard.Wizard.finish": "අවසන් කරන්න", "Apis.Details.Credentials.SubscriptionTableRow.prod.keys": "නිෂ්පාදන යතුරු", "Apis.Details.GraphQLConsole.GraphQLUI.URLs": "ද්වාර URL", "Apis.Details.InfoBar.available.mgLabels": "ලබා ගත හැකි මයික්‍රොගේට්වේස්", "Apis.Details.GraphQLConsole.GraphQLConsole.title": "උත්සාහ කර බලන්න", "Apis.Details.InfoBar.gateway.environments": "ද්වාර පරිසරයන්", "Apis.Details.InfoBar.default.gateway.urls": "පෙරනිමි ද්වාර URL", "Apis.Details.InfoBar.ingress.urls": "කුබර්නෙට්ස් URL වෙත ප්‍රවේශ වන්න", "Apis.Details.InfoBar.gateway.urls": "ද්වාර URL", "Apis.Details.Documents.Documentation.no.docs.content": "මෙම API සඳහා කිසිදු ලේඛනයක් නොමැත", "Apis.Details.Documents.Documentation.title": "API ප්‍රලේඛනය", "Apis.Details.GraphQLConsole.QueryComplexityView.title": "අභිරුචි සංකීර්ණතා අගයන්", "Apis.Details.Environments.download.wsdl": "WSDL", "Apis.Details.InfoBar.keyManagers": "ප්‍රධාන කළමනාකරුවන්", "Apis.Details.InfoBar.download.Schema": "යෝජනා ක්‍රමය බාගන්න", "Apis.Details.InfoBar.list.context.rating": "ශ්‍රේණිගත කිරීම", "Apis.Details.InfoBar.less": "අඩු", "Apis.Details.InfoBar.list.context": "සන්දර්භය", "Apis.Details.InfoBar.list.version": "පිටපත", "Apis.Details.InfoBar.provider": "සපයන්නා", "Apis.Details.InfoBar.more": "තවත්", "Apis.Details.InfoBar.graphQL.schema": "GraphQL යෝජනා ක්‍රමය", "Apis.Details.InfoBar.list.tags": "ටැග්", "Apis.Details.InfoBar.owner": "හිමිකරු", "Apis.Details.Overview.comments.no.content": "තවම අදහස් නැත", "Apis.Details.Labels.copied": "පිටපත් කරන ලදි", "Apis.Details.Overview.api.subscriptions": "දායකත්වයන්", "Apis.Details.Labels.copy.to.clipboard": "ක්ලිප් පුවරුවට පිටපත් කරන්න", "Apis.Details.InfoBar.microgateway.urls": "මයික්‍රොගේට්වේ URL", "Apis.Details.Overview.documents.list.title.sufix.documents": " ලේඛන", "Apis.Details.Overview.comments.show.more": "තවත් පෙන්වන්න >>", "Apis.Details.Overview.documents.list.title.prefix": "පෙන්වයි", "Apis.Details.Overview.comments.title": "අදහස්", "Apis.Details.Overview.documents.list.title.sufix.documents.multiple": " ලියකියවිලි ඉවතට", "Apis.Details.Overview.documents.list.title.sufix.document": " ලේඛනය", "Apis.Details.Overview.documents.title": "ලේඛන", "Apis.Details.InfoBar.technical": "තාක්ෂණික හිමිකරු", "Apis.Details.Overview.mutualssl.basicauth": "මූලික සත්‍යාපනය පමණක් ඇති අන්‍යෝන්‍ය SSL API හෝ API සඳහා දායකත්වය අවශ්‍ය නොවේ.", "Apis.Details.Operations.notFound": "මෙහෙයුම් හමු නොවීය", "Apis.Details.Overview.documents.no.content": "ලේඛන නොමැත", "Apis.Details.Overview.operations.title": "මෙහෙයුම්", "Apis.Details.Overview.no.subscription.message": "දායකත්වයන්ට අවසර නැත", "Apis.Details.Overview.resources.show.more": "පරීක්ෂණය >>", "Apis.Details.Overview.resources.title": "සම්පත්", "Apis.Details.Overview.signin.subscribe.btn.link": "දායක වීමට පුරනය වන්න", "Apis.Details.Overview.sdk.generation.show.more": "තවත් පෙන්වන්න >>", "Apis.Details.Overview.subscribebtn.link": "දායක වන්න", "Apis.Details.Overview.subscriptions.title": "දායකත්වයන්", "Apis.Details.Overview.sdk.generation.description": "ඔබට මෘදුකාංග යෙදුමක් නිර්මාණය කිරීමට අවශ්‍ය නම්\n                                                     දායක වූ API පරිභෝජනය කිරීමට, ඔබට සේවාදායකයාගේ පැත්ත ජනනය කළ හැකිය\n                                                      සහාය දක්වන භාෂාවක් / රාමුවක් සඳහා SDK කර එය ආරම්භයක් ලෙස භාවිතා කරන්න\n                                                       මෘදුකාංග යෙදුම ලිවීමට යොමු වන්න.", "Apis.Details.Overview.sdk.generation.title": "SDK පරම්පරාව", "Apis.Details.TryOutConsole.access.token.tooltip": "ඔබට දැනට පවතින ප්‍රවේශ ටෝකනය භාවිතා කළ හැකිය, නැතහොත් ඔබට නව පරීක්ෂණ යතුරක් ජනනය කළ හැකිය.", "Apis.Details.Sdk.title": "මෘදුකාංග සංවර්ධන කට්ටල (SDKs)", "Apis.Details.index.comments": "අදහස්", "Apis.Details.index.documentation": "ප්‍රලේඛනය", "Apis.Details.Overview.subscribe.count.singular": "අයදුම්පත දායක වී ඇත.", "Apis.Details.Overview.subscribe.count.zero": "යෙදුම් දායකත්වයන් නොමැත.", "Apis.Details.Sdk.no.sdks.content": "මෙම API සඳහා SDKs නොමැත", "Apis.Details.Overview.subscribe.count.plural": "අයදුම්පත් දායක වී ඇත.", "Apis.Details.Sdk.no.sdks": "SDKs නැත", "Apis.Details.Social.EmbadCode": "එම්බාඩ්", "Apis.Details.index.sdk": "SDKs", "Apis.Details.index.overview": "දළ විශ්ලේෂණය", "Apis.Details.index.subscriptions": "දායකත්වයන්", "Apis.Details.index.try.out": "උත්සාහ කර බලන්න", "Apis.Listing.APIList.version": "පිටපත", "Apis.Listing.APIList.name": "නම", "Apis.Listing.APIList.policy": "ප්රතිපත්ති", "Apis.Details.Sdk.search.sdk": "SDK සොයන්න", "Apis.Details.index.all.apis": "සියලුම API", "Apis.Details.index.invalid.tenant.domain": "වලංගු නොවන කුලී වසම", "Apis.Details.Overview.subscribe.info": "දායකත්වය මඟින් ඔබට ප්‍රවේශ ටෝකන ලබා ගැනීමට සහ මෙම API ක්‍රියාත්මක කිරීම සඳහා සත්‍යාපනය කිරීමට ඉඩ ලබා දේ.", "Apis.Listing.APICardView.already.subscribed": "දායක වී ඇත", "Apis.Listing.APIList.id": "අයි.ඩී.", "Apis.Listing.ApiTableView.context": "සන්දර්භය", "Apis.Listing.ApiBreadcrumbs.apigroups.main": "API කණ්ඩායම්", "Apis.Listing.ApiTableView.error.loading": "API පූරණය කිරීමේදී දෝෂයකි", "Apis.Listing.ApiTableView.name": "නම", "Apis.Listing.ApiTableView.invalid.tenant.domain": "වලංගු නොවන කුලී වසම", "Apis.Listing.ApiTableView.rating": "ශ්‍රේණිගත කිරීම", "Apis.Listing.ApiTagCloud.title": "ටැග්", "Apis.Listing.ApiTableView.provider": "සපයන්නා", "Apis.Listing.ApiTableView.version": "පිටපත", "Apis.Listing.ApiThumb.by": "විසින්", "Apis.Listing.ApiThumb.context": "සන්දර්භය", "Apis.Listing.ApiThumb.version": "පිටපත", "Apis.Listing.DocThumb.sourceType": "ප්‍රභව වර්ගය:", "Apis.Listing.ApiThumb.by.colon": " :", "Apis.Listing.ApiTableView.type": "වර්ගය", "Apis.Listing.DocThumb.apiName": "Api නම", "Apis.Listing.Listing.ApiTagCloud.title": "ටැග් / API වර්ග", "Apis.Listing.Listing.apis.main": "ඒපීඅයි", "Apis.Listing.DocThumb.apiVersion": "Api අනුවාදය", "Apis.Listing.CategoryListingCategories.categoriesNotFound": "ප්‍රවර්ග සොයාගත නොහැක", "Apis.Listing.CategoryListingCategories.title": "API කාණ්ඩ", "Apis.Listing.Recommendations.invalid.tenant.domain": "වලංගු නොවන කුලී වසම", "Apis.Listing.NoApi.nodata.title": "ඒපීඅයි නොමැත", "Apis.Listing.Recommendations.name": "නම", "Apis.Listing.Recommendations.error.loading": "API පූරණය කිරීමේදී දෝෂයකි", "Apis.Listing.Recommendations.rating": "ශ්‍රේණිගත කිරීම", "Apis.Listing.StarRatingBar.user": "පරිශීලක", "Apis.Listing.NoApi.nodata.content": "දැන් පෙන්වීමට API නොමැත.", "Apis.Listing.StarRatingBar.not.rated": "වර්ගීකරණය කර නැත", "Apis.Listing.SubscriptionPolicySelect.subscribe": "දායක වන්න", "Apis.Listing.TagCloudListingTags.tagsNotFound": "API කණ්ඩායම් සොයාගත නොහැක", "Apis.Settings.SettingsBase.sub.header": "සංවර්ධක ද්වාර සැකසුම් බලන්න සහ වින්‍යාස කරන්න", "Apis.Listing.StarRatingBar.users": "පරිශීලකයින්", "Apis.Listing.TagCloudListingTags.allApis": "සියලුම ඇපිස්", "Apis.Listing.TagCloudListing.apigroups.main": "API කණ්ඩායම්", "Apis.Settings.SettingsBase.header": "සැකසුම්", "Apis.Settings.Alerts.connection.error": "විශ්ලේෂණ සේවාදායකයට සම්බන්ධ වීමට නොහැකි විය. කරුණාකර සම්බන්ධතාවය පරීක්ෂා කරන්න.", "Applications.Create.ApplicationFormHandler.edit.application.sub.heading": "මෙම යෙදුම සංස්කරණය කරන්න. නම, කෝටාව සහ ටෝකන වර්ගය අනිවාර්ය පරාමිතීන් වන අතර විස්තරය අත්‍යවශ්‍ය නොවේ", "Applications.Create.ApplicationFormHandler.Application.created.successfully": "යෙදුම සාර්ථකව නිර්මාණය කරන ලදි.", "Applications.Create.ApplicationFormHandler.app.name.required": "අයදුම්පත්රයේ නම අවශ්ය වේ", "Applications.Create.ApplicationFormHandler.edit.application.heading": "යෙදුම සංස්කරණය කරන්න", "Applications.Create.ApplicationFormHandler.cancel": "අවලංගු කරන්න", "Apis.Listing.TagCloudListingTags.title": "Api කණ්ඩායම්", "Applications.ApplicationFormHandler.app.updated.success": "යෙදුම සාර්ථකව යාවත්කාලීන කරන ලදි", "Apis.Listing.TableView.TableView.doc.flag": "[ලේඛනය]", "Applications.Create.ApplicationFormHandler.error.while.creating.the.application": "යෙදුම නිර්මාණය කිරීමේදී දෝෂයකි", "Applications.Create.ApplicationFormHandler.create.application.heading": "යෙදුමක් සාදන්න", "Applications.Details.InfoBar.application.deleted.successfully": "යෙදුම {නම success සාර්ථකව මකා දමන ලදි!", "Applications.Details.InfoBar.edit": "සංස්කරණය කරන්න", "Applications.Details.InfoBar.edit.text": "සංස්කරණය කරන්න", "Applications.Details.InfoBar.listing.resource.not.found": "සම්පත් නොවේ", "Applications.Details.InfoBar.delete": "මකන්න", "Applications.Create.ApplicationFormHandler.create.application.sub.heading": "නම, කෝටා සහ ටෝකන වර්ග පරාමිතීන් සපයන යෙදුමක් සාදන්න. විස්තරය අත්‍යවශ්‍ය නොවේ", "Applications.Details.InfoBar.text": "මකන්න", "Applications.Details.InfoBar.subscriptions": "දායකත්වයන්", "Applications.Details.Invoice.no.data.available": "ලබා ගත හැකි දත්ත නැත", "Applications.Details.InfoBar.throttling.tier": "තෙරපීමේ ස්ථරය", "Applications.Details.Invoice.close": "වසන්න", "Applications.Create.ApplicationFormHandler.save": "සුරකින්න", "Applications.Details.InfoBar.application.deleting.error": "යෙදුම {name delete මකාදැමීමේදී දෝෂයකි", "Applications.Create.Listing.add.new.application": "නව අයදුම්පතක් එක් කරන්න", "Applications.Details.SubscriptionTableData.delete": "මකන්න", "Applications.Details.Invoice.pending.invoice.data": "මෙම දායකත්වය සඳහා ඉතිරිව ඇති ඉන්වොයිස් දත්ත හමු නොවේ.", "Applications.Details.Overview.description": "විස්තර", "Applications.Details.SubscriptionTableData.delete.subscription.confirmation": "ඔබට දායකත්වය මකා දැමීමට අවශ්‍ය බව ඔබට විශ්වාසද?", "Applications.Details.Overview.token.type": "ටෝකන් වර්ගය", "Applications.Details.SubscriptionTableData.cancel": "අවලංගු කරන්න", "Applications.Details.SubscriptionTableData.edit.text": "සංස්කරණය කරන්න", "Applications.Details.SubscriptionTableData.delete.text": "මකන්න", "Applications.Details.SubscriptionTableData.update.throttling.policy": "වත්මන් දායකත්ව ස්ථරය:", "Applications.Details.SubscriptionTableData.update": "යාවත්කාලීන කරන්න", "Applications.Details.Overview.application.owner": "යෙදුම් හිමිකරු", "Applications.Details.Invoice.view.btn": "ඉන්වොයිසිය බලන්න", "Applications.Details.SubscriptionTableData.update.subscription": "දායකත්වය යාවත්කාලීන කරන්න", "Applications.Details.SubscriptionTableData.update.throttling.policy.name": "තෙරපීමේ ස්ථරය", "Applications.Details.SubscriptionTableData.update.throttling.policy.rejected": "දායකත්වය දැනට ප්‍රතික්ෂේප කර ඇත. ස්ථරය සංස්කරණය කිරීමට පෙර ඔබ දායකත්වයට අනුමැතිය ලබා ගත යුතුය", "Applications.Details.Overview.workflow.status": "කාර්ය ප්‍රවාහ තත්වය", "Applications.Details.SubscriptionTableData.update.throttling.policy.onHold": "දායකත්වය දැනට ON_HOLD තත්වයේ පවතී. ස්ථරය සංස්කරණය කිරීමට පෙර ඔබ දායකත්වයට අනුමැතිය ලබා ගත යුතුය", "Applications.Details.SubscriptionTableData.update.throttling.policy.tier.update": "ස්ථර යාවත්කාලීන කිරීම අපේක්ෂිතය:", "Applications.Details.Subscriptions\n                                                                        .subscription.state": "ජීවන චක්‍රීය තත්වය", "Applications.Details.Subscriptions.Status": "දායකත්ව තත්වය", "Applications.Details.Subscriptions.action": "කටයුතු", "Applications.Details.Subscriptions.error.occurred.during.subscription.not.201": "දායකත්වය අතරතුර දෝෂයක් ඇතිවිය", "Applications.Details.Subscriptions.error.occurred.during.subscription": "දායකත්වය අතරතුර දෝෂයක් ඇතිවිය", "Applications.Details.Subscriptions.filter.msg.all.apis": "සියලුම API පෙන්වයි", "Applications.Details.SubscriptionTableData.update.throttling.policy.blocked": "දායකත්වය අවහිර කර ඇත. ස්ථරය සංස්කරණය කිරීම සඳහා ඔබ දායකත්ව අක්‍රීය කිරීම අවහිර කිරීම අවශ්‍ය වේ", "Applications.Details.Subscriptions.no.subscriptions.content": "මෙම යෙදුම සඳහා දායකත්වයන් නොමැත", "Applications.Details.Subscriptions.select.a.subscription.policy": "දායකත්ව ප්‍රතිපත්තියක් තෝරන්න", "Applications.Details.Subscriptions.api.name": "API", "Applications.Details.Subscriptions.search": "API සොයන්න", "Applications.Details.Subscriptions.subscription.management.add": "ඒපීඅයි දායක වන්න", "Applications.Details.api.keys.title": " API යතුර", "Applications.Details.menu.overview": "දළ විශ්ලේෂණය", "Applications.Details.menu.subscriptions": "දායකත්වයන්", "Applications.Listing.ApplicationTableHead.owner": "හිමිකරු", "Applications.Edit.app.update.error.no.required.attribute": "කරුණාකර අවශ්‍ය සියලුම යෙදුම් ගුණාංග පුරවන්න", "Applications.Listing.ApplicationTableHead.actions": "ක්‍රියා", "Applications.Details.menu.sandbox.keys": "සෑන්ඩ්බොක්ස් යතුරු", "Applications.Listing.ApplicationTableHead.name": "නම", "Applications.Details.Subscriptions.no.subscriptions": "දායකත්වයන් නොමැත", "Applications.Details.Subscriptions.subscription.management": "දායක කළමනාකරණය", "Applications.Details.Subscriptions.filter.msg": "සඳහා පෙරහන් කළ API", "Applications.Details.SubscriptionTableData.update.throttling.policy.helper": "පවත්නා දායකත්වයට නව තෙරපුම් ප්‍රතිපත්ති ස්ථරයක් පවරන්න", "Applications.Details.oauth2.keys.main.title": " OAuth2 යතුරු", "Applications.Details.menu.oauth.tokens": "OAuth2 ටෝකන", "Applications.Details.menu.prod.keys": "නිෂ්පාදන යතුරු", "Applications.Details.menu.api.key": "Api Key", "Applications.Details.Subscriptions.subscription.successful": "දායකත්වය සාර්ථකයි", "Applications.Details.applications.all": "සියලුම යෙදුම්", "Applications.Listing.ApplicationTableHead.subscriptions": "දායකත්වයන්", "Applications.Listing.ApplicationTableHead.policy": "ප්රතිපත්ති", "Applications.Listing.AppsTableContent.active": "ක්‍රියාකාරී", "Applications.Listing.AppsTableContent.delete.tooltip": "මකන්න", "Applications.Listing.DeleteConfirmation.dialog,delete": "මකන්න", "Applications.Listing.AppsTableContent.wait.approval": "අනුමැතිය සඳහා බලා සිටී", "Applications.Listing.Listing.application.deleted.successfully": "යෙදුම {නම success සාර්ථකව මකා දමන ලදි!", "Applications.Listing.Listing.applications": "අයදුම්පත්", "Applications.Listing.Listing.application.deleting.error": "යෙදුම {name delete මකාදැමීමේදී දෝෂයකි", "Applications.Listing.Listing.applications.no.search.results.title": "ගැලපෙන යෙදුම් නොමැත", "Applications.Listing.AppsTableContent.inactive": "අක්‍රීයයි", "Applications.Listing.AppsTableContent.rejected": "ප්‍රතික්ෂේප කරන ලදි", "Applications.Listing.Listing.applications.no.search.results.body.sufix": "සෙවීම ඉවත් කරන්න", "Applications.Listing.Listing.applications.search": "සෙවීම", "Applications.Listing.Listing.logical.description": "යෙදුමක් යනු API වල තාර්කික එකතුවකි.\n                                        ආයාචනා කිරීම සඳහා තනි ප්‍රවේශ ටෝකනයක් භාවිතා කිරීමට යෙදුම් ඔබට ඉඩ දෙයි\n                                         API එකතු කිරීම සහ එක් API එකකට කිහිප වතාවක් දායක වීම\n                                          සහ පෙරනිමියෙන් අසීමිත ප්‍රවේශයට ඉඩ දෙයි.", "Applications.Listing.DeleteConfirmation.dialog.cancel": "අවලංගු කරන්න", "Applications.Listing.Listing.applications.no.search.results.body.prefix": "අක්ෂර වින්‍යාසය පරීක්ෂා කරන්න හෝ උත්සාහ කරන්න", "Base.Errors.ResourceNotfound.default_body": "ඔබ සොයන පිටුව නොමැත", "Applications.Listing.Listing.noapps.display.link.text": "නව අයදුම්පතක් එක් කරන්න", "Base.Errors.ResourceNotFound.applications": "අයදුම්පත්", "Base.Header.GlobalNavbar.menu.applications": "අයදුම්පත්", "Base.Header.GlobalNavbar.menu.apis": "ඒපීඅයි", "Base.Header.headersearch.HeaderSearch.tooltip.option1": "API නම අනුව [පෙරනිමි]", "Applications.Listing.DeleteConfirmation.dialog.title": "යෙදුම මකන්න", "Applications.Listing.Listing.noapps.display.title": "අයදුම්පත් නොමැත", "Applications.Listing.DeleteConfirmation.dialog.text.description": "යෙදුම ඉවත් කරනු ලැබේ", "Base.Header.headersearch.HeaderSearch.tooltip.option6": "ටැග් මගින් [සින්ටැක්ස් - ටැග්: xxxx]", "Applications.Listing.Listing.clear.search": "සෙවීම හිස් කරන්න", "Base.Errors.ResourceNotfound.default_tittle": "පිටුව හමු නොවීය", "Base.Errors.SubscriptionNotFound.default_title": "API වෙත දායකත්වයක් නොමැතිව Solace Info පිටුව සංදර්ශණය නොවේ. කරුණාකර API වෙත දායක වන්න", "Base.Header.headersearch.HeaderSearch.search_api.tooltip": "API සොයන්න", "Base.Header.GlobalNavbar.menu.home": "නිවස", "Base.Header.headersearch.HeaderSearch.tooltip.title": "සෙවුම් විකල්ප", "Base.Header.headersearch.HeaderSearch.tooltip.option5": "විස්තරය අනුව [සින්ටැක්ස් - විස්තරය: xxxx]", "Base.Header.headersearch.HeaderSearch.tooltip.option4": "සන්දර්භය අනුව [සින්ටැක්ස් - සන්දර්භය: xxxx]", "Base.Header.headersearch.HeaderSearch.tooltip.option7": "උප-සන්දර්භය අනුව [සින්ටැක්ස් - උපකොන්ටෙක්ස්ට්: xxxx]", "Base.Errors.ResourceNotFound.api.list": "API ලැයිස්තුව", "Base.Errors.ResourceNotFound.more.links": "ඔබට පහත සබැඳි පරීක්ෂා කළ හැකිය", "Base.Header.headersearch.SearchUtils.lcState.published": "නිෂ්පාදනය", "Base.Header.headersearch.SearchUtils.lcState.all": "සියලුම", "Base.index.copyright.text": "WSO2 API-M v4.1.0 | © 2022 WSO2 LLC", "Base.Header.headersearch.SearchUtils.lcState.prototyped": "මූලාකෘති", "Base.Header.headersearch.HeaderSearch.tooltip.option2": "API සපයන්නා විසින් [සින්ටැක්ස් - සැපයුම්කරු: xxxx]", "Base.Header.headersearch.HeaderSearch.tooltip.option3": "API අනුවාදය අනුව [සින්ටැක්ස් - අනුවාදය: xxxx]", "Base.index.banner.alt": "දේව් ද්වාර බැනරය", "Base.index.go.to.public.store": "පොදු දේව් ද්වාරයට යන්න", "Base.Header.headersearch.HeaderSearch.tooltip.option10": "API ගුණාංග අනුව [සින්ටැක්ස් - දේපල_ නාමය: දේපල_ අගය]", "Base.Header.headersearch.HeaderSearch.tooltip.option9": "මයික්‍රොගේට්වේ ලේබලය මගින් [සින්ටැක්ස් - ලේබලය: xxxx]", "Change.Password.password.empty": "මුරපදය හිස් ය", "Change.Password.password.change.disabled": "මුරපද වෙනස් කිරීම අක්‍රීය කර ඇත", "Base.index.settingsMenu.alertConfiguration": "ඇඟවීම් වින්‍යාස කරන්න", "Change.Password.password.changed.success": "මුරපදය සාර්ථකව වෙනස් කළේය", "Change.Password.password.length.short": "මුරපදය ඉතා කෙටි ය!", "Base.index.logout": "ලොග්අවුට්", "Base.index.logo.alt": "දේව් ද්වාරය", "LandingPage.ApisWithTag.invalid.tenant.domain": "වලංගු නොවන කුලී වසම", "Base.index.sign.in": " පුරන්න", "Change.Password.password.mismatch": "මුරපදය නොගැලපේ", "Change.Password.current.password.incorrect": "වත්මන් මුරපදය වැරදිය", "Change.Password.password.pattern.invalid": "අවලංගු මුරපද රටාව", "Change.Password.password.length.long": "මුරපදය දිග වැඩිය!", "Change.Password.password.policy": "මුරපද ප්‍රතිපත්තිය:", "LoginDenied.title": "දෝෂය 403: තහනම්", "Login.RedirectToLogin.you.will.be.redirected.to": "ඔබව {පිටුව to වෙත හරවා යවනු ලැබේ", "Settings.Alert.AlertConfiguration.add": "නව වින්‍යාසය", "Change.Password.title": "මුරපදය වෙනස් කරන්න", "LoginDenied.anonymousview": "පොදු ද්වාරයට යන්න", "Change.Password.description": "ඔබගේ මුරපදය වෙනස් කරන්න. අවශ්‍ය ක්ෂේත්‍ර තරු ලකුණු කර ඇත (*)", "LoginDenied.logout": "ලොග්අවුට්", "LoginDenied.message": "සංවර්ධක ද්වාරයට පිවිසීමට ඔබට ප්‍රමාණවත් වරප්‍රසාද නොමැත.", "Settings.Alert.AlertConfiguration.alert.config.add.success.msg": "ඇඟවීම් වින්‍යාසය සාර්ථකව එක් කරන ලදි", "Settings.Alert.AlertConfiguration.alert.config.add.error.msg": "ඇඟවීම් වින්‍යාසය එකතු කිරීමේදී දෝෂයක් ඇතිවිය", "Settings.Alert.AlertConfiguration.alert.config.delete.success.msg": "ඇඟවීම් වින්‍යාසය සාර්ථකව මකා දමන ලදි", "Settings.Alert.AlertConfiguration.alert.config.delete.error.msg": "වින්‍යාසය මකාදැමීමේදී දෝෂයක් ඇතිවිය.", "Settings.Alerts.AlertConfiguration.api.version.label": "API අනුවාදය", "Settings.Alerts.AlertConfiguration.applications.label": "අයදුම්පත", "Settings.Alerts.AlertConfiguration.request.count.label": "ඉල්ලීම් ගණන.", "Settings.Alerts.AlertConfiguration.api.name": "API නම", "Settings.Alerts.AlertConfiguration.app.name": "අයදුම්පත්රයේ නම", "Settings.Alerts.AlertConfiguration.api.name.label": "API නම", "Settings.Alerts.AlertConfiguration.api.version": "API අනුවාදය", "Settings.Alerts.AlertConfiguration.no.config.message": "ඔබට කිසිදු වින්‍යාසයක් නොමැත. වින්‍යාසයක් එක් කිරීමට {newConfig} බොත්තම ක්ලික් කරන්න.", "Settings.Alerts.Alerts.abnormal.request.pattern.description": "කිසියම් යෙදුමක පරිශීලකයෙකුගේ සම්පත් ප්‍රවේශ රටාවේ වෙනසක් තිබේ නම් මෙම ඇඟවීම ක්‍රියාත්මක වේ. මෙම ඇඟවීම් ඔබගේ යෙදුම හරහා පරිශීලකයෙකු විසින් සිදු කරන ලද සැක සහිත ක්‍රියාකාරකමක ඇඟවීමක් ලෙස සැලකිය හැකිය.", "Settings.Alerts.Alerts.close.btn": "වසන්න", "Settings.Alerts.Alerts.abnormal.backend.time": "අසාමාන්‍ය සම්පත් ප්‍රවේශය", "Settings.Alerts.AlertConfiguration.select.version.helper": "API අනුවාදය තෝරන්න", "Settings.Alerts.AlertConfiguration.select.application.helper": "යෙදුම තෝරන්න", "Settings.Alerts.AlertConfiguration.threshold.value.helper": "ඉල්ලීම් ගණන ඇතුළත් කරන්න.", "Settings.Alerts.AlertConfiguration.configuration": "{name} වින්‍යාස කිරීම්", "Settings.Alerts.AlertConfiguration.select.api.helper": "API නම තෝරන්න", "Settings.Alerts.Alerts.frequent.tier": "නිරන්තර ස්ථර සීමාව පහර", "Settings.Alerts.Alerts.cancel.btn": "අවලංගු කරන්න", "Settings.Alerts.Alerts.subscribe.success.msg": "ඇඟවීම් සඳහා දායක වී ඇත.", "Settings.Alerts.Alerts.subscribe.to.alerts.heading": "ඇඟවීම් දායකත්වයන් කළමනාකරණය කරන්න", "Settings.Alerts.Alerts.confirm.btn": "සියල්ල ඉවත් කරන්න", "Settings.Alerts.Alerts.abnormal.response.time": "විනාඩියකට අසාමාන්‍ය ඉල්ලීම්", "Settings.Alerts.Alerts.configure.alert": "වින්‍යාස කිරීම්", "Settings.Alerts.Alerts.enable.analytics.message": "ඇඟවීම් වින්‍යාස කිරීමට විශ්ලේෂණ සක්‍රීය කරන්න", "Settings.Alerts.Alerts.loading.error.msg": "ඇඟවීම් පූරණය කිරීමේදී දෝෂයක් ඇතිවිය", "Settings.Alerts.Alerts.abnormal.request.per.min.description": "යෙදුමක් සඳහා විශේෂිත API එකක් සඳහා පෙරනිමියෙන් විනාඩියක කාලයක් තුළ ඉල්ලීම් ගණන හදිසියේ වැඩි වුවහොත් මෙම අනතුරු ඇඟවීම ක්‍රියාත්මක වේ. මෙම අනතුරු ඇඟවීම් ඉහළ තදබදයක්, සැක සහිත ක්‍රියාකාරකමක්, සේවාදායක යෙදුමේ අක්‍රමිකතා ආදිය පිළිබඳ ඇඟවීමක් ලෙස සැලකිය හැකිය.", "Settings.ChangePasswordForm.Cancel.Button.text": "අවලංගු කරන්න", "Settings.ChangePasswordForm.new.password": "නව මුරපදය", "Settings.Alerts.Alerts.unsubscribe.confirm.dialog.heading": "සියලුම ඇඟවීම් වලින් දායක නොවීම තහවුරු කරන්න", "Settings.ChangePasswordForm.Save.Button.text": "සුරකින්න", "Settings.Alerts.Alerts.numusual.ip": "අසාමාන්‍ය IP ප්‍රවේශය", "Settings.Alerts.Alerts.subscribe.error.msg": "ඇඟවීම් වලට දායක වන විට දෝෂයක් ඇතිවිය.", "Settings.ChangePasswordForm.confirm.new.password": "නව මුරපදය තහවුරු කරන්න", "Settings.ChangePasswordForm.confirmationOf.new.password": "නව මුරපදය තහවුරු කිරීම", "Settings.ChangePasswordForm.enter.new.password": "නව මුරපදයක් ඇතුළත් කරන්න", "Settings.ChangePasswordForm.current.password": "වත්මන් මුරපදය", "Settings.Alerts.Alerts.unsubscribe.error.msg": "දායක නොවීමේදී දෝෂයක් ඇතිවිය.", "Settings.Alerts.Alerts.tier.limit.hitting.description": "පහත දැක්වෙන අවස්ථා දෙකෙන් එකක්වත් සෑහීමකට පත්වේ නම් මෙම අනතුරු ඇඟවීම ක්‍රියාත්මක වේ. පැයක් ඇතුළත (පෙරනිමියෙන්) 10 වතාවක් (පෙරනිමියෙන්) එම යෙදුමේ දායකත්ව ස්ථර සීමාවට පහර දීම සඳහා කිසියම් යෙදුමක් තල්ලු වුවහොත් හෝ යෙදුමක නිශ්චිත පරිශීලකයකුගේ දායකත්ව ස්ථර සීමාවට පහර දීම නිසා තල්ලු වුවහොත් විශේෂිත API දිනක් තුළ (පෙරනිමියෙන්) 10 වතාවකට වඩා (පෙරනිමියෙන්)", "Settings.Alerts.Alerts.unsubscribe.confirm.dialog.message": "මෙය දැනට පවතින සියලුම ඇඟවීම් දායකත්වයන් සහ විද්‍යුත් තැපැල් ඉවත් කරනු ඇත. මෙම ක්‍රියාව අහෝසි කළ නොහැක.", "Settings.Alerts.Alerts.subscribe.to.alerts.subheading": "දායක වීමට / දායක නොවීමට ඇඟවීම් වර්ග තෝරන්න සහ සුරකින්න ක්ලික් කරන්න.", "Settings.ChangePasswordForm.enter.current.password": "වත්මන් මුරපදය ඇතුළත් කරන්න", "Shared.AppsAndKeys.ApplicationCreateForm.add.groups.label": "යෙදුම් කණ්ඩායම්", "Settings.Alerts.Alerts.unusual.ip.access.description": "පරිශීලකයෙකු විසින් කිසියම් යෙදුමක් සඳහා ඉල්ලීම් ප්‍රභව IP හි වෙනසක් සිදුවී ඇත්නම් හෝ ඉල්ලීම දින 30 ක කාල සීමාවකට පෙර භාවිතා කරන ලද IP එකකින් (පෙරනිමිය) තිබේ නම් මෙම ඇඟවීම ක්‍රියාත්මක වේ. යෙදුමක් හරහා පරිශීලකයෙකු විසින් සිදු කරන ලද සැක සහිත ක්‍රියාකාරකමක ඇඟවීමක් ලෙස මෙම ඇඟවීම් සැලකිය හැකිය.", "Shared.AppsAndKeys.ApplicationCreateForm.describe.the.application.help": "යෙදුම විස්තර කරන්න", "Shared.AppsAndKeys.ApplicationCreateForm.my.mobile.application.placeholder": "මගේ ජංගම යෙදුම", "Shared.AppsAndKeys.ImportExternalApp.key.provide.user.owner": "යතුරු සැපයිය හැක්කේ හිමිකරුට පමණි", "Shared.AppsAndKeys.ApplicationCreateForm.application.description.label": "අයදුම්පත් විස්තරය", "Shared.AppsAndKeys.ImportExternalApp.consumer.key.title": "OAuth යෙදුමේ පාරිභෝගික යතුර", "Shared.ApiKeyRestriction.key.restrictions": "යතුරු සීමාවන්", "Shared.AppsAndKeys.ApplicationCreateForm.enter.a.name": "යෙදුම හඳුනා ගැනීමට නමක් ඇතුළත් කරන්න.\n                                    API වලට දායක වන විට ඔබට මෙම යෙදුම තෝරා ගැනීමට හැකි වේ", "Shared.AppsAndKeys.ApiKeyManager.generate.key.help": "ස්වයං අන්තර්ගත JWT ටෝකනයක් ජනනය කිරීමට උත්පාදක යතුරු බොත්තම භාවිතා කරන්න.", "Shared.AppsAndKeys.ApplicationCreateForm.application.name": "අයදුම්පත්රයේ නම", "Shared.AppsAndKeys.ImportExternalApp.cancel": "අවලංගු කරන්න", "Shared.AppsAndKeys.ApplicationCreateForm.per.token.quota": "ටෝකන් කෝටාවකට.", "Shared.AppsAndKeys.ImportExternalApp.consumer.key": "පාරිභෝගික යතුර", "Shared.AppsAndKeys.ApplicationCreateForm.my.mobile.application": "මගේ අයදුම්පත", "Shared.AppsAndKeys.ApplicationCreateForm.type.a.group.and.enter": "කණ්ඩායමක් ටයිප් කර ඇතුල් කරන්න", "Shared.AppsAndKeys.ImportExternalApp.provide.\n                                                    oauth.button.update": "යාවත්කාලීන කරන්න", "Shared.AppsAndKeys.ApplicationCreateForm.assign.api.request": "ප්‍රවේශ ටෝකනයකට API ඉල්ලීම් කෝටාව පවරන්න.\n                            වෙන් කළ කෝටාව සියලු දෙනා අතර බෙදා ගැනේ\n                            යෙදුමේ දායක වූ API.", "Shared.AppsAndKeys.ImportExternalApp.provide.oauth.button.update": "යාවත්කාලීන කරන්න", "Shared.AppsAndKeys.ImportExternalApp.provide.oauth": "පවතින OAuth යතුරු ලබා දෙන්න", "Shared.AppsAndKeys.ImportExternalApp.consumer.secret": "පාරිභෝගික රහස", "Shared.AppsAndKeys.ImportExternalApp.consumer.secret.of.application": "OAuth යෙදුමේ පාරිභෝගික රහස", "Shared.AppsAndKeys.KeyConfiguration.copied": "පිටපත් කරන ලදි", "Shared.AppsAndKeys.KeyConfCiguration.Invalid.callback.empty.error.text": "ව්‍යංග හෝ බලය පැවරීමේ කේත ප්‍රදාන තෝරාගත් විට නැවත ඇමතුම් url හිස් විය නොහැක.", "Shared.AppsAndKeys.KeyConfCiguration.Invalid.callback.url.error.text": "වලංගු නැති URL. වලංගු URL එක ඇතුලත් කරන්න.", "Shared.AppsAndKeys.ImportExternalApp.provide.oauth.button.provide": "සපයන්න", "Shared.AppsAndKeys.KeyConfCiguration.callback.url.helper.text": "ඇමතුම් ආපසු URL යනු සේවාදායකයා තුළ යළි හරවා යැවීමේ URI ය\n                            යැවීමට බලයලත් සේවාදායකයා විසින් භාවිතා කරන යෙදුම\n                            ප්‍රවේශය ලබා දීමෙන් පසු සේවාදායකයාගේ පරිශීලක-නියෝජිතයා (සාමාන්‍යයෙන් වෙබ් බ්‍රව්සරය).", "Shared.AppsAndKeys.KeyConfiguration.revoke.endpoint.label": "එන්ඩ්පොයින්ට් අවලංගු කරන්න", "Shared.AppsAndKeys.KeyConfiguration.grant.types": "ප්‍රදාන වර්ග", "Shared.AppsAndKeys.KeyConfiguration.token.endpoint.label": "ටෝකන් එන්ඩ්පොයින්ට්", "Shared.AppsAndKeys.KeyConfiguration.userinfo.endpoint.label": "පරිශීලක තොරතුරු එන්ඩ්පොයින්ට්", "Shared.AppsAndKeys.KeyConfiguration.callback.url.label": "ඇමතුම් ආපසු URL", "Shared.AppsAndKeys.KeyConfiguration.copy.to.clipboard": "ක්ලිප් පුවරුවට පිටපත් කරන්න", "Shared.AppsAndKeys.KeyConfiguration.the.application.can": "උත්පාදනය සඳහා යෙදුමට පහත දැක්වෙන ප්‍රදාන වර්ග භාවිතා කළ හැකිය\n                            ටෝකන වෙත ප්‍රවේශ වන්න. යෙදුම් අවශ්‍යතාවය මත පදනම්ව, ඔබට සක්‍රිය හෝ අක්‍රීය කළ හැකිය\n                            මෙම යෙදුම සඳහා වර්ග ලබා දෙන්න.", "Shared.AppsAndKeys.TokenManager.cleanup.text": "දෝෂයකි! ඔබ අර්ධ වශයෙන් සාදන ලද යතුරු ඇත.\n                            කරුණාකර `පිරිසිදු කරන්න 'බොත්තම ක්ලික් කර නැවත උත්සාහ කරන්න.", "Shared.AppsAndKeys.KeyConfiguration.url.to.webapp": "http: // url-to-webapp", "Shared.AppsAndKeys.SubscribeToApi.available.policies": "පවතින ප්‍රතිපත්ති -", "Shared.AppsAndKeys.SubscribeToApi.application": "අයදුම්පත", "Shared.AppsAndKeys.TokenManager.key.and.secret": "යතුර සහ රහස", "Shared.AppsAndKeys.TokenManager.key.configuration": "යතුරු වින්‍යාසය", "Shared.AppsAndKeys.TokenManager.cleanup": "ඉවර කරන්න", "Shared.AppsAndKeys.TokenManager.key.cleanup.success": "යෙදුම් යතුරු සාර්ථකව පිරිසිදු කර ඇත", "Shared.AppsAndKeys.TokenManager.key.update.error": "යෙදුම් යතුරු යාවත්කාලීන කිරීමේදී දෝෂයක් ඇතිවිය", "Shared.AppsAndKeys.SubscribeToApi.select.an.application.to.subscribe": "දායක වීමට යෙදුමක් තෝරන්න", "Shared.AppsAndKeys.TokenManager.key.and.user.owner": "යතුරු ජනනය කිරීමට හෝ යාවත්කාලීන කිරීමට අයිතිකරුට පමණි", "Shared.AppsAndKeys.SubscribeToApi.throttling.policy": "තෙරපීමේ ප්‍රතිපත්තිය", "Shared.AppsAndKeys.TokenManager.key.generate.error.callbackempty": "ව්‍යංග හෝ යෙදුම් කේත ප්‍රදාන වර්ග තෝරාගත් විට නැවත ඇමතුම් URL හිස් විය නොහැක", "Shared.AppsAndKeys.TokenManager.key.cleanup.error": "යෙදුම් යතුරු පිරිසිදු කිරීමේදී දෝෂයක් ඇතිවිය", "Shared.AppsAndKeys.Tokens.apiKeyRestriction.enter.referer": "Http Referrer ඇතුලත් කරන්න", "Shared.AppsAndKeys.TokenManager.key.provide.error": "යෙදුම් යතුරු ලබා දීමේදී දෝෂයක් ඇතිවිය", "Shared.AppsAndKeys.TokenManager.key.generate.error": "යෙදුම් යතුරු ජනනය කිරීමේදී දෝෂයක් ඇතිවිය", "Shared.AppsAndKeys.Tokens.apiKeyRestriction.enter.ip": "IP ලිපිනය ඇතුළත් කරන්න", "Shared.AppsAndKeys.Tokens.apiKeyRestriction.referer.validity.error": "අවලංගු Http Referrer", "Shared.AppsAndKeys.TokenManager.key.generate.success": "යෙදුම් යතුරු සාර්ථකව ජනනය කර ඇත", "Shared.AppsAndKeys.Tokens.apikey": "API යතුරු වලංගු කාලය", "Shared.AppsAndKeys.TokenManager.key.provide.success": "යෙදුම් යතුරු සාර්ථකව සපයා ඇත", "Shared.AppsAndKeys.Tokens.apiKeyRestriction.ip.validity.error": "අවලංගු IP ලිපිනය", "Shared.AppsAndKeys.TokenManager.update.configuration": "යතුරු වින්‍යාස කිරීම", "Shared.AppsAndKeys.TokenManager.key.update.success": "යෙදුම් යතුරු සාර්ථකව යාවත්කාලීන කරන ලදි", "Shared.AppsAndKeys.TokenManagerSummary": "දෝෂයකි! ඔබ අර්ධ වශයෙන් සාදන ලද යතුරු ඇත. `පිරිසිදු කිරීම 'විකල්පය භාවිතා කරන්න.", "Shared.AppsAndKeys.Tokens.apikey.set.validity.error": "API යතුරු කල් ඉකුත් වීමේ කාලය සඳහා කරුණාකර වලංගු අංකයක් භාවිතා කරන්න", "Shared.AppsAndKeys.Tokens.when.you.generate": "ඔබ විෂය පථයෙන් ආරක්ෂා කර ඇති ඒපීඅයි වෙත ප්‍රවේශ ටෝකන ජනනය කරන විට, ඔබට විෂය පථය / ය තෝරාගෙන ඒ සඳහා ටෝකනය ජනනය කළ හැකිය. පරිශීලක භූමිකාවන් මත පදනම්ව API සම්පත් වෙත සියුම් ප්‍රවේශ ප්‍රවේශයන් විෂය පථ මඟින් සක්‍රීය කරයි. ඔබ API සම්පතක් සඳහා විෂය පථයන් අර්ථ දක්වයි. පරිශීලකයෙකු API සඳහා ආයාචනා කරන විට, ඔහුගේ / ඇයගේ OAuth 2 දරන්නා ටෝකනයට ඒ හා සම්බන්ධ විෂය පථයන්ගෙන් ඔබ්බට කිසිදු API සම්පතක් වෙත ප්‍රවේශය ලබා දිය නොහැක.", "Shared.AppsAndKeys.Tokens.apikey.set.validity.help": "පරම්පරාවෙන් පසු ටෝකනයේ වලංගු කාලය තීරණය කිරීම සඳහා ඔබට කල් ඉකුත් වීමේ කාල සීමාවක් නියම කළ හැකිය. Apikey කිසි විටෙකත් කල් ඉකුත් නොවන බව සහතික කිරීම සඳහා මෙය -1 ලෙස සකසන්න.", "Shared.AppsAndKeys.ViewCurl.copied": "පිටපත් කරන ලදි", "Shared.AppsAndKeys.Tokens.when.you.generate.scopes": "විෂය පථ", "Shared.AppsAndKeys.Tokens.apikey.enter.time": "තත්පර කිහිපයකින් කාලය ඇතුළත් කරන්න", "Shared.AppsAndKeys.ViewCurl.help.in.a.similar": "ඒ හා සමානව, ඔබට භාවිතයෙන් ප්‍රවේශ ටෝකනයක් ජනනය කළ හැකිය\n                    සේවාලාභී අක්තපත්‍ර ප්‍රදානය වර්ගය පහත දැක්වෙන CURL විධානය සමඟ.", "Shared.AppsAndKeys.ViewKeys.consumer.generate.btn": "ජනනය කරන්න", "Shared.AppsAndKeys.ViewCurl.copy.to.clipboard": "ක්ලිප් පුවරුවට පිටපත් කරන්න", "Shared.AppsAndKeys.ViewKeys.apiKeyRestriction.ip.example.content.message": "නිශ්චිත මාර්ගයක් සහිත නිශ්චිත URL: {url1 {{linebreak wild වයිල්ඩ්කාඩ් තරු ලකුණු භාවිතා කරමින් තනි උප ඩොමේනයක ඇති ඕනෑම URL එකක් (*): {url2 {{linebreak wild තනි වසමක ඇති ඕනෑම උප ඩොමේන් හෝ මාර්ග URL, වයිල්ඩ්කාඩ් තරු ලකුණු භාවිතා කරමින් (* ): {url3}", "Shared.AppsAndKeys.ViewKeys.consumer.key": "පාරිභෝගික යතුර", "Shared.AppsAndKeys.ViewKeys.consumer.close.btn": "වසන්න", "Shared.AppsAndKeys.ViewKeys.copied": "පිටපත් කරන ලදි", "Shared.AppsAndKeys.ViewKeys.apiKeyRestriction.ip.example.heading": "IP ලිපින සඳහා උදාහරණ අවසර ඇත", "Shared.AppsAndKeys.ViewCurl.help": "පහත දැක්වෙන CURL විධානය මඟින් ප්‍රවේශ ටෝකනයක් භාවිතා කරන්නේ කෙසේද යන්න පෙන්වයි\n                            මුරපද ප්‍රදාන වර්ගය.", "Shared.AppsAndKeys.ViewKeys.key.secret.title": "මෙම යෙදුම සඳහා යතුර සහ රහස ජනනය නොවේ", "Shared.AppsAndKeys.ViewKeys.apiKeyRestriction.ip.example.content": "CIDR අංකනය භාවිතා කරමින් එක් IPv4 හෝ IPv6 හෝ උප ජාලයක් සඳහන් කරන්න {linebreak} උදාහරණ: {ip1}, {ip2}, {ip3} හෝ {ip4}", "Shared.AppsAndKeys.ViewKeys.curl.to.generate": "ප්‍රවේශ ටෝකනය ජනනය කිරීමට CURL කරන්න", "Shared.AppsAndKeys.ViewKeys.access.token": "ටෝකනයට ප්‍රවේශ වන්න", "Shared.AppsAndKeys.ViewKeys.consumer.secret.of.application": "යෙදුමේ පාරිභෝගික රහස", "Shared.AppsAndKeys.ViewKeys.consumer.secret.button.regenerate": "පාරිභෝගික රහස නැවත උත්පාදනය කරන්න", "Shared.AppsAndKeys.ViewKeys.consumer.secret": "පාරිභෝගික රහස", "Shared.AppsAndKeys.ViewSecret.consumer.secret": "පාරිභෝගික රහස", "Shared.AppsAndKeys.ViewKeys.apiKeyRestriction.referer.example.heading": "වෙබ් අඩවි සීමා කිරීමට ඉඩ දී ඇති URL වල උදාහරණ", "Shared.AppsAndKeys.ViewToken.info.third": " සහ ටෝකනයට (", "Shared.AppsAndKeys.ViewKeys.generate.access.token": "ප්‍රවේශ ටෝකනය ජනනය කරන්න", "Shared.AppsAndKeys.ViewKeys.copy.to": "ක්ලිප් පුවරුවට පිටපත් කරන්න", "Shared.AppsAndKeys.ViewKeys.consumer.key.title": "යෙදුමේ පාරිභෝගික යතුර", "Shared.AppsAndKeys.ViewSecret.please.copy.secret": "කරුණාකර පාරිභෝගික රහස පිටපත් කරන්න", "Shared.AppsAndKeys.ViewToken.please.copy.help": "කරුණාකර මෙම උත්පාදනය කළ ටෝකන අගය වත්මන් බ්‍රව්සර් සැසිය සඳහා පමණක් දර්ශනය වන බැවින් එය පිටපත් කරන්න. (පිටුව නැවුම් කළ පසු ටෝකනය UI හි නොපෙනේ.)", "Shared.AppsAndKeys.ViewToken.info.first": "ටෝකනයට ඉහළින් වලංගු කාල සීමාවක් ඇත", "Shared.AppsAndKeys.ViewKeys.client.enable.client.credentials": "පරීක්ෂණ ප්‍රවේශ ටෝකන ජනනය කිරීම සඳහා සේවාලාභී අක්තපත්‍ර ප්‍රදාන වර්ගය සක්‍රීය කරන්න", "Shared.AppsAndKeys.ViewToken.info.second": " තත්පර", "Shared.AppsAndKeys.ViewSecret.please.copy.secret.help": "කරුණාකර පුනර්ජනනීය පාරිභෝගිකයා පිළිබඳ සටහනක් තබන්න\n                            රහස් අගය දර්ශනය වන්නේ එක් වරක් පමණි.", "Shared.AppsAndKeys.ViewToken.apikey": "API යතුර", "Shared.AppsAndKeys.WaitingForApproval.msg.ok": "මෙම අයදුම්පත ලියාපදිංචි කිරීම සඳහා ඉල්ලීමක් යවා ඇති අතර එය අනුමත වෙමින් පවතී.", "Shared.AppsAndKeys.ViewToken.please.copy": "කරුණාකර ප්‍රවේශ ටෝකනය පිටපත් කරන්න", "Shared.AppsAndKeys.ViewToken.info.fourth": ") විෂය පථ", "Shared.AppsAndKeys.ViewToken.access.token": "ටෝකනයට ප්‍රවේශ වන්න", "Shared.AppsAndKeys.ViewToken.please.copy.apikey": "කරුණාකර API යතුර පිටපත් කරන්න", "Shared.AppsAndKeys.WaitingForApproval.msg.reject": "යතුරු උත්පාදනය කිරීමෙන් මෙම යෙදුම ප්‍රතික්ෂේප කර ඇත", "Shared.ConfirmDialog.cancel": "අවලංගු කරන්න", "Shared.ConfirmDialog.please.confirm": "කරුණාකර තහවුරු කරන්න", "Shared.ConfirmDialog.ok": "හරි", "Shared.ConfirmDialog.please.confirm.sure": "ඔබට විශ්වාසද?", "api.console.gateway.heading": "දොරටුව", "api.console.security.heading": "ආරක්ෂාව", "TenantListing.title": "කුලී නිවැසි සංවර්ධක ද්වාර", "api.console.require.access.token": "API උත්සාහ කිරීමට ඔබට ප්‍රවේශ ටෝකනයක් අවශ්‍ය වේ. ප්‍රවේශවීමේ ටෝකනයක් ජනනය කිරීම සඳහා කරුණාකර ලොග් වී API වෙත දායක වන්න. ඔබට දැනටමත් ප්‍රවේශ ටෝකනයක් තිබේ නම්, කරුණාකර එය පහතින් සපයන්න.", "api.gateways": "API ද්වාර", "notice": "සැලකිල්ලට ගන්න", "micro.gateways": "මයික්‍රොගේට්වේස්", "password": "මුරපදය", "application.creation.pending": "මෙම අයදුම්පත ලියාපදිංචි කිරීම සඳහා ඉල්ලීමක් යවා ඇත.", "subscription.pending": "ඔබගේ දායකත්ව ඉල්ලීම ඉදිරිපත් කර ඇති අතර දැන් එය අනුමැතිය බලාපොරොත්තුවෙන් සිටී.", "username": "පරිශීලක නාමය"}