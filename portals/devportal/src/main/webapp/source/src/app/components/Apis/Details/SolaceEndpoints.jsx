/*
 *  Copyright (c) 2021, WSO2 Inc. (http://www.wso2.org) All Rights Reserved.
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
import React, { useContext, useEffect, useState } from 'react';
import { styled } from '@mui/material/styles';
import Paper from '@mui/material/Paper';
import InputBase from '@mui/material/InputBase';
import IconButton from '@mui/material/IconButton';
import PropTypes from 'prop-types';
import Icon from '@mui/material/Icon';
import { FormattedMessage, useIntl } from 'react-intl';
import Typography from '@mui/material/Typography';
import Avatar from '@mui/material/Avatar';
import Grid from '@mui/material/Grid';
import Chip from '@mui/material/Chip';
import Tooltip from '@mui/material/Tooltip';
import { upperCaseString } from 'AppData/stringFormatter';
import { ApiContext } from './ApiContext';
import GoToTryOut from './GoToTryOut';

const PREFIX = 'SolaceEndpoints';

const classes = {
    root: `${PREFIX}-root`,
    input: `${PREFIX}-input`,
    avatar: `${PREFIX}-avatar`,
    iconStyle: `${PREFIX}-iconStyle`,
    sectionTitle: `${PREFIX}-sectionTitle`,
};

// TODO jss-to-styled codemod: The Fragment root was replaced by div. Change the tag if needed.
const Root = styled('div')((
    {
        theme,
    },
) => ({
    [`& .${classes.root}`]: {
        padding: '2px 4px',
        display: 'flex',
        alignItems: 'center',
        width: '100%',
        border: `solid 1px ${theme.palette.grey[300]}`,
        '& .MuiInputBase-root:before,  .MuiInputBase-root:hover': {
            borderBottom: 'none !important',
            color: theme.palette.primary.main,
        },
        '& .MuiSelect-select': {
            color: theme.palette.primary.main,
            paddingLeft: theme.spacing(),
        },
        '& .MuiInputBase-input': {
            color: theme.palette.primary.main,
        },
        '& .material-icons': {
            fontSize: 16,
            color: `${theme.palette.grey[700]} !important`,
        },
        borderRadius: 10,
        marginRight: theme.spacing(),
    },

    [`& .${classes.input}`]: {
        marginLeft: theme.spacing(1),
        flex: 1,
    },

    [`& .${classes.avatar}`]: {
        width: 30,
        height: 30,
        background: 'transparent',
        border: `solid 1px ${theme.palette.grey[300]}`,
    },

    [`& .${classes.iconStyle}`]: {
        cursor: 'pointer',
        margin: '-10px 0',
        padding: '0 0 0 5px',
        '& .material-icons': {
            fontSize: 18,
            color: '#9c9c9c',
        },
    },

    [`& .${classes.sectionTitle}`]: {
        color: '#424242',
        fontSize: '0.85rem',
        marginRight: 20,
        fontWeight: 400,
    },
}));

function SolaceEndpoints() {
    const { api } = useContext(ApiContext);
    const [urlCopied, setUrlCopied] = useState(false);
    const [protocols, setProtocols] = useState(null);

    const intl = useIntl();

    const onCopy = () => {
        setUrlCopied(true);
        const caller = function () {
            setUrlCopied(false);
        };
        setTimeout(caller, 2000);
    };

    useEffect(() => {
        const protocolsList = [];
        if (api.asyncTransportProtocols) {
            api.asyncTransportProtocols.map((e) => {
                protocolsList.push(JSON.parse(e));
                return null;
            });
            setProtocols(protocolsList);
        }
    }, [api]);

    return (
        <Root>
            {(api.gatewayVendor === 'solace') && (
                <Grid container spacing={2} xs={12}>
                    <Grid item spacing={2} xs={2}>
                        <Typography
                            variant='subtitle2'
                            component='label'
                            gutterBottom
                            align='left'
                            className={classes.sectionTitle}
                        >
                            <FormattedMessage
                                id='Apis.Details.protocols.and.endpoints'
                                defaultMessage='Protocols & Endpoints'
                            />
                        </Typography>
                    </Grid>
                    <Grid item spacing={2} xs={10}>
                        <GoToTryOut />
                    </Grid>
                    <Grid item spacing={2} xs={12}>
                        <Grid container spacing={2} xs={12}>

                            <Grid item spacing={2} xs={12}>
                                {api.endpointURLs.map((e) => (
                                    <Typography
                                        component='p'
                                        variant='subtitle2'
                                    >
                                        {e.environmentDisplayName}
                                    </Typography>
                                ))}
                            </Grid>
                            <Grid item spacing={2} xs={12}>
                                {protocols && protocols.map((p) => (
                                    <Grid container spacing={2} xs={12}>
                                        <Grid item>
                                            <Typography component='p' variant='body1'>
                                                <FormattedMessage
                                                    id='Apis.Details.NewOverview.Endpoints.blank'
                                                    defaultMessage='-'
                                                />
                                            </Typography>
                                        </Grid>
                                        <Grid
                                            item
                                            style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                            }}
                                        >
                                            <Chip
                                                label={upperCaseString(p.protocol)}
                                                data-testid={upperCaseString(p.protocol) + '-label'}
                                                color='primary'
                                                style={{
                                                    width: '70px',
                                                }}
                                            />
                                        </Grid>
                                        <Grid
                                            xs={10}
                                            item
                                            style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                            }}
                                        >
                                            <Paper
                                                id='gateway-envirounment'
                                                component='form'
                                                className={classes.root}
                                            >
                                                <InputBase
                                                    className={classes.input}
                                                    inputProps={{ 'aria-label': 'api url' }}
                                                    value={p.endPointUrl}
                                                />
                                                <Avatar className={classes.avatar} sizes={30}>
                                                    <Tooltip
                                                        title={
                                                            urlCopied
                                                                ? intl.formatMessage({
                                                                    defaultMessage: 'Copied',
                                                                    id: 'Apis.Details.Environments.copied',
                                                                })
                                                                : intl.formatMessage({
                                                                    defaultMessage: 'Copy to clipboard',
                                                                    id: 'Apis.Details.Environments.copy.to.clipboard',
                                                                })
                                                        }
                                                        interactive
                                                        placement='right'
                                                        className={classes.iconStyle}
                                                    >
                                                        <IconButton
                                                            aria-label='Copy the API URL to clipboard'
                                                            size='large'
                                                            onClick={() => {
                                                                navigator.clipboard
                                                                    .writeText(p.endPointUrl).then(() => onCopy('urlCopied'));
                                                            }}
                                                        >
                                                            <Icon color='secondary'>file_copy</Icon>
                                                        </IconButton>
                                                    </Tooltip>
                                                </Avatar>
                                            </Paper>
                                        </Grid>
                                    </Grid>
                                ))}
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            )}
        </Root>
    );
}

SolaceEndpoints.propTypes = {
    classes: PropTypes.shape({}).isRequired,
    intl: PropTypes.shape({}).isRequired,
};

export default SolaceEndpoints;
