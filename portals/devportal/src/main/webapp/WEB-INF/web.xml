<?xml version="1.0" encoding="UTF-8"?>
<!--
    ~ Copyright (c) 2017-2023, WSO2 LLC (https://www.wso2.com).
    ~
    ~ WSO2 LLC licenses this file to you under the Apache License,
    ~ Version 2.0 (the "License"); you may not use this file except
    ~ in compliance with the License.
    ~ You may obtain a copy of the License at
    ~
    ~ http://www.apache.org/licenses/LICENSE-2.0
    ~
    ~ Unless required by applicable law or agreed to in writing,
    ~ software distributed under the License is distributed on an
    ~ "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    ~ KIND, either express or implied.  See the License for the
    ~ specific language governing permissions and limitations
    ~ under the License.
-->

<web-app version="3.1" xmlns="http://xmlns.jcp.org/xml/ns/javaee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://xmlns.jcp.org/xml/ns/javaee/web-app_3_1.xsd">
    <display-name>devportal</display-name>
    <filter>
        <filter-name>HttpHeaderSecurityFilter</filter-name>
        <filter-class>org.apache.catalina.filters.HttpHeaderSecurityFilter</filter-class>
        <init-param>
            <param-name>hstsEnabled</param-name>
            <param-value>false</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>HttpHeaderSecurityFilter</filter-name>
        <url-pattern>*</url-pattern>
    </filter-mapping>
    <filter>
        <filter-name>ContentTypeBasedCachePreventionFilter</filter-name>
        <filter-class>
            org.wso2.carbon.ui.filters.cache.ContentTypeBasedCachePreventionFilter
        </filter-class>
        <init-param>
            <param-name>patterns</param-name>
            <param-value>"text/html" ,"application/json" ,"plain/text"</param-value>
        </init-param>
        <init-param>
            <param-name>filterAction</param-name>
            <param-value>enforce</param-value>
        </init-param>
        <init-param>
            <param-name>httpHeaders</param-name>
            <param-value>Cache-Control: no-store, no-cache, must-revalidate, private</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>ContentTypeBasedCachePreventionFilter</filter-name>
        <url-pattern>*</url-pattern>
    </filter-mapping>
    <servlet>
        <servlet-name>settings</servlet-name>
        <jsp-file>/services/settings/settings.jsp</jsp-file>
    </servlet>
    <servlet>
        <servlet-name>index</servlet-name>
        <jsp-file>/site/public/pages/index.jsp</jsp-file>
    </servlet>
    <servlet>
        <servlet-name>logout</servlet-name>
        <jsp-file>/services/logout/logout.jsp</jsp-file>
    </servlet>
    <servlet>
        <servlet-name>idp</servlet-name>
        <jsp-file>/services/login/idp.jsp</jsp-file>
    </servlet>
    <servlet>
        <servlet-name>introspect</servlet-name>
        <jsp-file>/services/login/introspect.jsp</jsp-file>
    </servlet>
    <servlet>
        <servlet-name>login_callback</servlet-name>
        <jsp-file>/services/login/login_callback.jsp</jsp-file>
    </servlet>
    <servlet>
        <servlet-name>logout_callback</servlet-name>
        <jsp-file>/services/logout/logout_callback.jsp</jsp-file>
    </servlet>
    <servlet>
        <servlet-name>refresh</servlet-name>
        <jsp-file>/services/refresh/refresh.jsp</jsp-file>
    </servlet>
    <servlet>
        <servlet-name>theme</servlet-name>
        <jsp-file>/services/settings/userTheme.jsp</jsp-file>
    </servlet>
    <servlet>
        <servlet-name>exclude</servlet-name>
        <jsp-file>/services/settings/exclusion.jsp</jsp-file>
    </servlet>
    <servlet-mapping>
        <servlet-name>settings</servlet-name>
        <url-pattern>/services/settings/settings.js</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>index</servlet-name>
        <url-pattern>/api-groups/*</url-pattern>
        <url-pattern>/api-products/*</url-pattern>
        <url-pattern>/home/<USER>/url-pattern>
        <url-pattern>/apis/*</url-pattern>
        <url-pattern>/policy/*</url-pattern>
        <url-pattern>/apiGroups/*</url-pattern>
        <url-pattern>/sign-up/*</url-pattern>
        <url-pattern>/login/*</url-pattern>
        <url-pattern>/settings/*</url-pattern>
        <url-pattern>/applications/*</url-pattern>
        <url-pattern>/logout/*</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>logout</servlet-name>
        <url-pattern>/services/logout</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>idp</servlet-name>
        <url-pattern>/services/configs</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>introspect</servlet-name>
        <url-pattern>/services/auth/introspect</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>login_callback</servlet-name>
        <url-pattern>/services/auth/callback/login</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>logout_callback</servlet-name>
        <url-pattern>/services/auth/callback/logout</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>refresh</servlet-name>
        <url-pattern>/services/refresh</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>theme</servlet-name>
        <url-pattern>/site/public/theme/userTheme.js</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>exclude</servlet-name>
        <url-pattern>/.eslintignore</url-pattern>
        <url-pattern>/source/*</url-pattern>
        <url-pattern>/babel.config</url-pattern>
        <url-pattern>/loader.js</url-pattern>
        <url-pattern>/.eslintrc.js</url-pattern>
        <url-pattern>/jsconfig.json</url-pattern>
        <url-pattern>/package.json</url-pattern>
        <url-pattern>/webpack.config.js</url-pattern>
        <url-pattern>/.npmrc</url-pattern>
        <url-pattern>/package-lock.json</url-pattern>
        <url-pattern>/jest.config.js</url-pattern>
    </servlet-mapping>
    <session-config>
        <session-timeout>
            30
        </session-timeout>
    </session-config>
    <welcome-file-list>
        <welcome-file>site/public/pages/index.jsp</welcome-file>
    </welcome-file-list>
    <!-- custom error pages -->
    <error-page>
        <error-code>401</error-code>
        <location>/site/public/pages/error-pages/401.html</location>
    </error-page>
    <error-page>
        <error-code>403</error-code>
        <location>/site/public/pages/error-pages/403.html</location>
    </error-page>
    <error-page>
        <error-code>404</error-code>
        <location>/site/public/pages/error-pages/404.html</location>
    </error-page>
    <error-page>
        <error-code>500</error-code>
        <location>/site/public/pages/error-pages/500.html</location>
    </error-page>
    <error-page>
        <location>/site/public/pages/error-pages/error-page.html</location>
    </error-page>
</web-app>
