{"APIs.details.endpoints.certificate.usage": "证书用途 -", "APIs.details.endpoints.certificate.usage.cancel": "取消", "Actions": "操作", "Active.Deployment.Available": "存在可用活跃部署", "Active.Deployments.Available": "存在多个可用活跃部署", "Adding.Policy.Mapping.Error": "添加策略映射时发生错误", "AdminPages.Addons.ListBase.noDataError": "获取数据时发生错误", "AdminPages.Addons.ListBase.nodata.message": "暂无数据", "AdminPages.Addons.ListBase.reload": "重新加载", "Api.category.dropdown.tooltip": "允许对具有相似属性的API进行分组。环境中需预先定义API分类方可关联API。", "Api.login.page.readonly.user": "只读", "Apis.APIProductCreateWrapper.error.errorMessage.create.api.product": "创建API产品时发生错误", "Apis.APIProductCreateWrapper.error.errorMessage.create.revision": "创建API产品修订版时发生错误", "Apis.APIProductCreateWrapper.error.errorMessage.deploy.revision": "部署API产品修订版时发生错误", "Apis.Create.AIAPI.ApiCreateAIAPI.back": "返回", "Apis.Create.AIAPI.ApiCreateAIAPI.cancel": "取消", "Apis.Create.AIAPI.ApiCreateAIAPI.create": "创建", "Apis.Create.AIAPI.ApiCreateAIAPI.heading": "使用AI/LLM服务提供商API定义创建API", "Apis.Create.AIAPI.ApiCreateAIAPI.next": "下一步", "Apis.Create.AIAPI.ApiCreateAIAPI.sub.heading": "使用现有AI/LLM服务提供商API定义创建API", "Apis.Create.AIAPI.ApiCreateAIAPI.wizard.one": "提供AI/LLM服务提供商API", "Apis.Create.AIAPI.ApiCreateAIAPI.wizard.two": "创建API", "Apis.Create.AIAPI.Steps.ProvideAIOpenAPI.AI.model": "API版本", "Apis.Create.AIAPI.Steps.ProvideAIOpenAPI.AI.model.empty": "未选择AI/LLM服务提供商", "Apis.Create.AIAPI.Steps.ProvideAIOpenAPI.AI.model.helper": "为API选择版本", "Apis.Create.AIAPI.Steps.ProvideAIOpenAPI.AI.model.placeholder": "搜索API版本", "Apis.Create.AIAPI.Steps.ProvideAIOpenAPI.AI.provider": "AI/LLM服务提供商", "Apis.Create.AIAPI.Steps.ProvideAIOpenAPI.AI.provider.empty": "未定义AI/LLM服务提供商", "Apis.Create.AIAPI.Steps.ProvideAIOpenAPI.AI.provider.helper.text": "为API选择AI/LLM服务提供商", "Apis.Create.AIAPI.Steps.ProvideAIOpenAPI.AI.provider.loading": "正在加载AI/LLM服务提供商...", "Apis.Create.AIAPI.Steps.ProvideAIOpenAPI.AI.provider.placeholder": "搜索AI/LLM服务提供商", "Apis.Create.AIAPI.Steps.ProvideAIOpenAPI.LLM.Provider.fetch.error": "获取LLM提供商时发生错误", "Apis.Create.AIAPI.Steps.ProvideAIOpenAPI.LLMProvider.API.Definition.fetch.error": "获取LLM提供商API定义时发生错误", "Apis.Create.AIAPI.Steps.ProvideAIOpenAPI.LLMProvider.API.Definition.validation.failure.error": "验证LLM提供商API定义失败", "Apis.Create.APIProduct.APIProductCreateWrapper.back": "返回", "Apis.Create.APIProduct.APIProductCreateWrapper.cancel": "取消", "Apis.Create.APIProduct.APIProductCreateWrapper.create": "创建", "Apis.Create.APIProduct.APIProductCreateWrapper.create.and.publish.btn": "创建并发布", "Apis.Create.APIProduct.APIProductCreateWrapper.create.deploy.revision.status": "正在部署修订版...", "Apis.Create.APIProduct.APIProductCreateWrapper.create.publish.status": "正在发布API产品...", "Apis.Create.APIProduct.APIProductCreateWrapper.create.revision.status": "正在创建修订版...", "Apis.Create.APIProduct.APIProductCreateWrapper.create.status": "正在创建API产品...", "Apis.Create.APIProduct.APIProductCreateWrapper.created.success": "API产品创建成功", "Apis.Create.APIProduct.APIProductCreateWrapper.defineProvide": "定义API产品", "Apis.Create.APIProduct.APIProductCreateWrapper.error.policies.not.available": "流控策略不可用，请联系管理员", "Apis.Create.APIProduct.APIProductCreateWrapper.heading": "创建API产品", "Apis.Create.APIProduct.APIProductCreateWrapper.next": "下一步", "Apis.Create.APIProduct.APIProductCreateWrapper.otherStatus": "API产品状态更新成功", "Apis.Create.APIProduct.APIProductCreateWrapper.publishStatus": "已发送生命周期状态变更请求", "Apis.Create.APIProduct.APIProductCreateWrapper.resources": "添加资源", "Apis.Create.APIProduct.APIProductCreateWrapper.revision.created.success": "API修订版创建成功", "Apis.Create.APIProduct.APIProductCreateWrapper.revision.deployed.success": "API修订版部署成功", "Apis.Create.APIProduct.APIProductCreateWrapper.sub.heading": "通过提供名称、上下文、版本、资源和业务计划（可选）创建API产品", "Apis.Create.AsyncAPI.ApiCreateAsyncAPI.advertiseOnly.warning": "API管理器仅支持WebSocket、SSE和WebSub类型的流式API。如需在网关上部署，请创建支持的类型。", "Apis.Create.AsyncAPI.ApiCreateAsyncAPI.advertiseOnly.warning.title": "其他类型流式API将作为第三方API创建", "Apis.Create.AsyncAPI.ApiCreateAsyncAPI.back": "返回", "Apis.Create.AsyncAPI.ApiCreateAsyncAPI.cancel": "取消", "Apis.Create.AsyncAPI.ApiCreateAsyncAPI.create": "创建", "Apis.Create.AsyncAPI.ApiCreateAsyncAPI.created.error": "添加API时发生错误", "Apis.Create.AsyncAPI.ApiCreateAsyncAPI.created.success": "API创建成功", "Apis.Create.AsyncAPI.ApiCreateAsyncAPI.designAssistant.back": "返回", "Apis.Create.AsyncAPI.ApiCreateAsyncAPI.externalEndpoint": "外部端点", "Apis.Create.AsyncAPI.ApiCreateAsyncAPI.externalEndpoint.error": "无效的端点URL", "Apis.Create.AsyncAPI.ApiCreateAsyncAPI.heading": "使用AsyncAPI定义创建API", "Apis.Create.AsyncAPI.ApiCreateAsyncAPI.next": "下一步", "Apis.Create.AsyncAPI.ApiCreateAsyncAPI.sub.heading": "使用现有AsyncAPI文件或URL创建API", "Apis.Create.AsyncAPI.ApiCreateAsyncAPI.wizard.one": "提供AsyncAPI", "Apis.Create.AsyncAPI.ApiCreateAsyncAPI.wizard.two": "创建API", "Apis.Create.AsyncAPI.Steps.ProvideAsyncAPI.Input.file.dropzone": "拖放AsyncAPI文件至此{break}或{break}浏览文件", "Apis.Create.AsyncAPI.Steps.ProvideAsyncAPI.Input.file.upload": "上传文件", "Apis.Create.AsyncAPI.Steps.ProvideAsyncAPI.Input.type": "输入类型", "Apis.Create.AsyncAPI.Steps.ProvideAsyncAPI.Input.url.text.placeholder": "输入AsyncAPI URL", "Apis.Create.AsyncAPI.Steps.ProvideAsyncAPI.content.validation.failed": "AsyncAPI内容验证失败", "Apis.Create.AsyncAPI.Steps.ProvideAsyncAPI.file.label": "AsyncAPI文件", "Apis.Create.AsyncAPI.Steps.ProvideAsyncAPI.solace.api.label": "识别为Solace事件门户API", "Apis.Create.AsyncAPI.Steps.ProvideAsyncAPI.url.helper.text": "点击以验证URL", "Apis.Create.AsyncAPI.Steps.ProvideAsyncAPI.url.label": "AsyncAPI URL", "Apis.Create.Components.DefaultAPIForm.api.actual.context.helper": "API将在网关的{actualContext}上下文中暴露", "Apis.Create.Components.DefaultAPIForm.api.channel": "通道", "Apis.Create.Components.DefaultAPIForm.api.context": "上下文", "Apis.Create.Components.DefaultAPIForm.api.endpoint": "端点", "Apis.Create.Components.DefaultAPIForm.api.product.actual.context.helper": "API产品将在网关的{actualContext}上下文中暴露", "Apis.Create.Components.DefaultAPIForm.api.product.context": "上下文", "Apis.Create.Components.DefaultAPIForm.api.product.version": "版本", "Apis.Create.Components.DefaultAPIForm.mandatory.fields": "必填字段", "Apis.Create.Components.DefaultAPIForm.name": "名称", "Apis.Create.Components.DefaultAPIForm.select.gateway.type": "选择网关类型", "Apis.Create.Components.DefaultAPIForm.select.gateway.type.helper.text": "选择运行API的网关类型", "Apis.Create.Components.DefaultAPIForm.validation.error.curly.braces.cannot.be.in.path.param": "路径参数中不能包含{或}", "Apis.Create.Components.DefaultAPIForm.validation.error.name.exists": "名称{value}已存在", "Apis.Create.Components.DefaultAPIForm.validation.error.unbalanced.parantheses": "API上下文中括号必须成对", "Apis.Create.Components.DefaultAPIForm.validation.error.version.exists.as.a.substring.in.path.param": "路径参数中不能包含{version}子字符串", "Apis.Create.Components.DefaultAPIForm.version": "版本", "Apis.Create.Components.NewTopic.topic.name": "主题名称", "Apis.Create.Components.SelectPolicies.business.plans": "业务计划", "Apis.Create.Default.APICreateDefault.api.created.error": "添加API时发生错误", "Apis.Create.Default.APICreateDefault.api.created.success": "API创建成功", "Apis.Create.Default.APICreateDefault.api.heading": "创建API", "Apis.Create.Default.APICreateDefault.api.product.created.error": "添加API产品时发生错误", "Apis.Create.Default.APICreateDefault.api.product.created.success": "API产品创建成功", "Apis.Create.Default.APICreateDefault.api.revision.created.success": "API修订版创建成功", "Apis.Create.Default.APICreateDefault.api.revision.deployed.success": "API修订版部署成功", "Apis.Create.Default.APICreateDefault.api.sub.heading": "通过提供名称、版本、上下文和后端端点（可选）创建API", "Apis.Create.Default.APICreateDefault.apiProduct.heading": "创建API产品", "Apis.Create.Default.APICreateDefault.apiProduct.sub.heading": "通过提供名称、上下文和业务计划（可选）创建API产品", "Apis.Create.Default.APICreateDefault.cancel": "取消", "Apis.Create.Default.APICreateDefault.create.btn": "创建API", "Apis.Create.Default.APICreateDefault.create.publish.btn": "创建并发布", "Apis.Create.Default.APICreateDefault.create.publish.btn.creating.deploying.revision.status": "正在部署修订版...", "Apis.Create.Default.APICreateDefault.create.publish.btn.creating.publishing.status": "正在发布API...", "Apis.Create.Default.APICreateDefault.create.publish.btn.creating.revision.status": "正在创建修订版...", "Apis.Create.Default.APICreateDefault.create.publish.btn.creating.status": "正在创建API...", "Apis.Create.Default.APICreateDefault.error.errorMessage.create.revision": "创建API修订版时发生错误", "Apis.Create.Default.APICreateDefault.error.errorMessage.deploy.revision": "部署API修订版时发生错误", "Apis.Create.Default.APICreateDefault.error.errorMessage.publish": "发布API时发生错误", "Apis.Create.Default.APICreateDefault.error.governance.violation": "因治理违规无法发布API", "Apis.Create.Default.APICreateDefault.error.governance.violation.download": "下载违规报告", "Apis.Create.Default.APICreateDefault.error.otherStatus": "发布API时发生错误", "Apis.Create.Default.APICreateDefault.error.policies.not.available": "流控策略不可用，请联系管理员", "Apis.Create.Default.APICreateDefault.error.url.not.valid": "URL无效", "Apis.Create.Default.APICreateDefault.streaming.error": "添加API时发生错误", "Apis.Create.Default.APICreateDefault.streaming.revision.deployed.": "API修订版部署成功", "Apis.Create.Default.APICreateDefault.streaming.revision.success": "API修订版创建成功", "Apis.Create.Default.APICreateDefault.streaming.success": "API创建成功", "Apis.Create.Default.APICreateDefault.success.otherStatus": "API更新成功", "Apis.Create.Default.APICreateDefault.success.publishStatus": "已发送生命周期状态变更请求", "Apis.Create.Default.APICreateDefault.webSocket.heading": "创建WebSocket API", "Apis.Create.Default.APICreateDefault.webSocket.sub.heading": "通过提供名称和上下文创建WebSocket API", "Apis.Create.GraphQL.ApiCreateGraphQL.back": "返回", "Apis.Create.GraphQL.ApiCreateGraphQL.create": "创建", "Apis.Create.GraphQL.ApiCreateGraphQL.created.error": "添加API时发生错误", "Apis.Create.GraphQL.ApiCreateGraphQL.created.success": "{name} API创建成功", "Apis.Create.GraphQL.ApiCreateGraphQL.designAssistant.back": "返回", "Apis.Create.GraphQL.ApiCreateGraphQL.error.policies.not.available": "流控策略不可用，请联系管理员", "Apis.Create.GraphQL.ApiCreateGraphQL.heading": "创建GraphQL API", "Apis.Create.GraphQL.ApiCreateGraphQL.next": "下一步", "Apis.Create.GraphQL.ApiCreateGraphQL.sub.heading": "通过文件、URL或GraphQL端点导入SDL定义创建GraphQL API", "Apis.Create.GraphQL.ApiCreateGraphQL.wizard.one": "提供GraphQL", "Apis.Create.GraphQL.ApiCreateGraphQL.wizard.two": "创建API", "Apis.Create.GraphQL.Steps.ProvideGraphQL.Input.file.dropzone": "拖放文件至此{break}或{break}浏览文件{break}({accept})", "Apis.Create.GraphQL.Steps.ProvideGraphQL.Input.file.upload": "上传文件", "Apis.Create.GraphQL.Steps.ProvideGraphQL.Input.type": "输入类型", "Apis.Create.GraphQL.create.api.endpoint.label": "GraphQL端点", "Apis.Create.GraphQL.create.api.endpoint.placeholder": "输入GraphQL端点", "Apis.Create.GraphQL.create.api.form.endpoint.label": "GraphQL端点", "Apis.Create.GraphQL.create.api.form.file.label": "GraphQL文件/压缩包", "Apis.Create.GraphQL.create.api.form.url.label": "GraphQL SDL URL", "Apis.Create.GraphQL.create.api.url.helper.text": "点击以验证URL", "Apis.Create.GraphQL.create.api.url.label": "GraphQL SDL URL", "Apis.Create.GraphQL.create.api.url.placeholder": "输入GraphQL SDL URL", "Apis.Create.OpenAPI.ApiCreateOpenAPI.back": "返回", "Apis.Create.OpenAPI.ApiCreateOpenAPI.cancel": "取消", "Apis.Create.OpenAPI.ApiCreateOpenAPI.create": "创建", "Apis.Create.OpenAPI.ApiCreateOpenAPI.created.error": "添加API时发生错误", "Apis.Create.OpenAPI.ApiCreateOpenAPI.created.success": "API创建成功", "Apis.Create.OpenAPI.ApiCreateOpenAPI.designAssistant.back": "返回", "Apis.Create.OpenAPI.ApiCreateOpenAPI.heading": "使用OpenAPI定义创建API", "Apis.Create.OpenAPI.ApiCreateOpenAPI.next": "下一步", "Apis.Create.OpenAPI.ApiCreateOpenAPI.sub.heading": "使用现有OpenAPI文件或URL创建API", "Apis.Create.OpenAPI.ApiCreateOpenAPI.wizard.one": "提供OpenAPI", "Apis.Create.OpenAPI.ApiCreateOpenAPI.wizard.two": "创建API", "Apis.Create.OpenAPI.Steps.ProvideOpenAPI.Input.file.dropzone": "拖放OpenAPI文件/压缩包至此{break}或{break}浏览文件", "Apis.Create.OpenAPI.Steps.ProvideOpenAPI.Input.file.upload": "上传文件", "Apis.Create.OpenAPI.Steps.ProvideOpenAPI.Input.type": "输入类型", "Apis.Create.OpenAPI.Steps.ValidationResults.linter.results": "代码检查结果", "Apis.Create.OpenAPI.Steps.ValidationResults.linting": "生成代码检查结果", "Apis.Create.OpenAPI.Steps.ValidationResults.validating": "验证API定义", "Apis.Create.OpenAPI.Steps.ValidationResults.validation.errros": "验证错误", "Apis.Create.OpenAPI.create.api.form.file.label": "OpenAPI文件/压缩包", "Apis.Create.OpenAPI.create.api.form.url.label": "OpenAPI URL", "Apis.Create.OpenAPI.create.api.openapi.content.validation.failed": "OpenAPI内容验证失败", "Apis.Create.OpenAPI.create.api.url.helper.text": "点击以验证URL", "Apis.Create.OpenAPI.create.api.url.label": "OpenAPI URL", "Apis.Create.OpenAPI.create.api.url.placeholder": "输入OpenAPI URL", "Apis.Create.StreamingAPI.APICreateStreamingAPI.api.heading": "创建流式API", "Apis.Create.StreamingAPI.APICreateStreamingAPI.api.sub.heading": "通过提供名称、版本、上下文和端点创建API", "Apis.Create.StreamingAPI.APICreateStreamingAPI.error.governance.violation": "由于治理违规无法创建API修订版本", "Apis.Create.StreamingAPI.APICreateStreamingAPI.error.governance.violation.download": "下载违规详情", "Apis.Create.StreamingAPI.APICreateStreamingAPI.websub.api.sub.heading": "通过提供名称、版本和上下文创建API", "Apis.Create.WSDL.ApiCreateWSDL.create.error": "添加API时发生错误", "Apis.Create.WSDL.ApiCreateWSDL.create.success": "API创建成功", "Apis.Create.WSDL.ApiCreateWSDL.deprecated.msg": "APIM对此功能仅支持有限能力。如需处理复杂WSDL/XML模式，请参考以下文档。", "Apis.Create.WSDL.ApiCreateWSDL.error.policies.not.available": "限流策略不可用，请联系管理员", "Apis.Create.WSDL.ApiCreateWSDL.heading": "将SOAP服务公开为REST API", "Apis.Create.WSDL.ApiCreateWSDL.step.label.create.api": "创建API", "Apis.Create.WSDL.ApiCreateWSDL.step.label.create.api.back.btn": "返回", "Apis.Create.WSDL.ApiCreateWSDL.step.label.create.api.create.btn": "创建", "Apis.Create.WSDL.ApiCreateWSDL.step.label.create.api.next.btn": "下一步", "Apis.Create.WSDL.ApiCreateWSDL.step.label.provide.wsdl": "提供WSDL", "Apis.Create.WSDL.ApiCreateWSDL.sub.heading": "通过导入SOAP服务的WSDL将其公开为REST API", "Apis.Create.WSDL.Steps.ProvideWSDL.Input.file.archive.dropzone": "拖放WSDL文件/压缩包{break} -或-", "Apis.Create.WSDL.Steps.ProvideWSDL.Input.file.dropzone": "拖放WSDL文件{break} -或-", "Apis.Create.WSDL.Steps.ProvideWSDL.Input.file.upload": "浏览上传文件", "Apis.Create.WSDL.Steps.ProvideWSDL.Input.type": "输入类型", "Apis.Create.WSDL.Steps.ProvideWSDL.SOAPtoREST.label": "生成REST API", "Apis.Create.WSDL.Steps.ProvideWSDL.file.label.wsdl.file.archive": "WSDL文件/压缩包", "Apis.Create.WSDL.Steps.ProvideWSDL.implementation.type": "实现类型", "Apis.Create.WSDL.Steps.ProvideWSDL.passthrough.label": "透传", "Apis.Create.WSDL.Steps.ProvideWSDL.url.label": "WSDL URL", "Apis.Create.WSDL.content.validation.file.failed": "WSDL内容验证失败！", "Apis.Create.WSDL.content.validation.url.failed": "无效的WSDL URL！", "Apis.Create.WSDL.url.helper.text": "点击此处验证URL", "Apis.Create.WSDL.url.label": "WSDL URL", "Apis.Create.WSDL.url.placeholder": "输入WSDL URL", "Apis.Create.WSDL.validation.error.response": "验证过程中发生错误", "Apis.Create.asyncAPI.Components.SelectPolicies.business.plans": "协议", "Apis.Create.streaming.Components.SelectPolicies.business.plans": "协议", "Apis.Create.streaming.Components.create.and.publish.btn": "创建并发布", "Apis.Create.streaming.Components.create.btn": "创建", "Apis.Details.\n                                                                                            Environments.Environments.\n                                                                                            pending.chip": "待处理", "Apis.Details.\n                                                                                Environments.deploy.vhost": "虚拟主机", "Apis.Details.APIDefinition.APIDefinition.api.definition": "API定义", "Apis.Details.APIDefinition.APIDefinition.api.definition.oas.updated.successfully": "API定义更新成功", "Apis.Details.APIDefinition.APIDefinition.api.definition.save.confirmation": "确定要保存API定义吗？这可能会影响现有资源。", "Apis.Details.APIDefinition.APIDefinition.api.definition.updated.successfully": "API定义更新成功", "Apis.Details.APIDefinition.APIDefinition.async.api.definition.updated.successfully": "API定义更新成功", "Apis.Details.APIDefinition.APIDefinition.async.api.import.definition.updated.successfully": "API定义更新成功", "Apis.Details.APIDefinition.APIDefinition.asyncAPI.definition": "AsyncAPI定义", "Apis.Details.APIDefinition.APIDefinition.audit.api": "审计API", "Apis.Details.APIDefinition.APIDefinition.btn.close": "关闭", "Apis.Details.APIDefinition.APIDefinition.btn.no": "取消", "Apis.Details.APIDefinition.APIDefinition.convert.to": "转换为", "Apis.Details.APIDefinition.APIDefinition.documents.swagger.editor.import.content": "导入内容", "Apis.Details.APIDefinition.APIDefinition.documents.swagger.editor.update.content": "更新内容", "Apis.Details.APIDefinition.APIDefinition.download.definition": "下载定义", "Apis.Details.APIDefinition.APIDefinition.edit": "编辑", "Apis.Details.APIDefinition.APIDefinition.editor.drawer.toggle.linter": "代码检查", "Apis.Details.APIDefinition.APIDefinition.editor.drawer.toggle.swagger": "Swagger", "Apis.Details.APIDefinition.APIDefinition.error.updating.graphQL.schema": "更新GraphQL模式时出错", "Apis.Details.APIDefinition.APIDefinition.error.while.updating.api.definition": "更新API定义时发生错误", "Apis.Details.APIDefinition.APIDefinition.error.while.updating.async.api.definition": "更新API定义时发生错误", "Apis.Details.APIDefinition.APIDefinition.error.while.updating.import.api.definition": "更新API定义时出错", "Apis.Details.APIDefinition.APIDefinition.error.while.updating.import.async.api.definition": "更新API定义时出错", "Apis.Details.APIDefinition.APIDefinition.graphQLDefinition.updated.successfully": "模式定义更新成功", "Apis.Details.APIDefinition.APIDefinition.import.definition": "导入定义", "Apis.Details.APIDefinition.APIDefinition.import.definition.asyncApi": "导入AsyncAPI定义", "Apis.Details.APIDefinition.APIDefinition.import.definition.cancel": "取消", "Apis.Details.APIDefinition.APIDefinition.import.definition.edit": "编辑并导入", "Apis.Details.APIDefinition.APIDefinition.import.definition.graphql": "导入GraphQL模式定义", "Apis.Details.APIDefinition.APIDefinition.import.definition.import": "导入", "Apis.Details.APIDefinition.APIDefinition.import.definition.oas": "导入OpenAPI定义", "Apis.Details.APIDefinition.APIDefinition.import.definition.wsdl": "导入WSDL", "Apis.Details.APIDefinition.APIDefinition.import.wsdl": "导入WSDL", "Apis.Details.APIDefinition.APIDefinition.save.api.definition": "保存API定义", "Apis.Details.APIDefinition.APIDefinition.schema.definition": "模式定义", "Apis.Details.APIDefinition.Addservice.service.retrieve.error": "检索服务时发生错误", "Apis.Details.APIDefinition.AuditApi.ApiSecurityAuditReport": "API安全审计报告", "Apis.Details.APIDefinition.AuditApi.AuditScoreSummary": "审计分数与摘要", "Apis.Details.APIDefinition.AuditApi.DataValidation": "数据验证", "Apis.Details.APIDefinition.AuditApi.DataValidationNumOfIssues": "{dataNumOfIssuesText} {dataNumOfIssues}", "Apis.Details.APIDefinition.AuditApi.DataValidationScore": "{dataScoreText} {dataScore} / 70", "Apis.Details.APIDefinition.AuditApi.DataValidationSummary": "{dataValidationSummary}", "Apis.Details.APIDefinition.AuditApi.FailedToValidate.Content": "请修复下方显示的关键错误并重新运行审计。", "Apis.Details.APIDefinition.AuditApi.FailedToValidate.Heading": "验证OpenAPI文件失败", "Apis.Details.APIDefinition.AuditApi.GetReportError": "检索API安全报告时发生错误", "Apis.Details.APIDefinition.AuditApi.LinkToDetailedReport": "{linkToDetailedReportText} {link} {afterLinkText}", "Apis.Details.APIDefinition.AuditApi.OASNoIssuesFound": "未发现问题", "Apis.Details.APIDefinition.AuditApi.OpenApiFormatRequirements": "OpenAPI格式要求", "Apis.Details.APIDefinition.AuditApi.OverallCriticality": "{overallCriticalityText} {overallCriticality}", "Apis.Details.APIDefinition.AuditApi.OverallScoreProgress": "{overallScore}", "Apis.Details.APIDefinition.AuditApi.ReferenceSection": "访问此{link}查看该问题的详细描述、可能的利用方式及修复方法。", "Apis.Details.APIDefinition.AuditApi.ScoreFooter": "满分100分", "Apis.Details.APIDefinition.AuditApi.Security": "安全", "Apis.Details.APIDefinition.AuditApi.SecurityNumOfIssues": "{securityNumOfIssuesText} {securityNumOfIssues}", "Apis.Details.APIDefinition.AuditApi.SecurityScore": "{securityScoreText} {securityScore} / 30", "Apis.Details.APIDefinition.AuditApi.SecuritySummary": "{securitySummary}", "Apis.Details.APIDefinition.AuditApi.TotalNumOfErrors": "{totalNumOfErrorsText} {totalNumOfErrors}", "Apis.Details.APIDefinition.AuditApi.WaitForReport": "请稍候...", "Apis.Details.APIDefinition.AuditApi.WaitForReport.Content": "首次审计API需要一些时间", "Apis.Details.APIDefinition.AuditApi.dataCriticality": "{dataCriticalityText} {dataCriticality}", "Apis.Details.APIDefinition.AuditApi.overallScore": "{overallScoreText} {overallScore} / 100", "Apis.Details.APIDefinition.AuditApi.securityCriticality": "{securityCriticalityText} {securityCriticality}", "Apis.Details.APIDefinition.AuditApi.table.best.practices": "最佳实践问题", "Apis.Details.APIDefinition.AuditApi.table.issues": "问题", "Apis.Details.APIDefinition.AuditApi.table.semantic.errors": "语义错误", "Apis.Details.APIDefinition.AuditApi.table.structural.errors": "结构错误", "Apis.Details.APIDefinition.AuditApi.tooltip.critical": "5. 严重", "Apis.Details.APIDefinition.AuditApi.tooltip.high": "4. <PERSON>", "Apis.Details.APIDefinition.AuditApi.tooltip.info": "1. 信息", "Apis.Details.APIDefinition.AuditApi.tooltip.low": "2. 低", "Apis.Details.APIDefinition.AuditApi.tooltip.medium": "3. 中", "Apis.Details.APIDefinition.AuditApi.tooltip.severity": "严重程度范围：", "Apis.Details.APIDefinition.DefinitionOutdated.api.outdated.definition": "当前API定义已过时。您可以重新导入新定义或创建此API的新版本。", "Apis.Details.APIDefinition.DefinitionOutdated.btn.cancel": "取消", "Apis.Details.APIDefinition.DefinitionOutdated.btn.hide.diff": "隐藏差异", "Apis.Details.APIDefinition.DefinitionOutdated.btn.reimport": "重新导入", "Apis.Details.APIDefinition.DefinitionOutdated.btn.show.diff": "显示差异", "Apis.Details.APIDefinition.DefinitionOutdated.import.error": "重新导入API定义时出错", "Apis.Details.APIDefinition.DefinitionOutdated.import.success": "API定义重新导入成功！", "Apis.Details.APIDefinition.DefinitionOutdated.new.api.definition.error": "检索新API定义时发生错误。", "Apis.Details.APIDefinition.DefinitionOutdated.outdated.definition": "过时的定义", "Apis.Details.APIDefinition.DefinitionOutdated.outdated.definition.heading": "过时的定义", "Apis.Details.APIDefinition.DefinitionOutdated.service.retrieve.error": "渲染API定义差异时发生错误", "Apis.Details.APIDefinition.ImportDefinition.WSDL.updated.successfully": "WSDL更新成功", "Apis.Details.APIDefinition.ImportDefinition.error.updating.WSDL": "更新WSDL时出错", "Apis.Details.APIDefinition.Linting.APILinting.loading": "代码检查中...", "Apis.Details.APIDefinition.Linting.APILintingSummary.loading": "代码检查中...", "Apis.Details.APIDefinition.Linting.Linting.custom.ruleset.validation.failed": "OpenAPI检查器自定义规则集验证失败", "Apis.Details.APIDefinition.Linting.Linting.default.ruleset.validation.failed": "OpenAPI检查器默认规则集验证失败", "Apis.Details.APIDefinition.Linting.Linting.error.retrieving.custom.rules": "检索自定义检查规则集时出错", "Apis.Details.APIDefinition.SwaggerEditorDrawer.linter.goodupdate.content": "定义中未发现代码检查问题", "Apis.Details.APIDefinition.SwaggerEditorDrawer.linter.no.resultsupdate.content": "未发现{type}代码检查结果", "Apis.Details.APIDefinition.SwaggerEditorDrawer.title": "验证OpenAPI文件失败", "Apis.Details.APIDefinition.WSDL.download.definition": "下载WSDL", "Apis.Details.APIDefinition.WSDL.download.error": "下载WSDL ZIP文件时出错", "Apis.Details.APIDefinition.WSDL.error.loading.wsdl": "加载WSDL时出错", "Apis.Details.APIDefinition.WSDL.error.loading.wsdl.info": "加载WSDL时出错", "Apis.Details.APIDefinition.WSDL.preview.not.available": "API包含WSDL ZIP文件，因此预览不可用。", "Apis.Details.APIDefinition.WSDL.update.not.allowed": "未授权：更新WSDL定义的权限不足", "Apis.Details.APIDefinition.WSDL.wsdl.definition": "WSDL定义", "Apis.Details.APIDefinition.import.asyncAPI": "导入AsyncAPI", "Apis.Details.APIDefinition.info.updating.auditapi": "若要反映所做更改，需点击'审计API'", "Apis.Details.APIDetailsTopMenu.download.api": "下载API", "Apis.Details.APILevelRateLimitingPolicies.components.Configuration.tooltip": "所选速率限制策略将应用于此API的所有请求。", "Apis.Details.APIProduct.NewVersion.NewVersion.error": "创建新版本时发生错误！错误：", "Apis.Details.APIProduct.NewVersion.NewVersion.success": "新版本创建成功", "Apis.Details.AccessControl.roles.help": "输入有效角色并按回车键", "Apis.Details.ApiChat.components.ApiChatExecute.disclaimer.label": "由于语言模型可能存在一定不可预测性，建议保持谨慎和深思熟虑。", "Apis.Details.ApiChat.components.ApiChatExecute.queryInput.placeholder": "描述您的API设计要求...", "Apis.Details.ApiChat.components.ApiChatPoweredBy.apiChatMainHeader": "API设计助手", "Apis.Details.ApiChat.components.ApiChatResponse.CopyToClipboard.copiedText": "已复制", "Apis.Details.ApiChat.components.ApiChatResponse.CopyToClipboard.copyText": "复制cURL到剪贴板", "Apis.Details.ApiChat.components.CopyToClipboard.copiedText": "已复制", "Apis.Details.ApiChat.components.CopyToClipboard.copyText": "复制到剪贴板", "Apis.Details.ApiChat.components.SampleQueryCard.executeButton": "尝试", "Apis.Details.ApiChat.warning.authTokenMissing": "必须提供令牌才能使用API设计助手。获取步骤请参阅{apiAiChatDocLink}", "Apis.Details.ApiConsole.ApiConsole.title": "测试", "Apis.Details.ApiConsole.adv.auth.header": "授权头", "Apis.Details.ApiConsole.adv.auth.header.value": "授权头值", "Apis.Details.ApiConsole.authentication.heading": "身份验证", "Apis.Details.ApiConsole.deployments.api.gateways": "API网关", "Apis.Details.ApiConsole.deployments.isAPIRetired": "无法测试已停用的API！", "Apis.Details.ApiConsole.deployments.no": "{artifactType}尚未部署！测试前请先部署{artifactType}", "Apis.Details.ApiConsole.endpoint": "端点类型", "Apis.Details.ApiConsole.endpoint.help": "请选择端点类型", "Apis.Details.ApiConsole.enpoint.heading": "API端点", "Apis.Details.ApiConsole.environment": "环境", "Apis.Details.ApiConsole.generate.test.key": "生成密钥", "Apis.Details.BusinessInformation.BusinessInformation.APIProduct.sub.heading": "API产品的业务信息", "Apis.Details.BusinessInformation.BusinessInformation.business.info": "业务信息", "Apis.Details.BusinessInformation.BusinessInformation.business.owner.email": "业务负责人邮箱", "Apis.Details.BusinessInformation.BusinessInformation.business.owner.email.helper.text": "提供业务负责人的邮箱", "Apis.Details.BusinessInformation.BusinessInformation.business.owner.email.helper.text.error": "请输入有效的邮箱地址", "Apis.Details.BusinessInformation.BusinessInformation.business.owner.name": "业务负责人", "Apis.Details.BusinessInformation.BusinessInformation.business.owner.name.helper.text": "提供业务负责人的姓名", "Apis.Details.BusinessInformation.BusinessInformation.sub.heading": "API的业务信息", "Apis.Details.BusinessInformation.BusinessInformation.technical.owner.email": "技术负责人邮箱", "Apis.Details.BusinessInformation.BusinessInformation.technical.owner.email.helper.text": "提供技术负责人的邮箱", "Apis.Details.BusinessInformation.BusinessInformation.technical.owner.email.helper.text.error": "请输入有效的邮箱地址", "Apis.Details.BusinessInformation.BusinessInformation.technical.owner.name": "技术负责人", "Apis.Details.BusinessInformation.BusinessInformation.technical.owner.name.helper.text": "提供技术负责人的姓名", "Apis.Details.Comments.Comment.Add.blank.error": "不能输入空白评论", "Apis.Details.Comments.Comment.Add.error": "添加评论时发生错误", "Apis.Details.Comments.Comment.delete.confirm": "确定删除此评论吗？", "Apis.Details.Comments.Comment.delete.confirm.cancel.label": "取消", "Apis.Details.Comments.Comment.delete.confirm.title": "确认删除", "Apis.Details.Comments.Comment.delete.confirm.yes.label": "是", "Apis.Details.Comments.Comment.delete.success": "评论已成功删除", "Apis.Details.Comments.Comment.load.more.replies": "显示更多回复", "Apis.Details.Comments.Comment.something.went.wrong": "删除评论时发生错误", "Apis.Details.Comments.CommentAdd.btn.add.comment": "评论", "Apis.Details.Comments.CommentAdd.btn.cancel": "取消", "Apis.Details.Comments.CommentAdd.write.comment.help": "撰写评论", "Apis.Details.Comments.CommentAdd.write.comment.label": "撰写评论", "Apis.Details.Comments.CommentEdit.error": "添加评论时发生错误", "Apis.Details.Comments.CommentEdit.is.blank": "不能提交空白评论", "Apis.Details.Comments.CommentEdit.write.placeholder": "撰写评论", "Apis.Details.Comments.CommentOptions.delete": "删除", "Apis.Details.Comments.CommentOptions.reply": "回复", "Apis.Details.Comments.delete.comment.error": "删除评论时发生错误 - {commentIdOfCommentToDelete}", "Apis.Details.Comments.load.previous.comments": "显示更多", "Apis.Details.Comments.no.comments": "暂无评论", "Apis.Details.Comments.no.comments.content": "此API暂无可用评论", "Apis.Details.Comments.reply.delete.success": "回复评论已成功删除", "Apis.Details.Comments.retrieve.error": "获取评论时发生错误", "Apis.Details.Comments.title": "评论", "Apis.Details.Compliance.PolicyAdherence.column.policy": "策略", "Apis.Details.Compliance.PolicyAdherence.column.rulesets": "规则集", "Apis.Details.Compliance.PolicyAdherence.column.status": "状态", "Apis.Details.Compliance.PolicyAdherence.empty.helper": "此API尚未应用治理策略。", "Apis.Details.Compliance.PolicyAdherence.empty.title": "未应用策略", "Apis.Details.Compliance.PolicyAdherence.followed.count": "{followed}/{total} 已遵循", "Apis.Details.Compliance.PolicyAdherence.not.applied": "不适用 - 未应用策略", "Apis.Details.Compliance.PolicyAdherence.pending": "不适用 - 等待策略评估", "Apis.Details.Compliance.RuleViolation.column.description": "描述", "Apis.Details.Compliance.RuleViolation.column.message": "消息", "Apis.Details.Compliance.RuleViolation.column.path": "路径", "Apis.Details.Compliance.RuleViolation.column.rule": "规则", "Apis.Details.Compliance.RuleViolation.documentation": "查看文档", "Apis.Details.Compliance.RuleViolation.empty.errors": "未找到错误违规项", "Apis.Details.Compliance.RuleViolation.empty.info": "未找到信息违规项", "Apis.Details.Compliance.RuleViolation.empty.passed": "未找到通过规则", "Apis.Details.Compliance.RuleViolation.empty.warnings": "未找到警告违规项", "Apis.Details.Compliance.RuleViolation.tab.errors": "错误({count})", "Apis.Details.Compliance.RuleViolation.tab.info": "信息({count})", "Apis.Details.Compliance.RuleViolation.tab.passed": "通过({count})", "Apis.Details.Compliance.RuleViolation.tab.warnings": "警告({count})", "Apis.Details.Compliance.RulesetAdherence.column.ruleset": "规则集", "Apis.Details.Compliance.RulesetAdherence.column.status": "状态", "Apis.Details.Compliance.RulesetAdherence.column.violations": "违规项", "Apis.Details.Compliance.RulesetAdherence.empty.helper": "未为此API应用任何治理规则集。", "Apis.Details.Compliance.RulesetAdherence.empty.title": "未找到规则集", "Apis.Details.Compliance.RulesetAdherence.violations.tooltip": "错误：{error}，警告：{warn}，信息：{info}", "Apis.Details.Compliance.check.progress": "合规性检查正在进行中", "Apis.Details.Compliance.check.progress.message": "合规性检查正在进行中。这可能需要一些时间。", "Apis.Details.Compliance.failed": "失败", "Apis.Details.Compliance.followed": "已遵循（{count}）", "Apis.Details.Compliance.not.applicable.message": "此API未附加治理策略。", "Apis.Details.Compliance.not.applied": "未应用（{count}）", "Apis.Details.Compliance.passed": "已通过", "Apis.Details.Compliance.pending": "待处理（{count}）", "Apis.Details.Compliance.policy.adherence": "策略遵循情况", "Apis.Details.Compliance.policy.adherence.summary": "策略遵循摘要", "Apis.Details.Compliance.revision.message": "API修订版本不可用合规性摘要。请导航至当前API版本查看合规性摘要。", "Apis.Details.Compliance.rule.adherence": "规则遵循情况", "Apis.Details.Compliance.rules.errors": "错误（{count}）", "Apis.Details.Compliance.rules.info": "信息（{count}）", "Apis.Details.Compliance.rules.passed": "已通过（{count}）", "Apis.Details.Compliance.rules.warnings": "警告（{count}）", "Apis.Details.Compliance.ruleset.adherence": "规则集遵循情况", "Apis.Details.Compliance.ruleset.adherence.summary": "规则集遵循摘要", "Apis.Details.Compliance.topic.header": "合规性摘要", "Apis.Details.Compliance.unapplied": "未应用", "Apis.Details.Compliance.violated": "已违反（{count}）", "Apis.Details.Components.SOAP.To.REST.edit.btn": "编辑", "Apis.Details.Components.SOAP.To.REST.tabs.In.text": "入站", "Apis.Details.Components.SOAP.To.REST.tabs.Out.text": "出站", "Apis.Details.Components.SOAP.To.REST.transformation.text": "转换配置", "Apis.Details.Components.async.api.add.property.description.helper.text": "输入属性描述", "Apis.Details.Components.async.api.add.property.description.text": "描述", "Apis.Details.Components.async.api.add.property.select.data.type": "选择数据类型", "Apis.Details.Configurartion.components.QueryAnalysis": "查询分析", "Apis.Details.Configurartion.components.QueryAnalysis.cancle.btn": "取消", "Apis.Details.Configurartion.components.QueryAnalysis.edit": "编辑复杂度值", "Apis.Details.Configurartion.components.QueryAnalysis.update.complexity": "更新复杂度", "Apis.Details.Configuration\n                                        .Configuration.Design.APIProduct.sub.heading": "配置基础API产品元信息", "Apis.Details.Configuration.ApiKeyHeader.helper.text": "ApiKey头名称不能包含空格或特殊字符", "Apis.Details.Configuration.AuthHeader.helper.text": "授权头名称不能包含空格或特殊字符", "Apis.Details.Configuration.Components.AI.BE.Rate.Limiting.prod": "后端速率限制", "Apis.Details.Configuration.Components.AI.BE.Rate.Limiting.tooltip": "此选项决定应用于API的后端速率限制类型。", "Apis.Details.Configuration.Components.APISecurity.Components.\n                                            ApplicationLevel.Client.Websocket": "客户端Websocket", "Apis.Details.Configuration.Components.APISecurity.Components.\n                                            ApplicationLevel.Websocket": "应用级安全", "Apis.Details.Configuration.Components.APISecurity.Components.ApplicationLevel.http": "应用级安全", "Apis.Details.Configuration.Components.APISecurity.Components.ApplicationLevel.security.scheme.api.key": "API密钥", "Apis.Details.Configuration.Components.APISecurity.Components.ApplicationLevel.security.scheme.basic": "基本认证", "Apis.Details.Configuration.Components.APISecurity.Components.ApplicationLevel.security.scheme.mandatory": "强制", "Apis.Details.Configuration.Components.APISecurity.Components.ApplicationLevel.security.scheme.oauth2": "OAuth2", "Apis.Details.Configuration.Components.APISecurity.Components.ApplicationLevel.security.scheme.optional": "可选", "Apis.Details.Configuration.Components.APISecurity.Components.TransportLevel.transport.level.security": "传输层安全", "Apis.Details.Configuration.Components.APISecurity.Components.TransportLevel.transport.level.security.mutual.ssl.mandatory": "强制", "Apis.Details.Configuration.Components.APISecurity.Components.TransportLevel.transport.level.security.mutual.ssl.optional": "可选", "Apis.Details.Configuration.Components.MaxBackendTps.maximum.backend.throughput": "后端吞吐量", "Apis.Details.Configuration.Components.MaxBackendTps.maximum.throughput.field": "最大吞吐量", "Apis.Details.Configuration.Components.validate.role.error": "验证角色时出错：{role}", "Apis.Details.Configuration.Configuration.ApiKeyHeader.tooltip": "用于发送API密钥信息的头名称。默认为\"ApiKey\"。", "Apis.Details.Configuration.Configuration.AuthHeader.tooltip": "用于发送授权信息的头名称。默认为\"Authorization\"。", "Apis.Details.Configuration.Configuration.Design.attached.labels": "已附加标签", "Apis.Details.Configuration.Configuration.Design.no.labels": "未附加标签", "Apis.Details.Configuration.Configuration.Design.no.labels.found": "未找到标签", "Apis.Details.Configuration.Configuration.Design.no.more.labels": "无更多可用标签", "Apis.Details.Configuration.Configuration.Design.sub.heading": "配置基础API元信息", "Apis.Details.Configuration.Configuration.Design.topic.header": "设计配置", "Apis.Details.Configuration.Configuration.Design.topic.label": "标签", "Apis.Details.Configuration.Configuration.Design.unattached.labels": "未附加标签", "Apis.Details.Configuration.Configuration.Endpoints.edit.api.endpoints": "编辑API端点", "Apis.Details.Configuration.Configuration.apiKey.header.label": "<PERSON><PERSON><PERSON><PERSON>头", "Apis.Details.Configuration.Configuration.auth.header.label": "授权头", "Apis.Details.Configuration.Configuration.cache.timeout": "缓存超时（秒）", "Apis.Details.Configuration.Configuration.cancel": "取消", "Apis.Details.Configuration.Configuration.defaultversion.tooltip": "指示此版本是否为API的默认版本。若调用API时未指定版本，API网关将路由请求至默认版本。", "Apis.Details.Configuration.Configuration.isdefault.label": "设为默认版本", "Apis.Details.Configuration.Configuration.isdefault.no": "否", "Apis.Details.Configuration.Configuration.isdefault.yes": "是", "Apis.Details.Configuration.Configuration.publisher": "发布", "Apis.Details.Configuration.Configuration.requirements": "需求", "Apis.Details.Configuration.Configuration.requirements.state.transition": "下一状态转换需求", "Apis.Details.Configuration.Configuration.response.caching": "响应缓存", "Apis.Details.Configuration.Configuration.save": "保存", "Apis.Details.Configuration.Configuration.schema.validation": "架构验证", "Apis.Details.Configuration.Configuration.section.backend": "后端", "Apis.Details.Configuration.Configuration.section.events": "事件", "Apis.Details.Configuration.Configuration.section.initial.request": "初始请求", "Apis.Details.Configuration.Configuration.section.request": "请求", "Apis.Details.Configuration.Configuration.section.response": "响应", "Apis.Details.Configuration.Configuration.transports": "传输协议", "Apis.Details.Configuration.Design.Configurations.error.occured": "发生错误", "Apis.Details.Configuration.Design.Configurations.error.updating": "更新设计配置时发生错误", "Apis.Details.Configuration.Resources.operation.already.exist.error": "操作已存在！", "Apis.Details.Configuration.Resources.operation.api.update.error": "更新API时出错", "Apis.Details.Configuration.Resources.operation.definition.update.error": "更新定义时出错", "Apis.Details.Configuration.Resources.operation.required": "API至少需要一个操作", "Apis.Details.Configuration.Resources.operation.verbs.already.exist.error": "操作已存在（数据目标：{data_target}，当前动词：{currentVerb}）", "Apis.Details.Configuration.Resources.reset": "重置", "Apis.Details.Configuration.Resources.reset.dialog.close.btn": "关闭", "Apis.Details.Configuration.Resources.reset.dialog.content": "请确认放弃所有更改操作", "Apis.Details.Configuration.Resources.reset.dialog.reset.btn": "重置", "Apis.Details.Configuration.Resources.reset.dialog.title": "放弃更改", "Apis.Details.Configuration.Resources.save": "保存", "Apis.Details.Configuration.RuntimeConfiguration.backend.api.product.endpoint": "请参考相应API获取端点信息", "Apis.Details.Configuration.RuntimeConfiguration.no.km.error": "请选择一个或多个密钥管理器", "Apis.Details.Configuration.RuntimeConfiguration.topic.header": "运行时配置", "Apis.Details.Configuration.RuntimeConfigurationWebSocket.section.backend.websocket": "后端Websocket", "Apis.Details.Configuration.RuntimeConfigurationWebSocket.topic.header": "运行时配置", "Apis.Details.Configuration.RuntimeConfigurationWebsocket.RuntimeConfiguration.emptySchemes": "请至少选择一种API安全方法。", "Apis.Details.Configuration.Topic.already.exist.error": "操作已存在！", "Apis.Details.Configuration.Topic.already.opreation.verb.exist.error": "操作已存在（数据目标：{data_target}，当前动词：{current_Verb}）", "Apis.Details.Configuration.Topic.update.api.error": "更新API时出错", "Apis.Details.Configuration.Topic.update.definition.error": "更新定义时出错", "Apis.Details.Configuration.UpdateWithoutDetails.cancel": "取消", "Apis.Details.Configuration.UpdateWithoutDetails.confirm.update.message": "此API当前已发布，改为常规API后将不可用，因其无有效部署。", "Apis.Details.Configuration.UpdateWithoutDetails.continue": "继续", "Apis.Details.Configuration.UpdateWithoutDetails.dialog.title": "恢复为常规API", "Apis.Details.Configuration.UpdateWithoutDetails.endpoint": "端点", "Apis.Details.Configuration.UpdateWithoutDetails.endpoint.error": "无效端点URL", "Apis.Details.Configuration.UpdateWithoutDetails.tier.message": "商业计划：", "Apis.Details.Configuration.components.APILevelRateLimitingPolicies.configuration": "速率限制配置", "Apis.Details.Configuration.components.APISecurity.TranportLevel.certificate.add.success": "证书添加成功", "Apis.Details.Configuration.components.APISecurity.TranportLevel.certificate.alias.error": "添加API证书时发生错误", "Apis.Details.Configuration.components.APISecurity.TranportLevel.certificate.delete.error": "删除证书时出错", "Apis.Details.Configuration.components.APISecurity.TranportLevel.certificate.delete.success": "证书删除成功", "Apis.Details.Configuration.components.APISecurity.api.unsecured": "因API无受保护资源，无需应用级安全", "Apis.Details.Configuration.components.APISecurity.application.mandatory": "选择应用级安全是否为强制或可选", "Apis.Details.Configuration.components.APISecurity.emptySchemas": "请至少选择一种API安全方法！", "Apis.Details.Configuration.components.APISecurity.http.mandatory": "选择传输层安全是否为强制或可选", "Apis.Details.Configuration.components.APISecurity.tooltip": "此选项决定用于保护API的安全类型。API可通过OAuth2/基本认证/ApiKey单独或组合保护。选择OAuth2时需有效令牌方可调用。", "Apis.Details.Configuration.components.AccessControl.dropdown.none": "全部", "Apis.Details.Configuration.components.AccessControl.dropdown.restricted": "按角色限制", "Apis.Details.Configuration.components.AccessControl.form.helper.text": "默认无访问限制", "Apis.Details.Configuration.components.AccessControl.head.topic": "发布者访问控制", "Apis.Details.Configuration.components.AccessControl.roles": "角色", "Apis.Details.Configuration.components.AccessControl.tooltip.all": "全部：", "Apis.Details.Configuration.components.AccessControl.tooltip.all.desc": "所有发布者和创建者均可查看和修改此API。", "Apis.Details.Configuration.components.AccessControl.tooltip.restrict": "按角色限制：", "Apis.Details.Configuration.components.AccessControl.tooltip.restrict.desc": "仅限指定角色的发布者和创建者查看和修改此API。", "Apis.Details.Configuration.components.AdvertiseInfo.advertised.label": "标记为第三方API", "Apis.Details.Configuration.components.AdvertiseInfo.advertised.no": "否", "Apis.Details.Configuration.components.AdvertiseInfo.advertised.yes": "是", "Apis.Details.Configuration.components.AdvertiseInfo.apiExternalEndpoint.error": "无效端点URL", "Apis.Details.Configuration.components.AdvertiseInfo.apiExternalProductionEndpoint": "API外部生产端点", "Apis.Details.Configuration.components.AdvertiseInfo.apiExternalProductionEndpoint.help": "第三方API的外部生产端点", "Apis.Details.Configuration.components.AdvertiseInfo.apiExternalSandboxEndpoint": "API外部沙盒端点", "Apis.Details.Configuration.components.AdvertiseInfo.apiExternalSandboxEndpoint.help": "第三方API的外部沙盒端点", "Apis.Details.Configuration.components.AdvertiseInfo.async.api.warning": "若需在网关部署API，请创建WebSocket、SSE或WebSub类型的流式API。", "Apis.Details.Configuration.components.AdvertiseInfo.async.api.warning.title": "\"其他\"类型流式API将作为第三方API使用。", "Apis.Details.Configuration.components.AdvertiseInfo.deployed.api.warning": "请取消部署当前修订版本后再将API改为第三方API。", "Apis.Details.Configuration.components.AdvertiseInfo.deployed.api.warning.title": "存在活跃的API部署。", "Apis.Details.Configuration.components.AdvertiseInfo.originalDevPortalUrl": "原始开发者门户URL", "Apis.Details.Configuration.components.AdvertiseInfo.originalDevPortalUrl.error": "无效原始开发者门户URL", "Apis.Details.Configuration.components.AdvertiseInfo.originalDevPortalUrl.help": "第三方API的原始开发者门户URL", "Apis.Details.Configuration.components.AdvertiseInfo.tooltip": "指示API是否为第三方API。可通过API管理器暴露外部发布的API。", "Apis.Details.Configuration.components.Audience.Validation.Title": "受众验证", "Apis.Details.Configuration.components.Audience.Validation.helper": "输入受众值后按`Enter`以添加新受众", "Apis.Details.Configuration.components.Audience.Validation.values": "允许的受众", "Apis.Details.Configuration.components.CORSConfiguration.allow.all.origins": "允许所有来源", "Apis.Details.Configuration.components.CORSConfiguration.allow.credentials": "访问控制允许凭证", "Apis.Details.Configuration.components.CORSConfiguration.allow.headers": "访问控制允许头", "Apis.Details.Configuration.components.CORSConfiguration.allow.methods": "访问控制允许方法", "Apis.Details.Configuration.components.CORSConfiguration.allow.origins": "访问控制允许来源", "Apis.Details.Configuration.components.CORSConfiguration.cors.configuration": "CORS配置", "Apis.Details.Configuration.components.CORSConfiguration.tooltip": "启用后，将开启API的CORS配置。", "Apis.Details.Configuration.components.CORSConfigurations.header.helper": "输入头名称后按`Enter`以添加新头", "Apis.Details.Configuration.components.CORSConfigurations.method.helper": "输入方法名称后按`Enter`以添加新方法", "Apis.Details.Configuration.components.CORSConfigurations.origin.helper": "输入来源名称后按`Enter`以添加新来源", "Apis.Details.Configuration.components.Description.help": "此描述将在开发者门户的API概览页面显示", "Apis.Details.Configuration.components.Description.title": "描述", "Apis.Details.Configuration.components.DescriptionEditor.edit.content.button": "编辑概述Markdown", "Apis.Details.Configuration.components.DescriptionEditor.edit.description.of": "概述：", "Apis.Details.Configuration.components.DescriptionEditor.markdown.help": "此选项允许用下方内容替换开发者门户概览页面的默认内容。清空文本后点击`更新内容`并保存即可恢复默认。", "Apis.Details.Configuration.components.DescriptionEditor.update.cont.button": "更新内容", "Apis.Details.Configuration.components.Endpoints.dynamic": "动态", "Apis.Details.Configuration.components.Endpoints.endpoints": "端点", "Apis.Details.Configuration.components.Endpoints.not.set": "-", "Apis.Details.Configuration.components.Endpoints.production": "生产环境", "Apis.Details.Configuration.components.Endpoints.prototype": "原型", "Apis.Details.Configuration.components.Endpoints.sandbox": "沙箱", "Apis.Details.Configuration.components.Endpoints.sandbox.not.set": "-", "Apis.Details.Configuration.components.KeyManager.allow.all": "允许全部", "Apis.Details.Configuration.components.KeyManager.allow.selected": "允许选定", "Apis.Details.Configuration.components.KeyManager.configuration": "密钥管理器配置", "Apis.Details.Configuration.components.KeyManager.more.than.one.error": "请至少选择一个密钥管理器", "Apis.Details.Configuration.components.KeyManager.more.than.one.info": "请选择一个或多个密钥管理器", "Apis.Details.Configuration.components.MaxBackendTps.formattedMessage": "后端每秒最大事务数（整数）", "Apis.Details.Configuration.components.MaxBackendTps.max.throughput.specify": "指定", "Apis.Details.Configuration.components.MaxBackendTps.max.throughput.specify.max.prod.tps": "最大生产环境TPS", "Apis.Details.Configuration.components.MaxBackendTps.max.throughput.specify.max.request.count": "最大请求数", "Apis.Details.Configuration.components.MaxBackendTps.max.throughput.specify.max.sandbox.tps": "最大沙箱环境TPS", "Apis.Details.Configuration.components.MaxBackendTps.max.throughput.unlimited": "无限制", "Apis.Details.Configuration.components.MaxBackendTps.tooltip": "限制API管理器允许向后端发起的调用总数", "Apis.Details.Configuration.components.ResponseCaching.tooltip": "启用后将在网关级别缓存API响应，以提高响应速度并减轻后端负载", "Apis.Details.Configuration.components.SchemaValidation.btn.no": "否", "Apis.Details.Configuration.components.SchemaValidation.btn.yes": "是", "Apis.Details.Configuration.components.SchemaValidation.description": "启用JSON模式验证将导致在每个请求和响应中构建有效载荷，这会影响API请求的往返时间！", "Apis.Details.Configuration.components.SchemaValidation.description.question": "是否启用模式验证？", "Apis.Details.Configuration.components.SchemaValidation.title": "注意！", "Apis.Details.Configuration.components.Share.API.no.organizations": "当前组织下未注册其他组织用于共享API", "Apis.Details.Configuration.components.Shared.Organizations.label": "选择与组织共享API的选项", "Apis.Details.Configuration.components.Social.giturl": "GitHub地址", "Apis.Details.Configuration.components.Social.giturl.help": "此GitHub地址将在开发者门户的API概览页面显示", "Apis.Details.Configuration.components.Social.slack": "Slack频道地址", "Apis.Details.Configuration.components.Social.slack_url.help": "此Slack频道地址将在开发者门户的API概览页面显示", "Apis.Details.Configuration.components.StoreVisibility.dropdown.public": "公开", "Apis.Details.Configuration.components.Subscription.algorithm.helper.text": "选择用于签名消息的算法", "Apis.Details.Configuration.components.Subscription.configuration": "订阅配置", "Apis.Details.Configuration.components.Subscription.enable.switch": "启用", "Apis.Details.Configuration.components.Subscription.http.header.helper.text": "设置提供者用于发送签名的HTTP头", "Apis.Details.Configuration.components.Subscription.secret": "密钥", "Apis.Details.Configuration.components.Subscription.secret.generate.btn": "生成", "Apis.Details.Configuration.components.Subscription.secret.helper.text": "在提供者注册时使用上述密钥", "Apis.Details.Configuration.components.Subscription.signature.header": "签名头", "Apis.Details.Configuration.components.Subscription.signingAlgorithm": "签名算法", "Apis.Details.Configuration.components.Tags.error": "标签包含一个或多个非法字符（ ~ ! @ # ; % ^ & * + = | < > , ' \" \\\\ / ）。", "Apis.Details.Configuration.components.Tags.helper": "输入标签名称后按`Enter`键添加新标签", "Apis.Details.Configuration.components.Tags.limit.error": "标签长度超过30个字符的最大限制", "Apis.Details.Configuration.components.Tags.title": "标签", "Apis.Details.Configuration.components.Transports.tooltip": "如果选择了双向SSL选项，则需要提供受信任的客户端证书才能访问API", "Apis.Details.Configuration.components.WebSubConfiguration.configuration": "WebSub配置", "Apis.Details.Configuration.components.WebSubConfiguration.configuration.subVerification": "启用订阅者验证", "Apis.Details.Configuration.components.WebSubConfiguration.configuration.subVerification.tooltip": "如果启用，APIM将执行订阅API的意图验证", "Apis.Details.Configuration.components.oauth.disabled": "仅当启用OAuth2安全性时，密钥管理器配置才有效。", "Apis.Details.Configuration.components.schema.validation.tooltip": "启用针对OpenAPI定义的请求和响应验证", "Apis.Details.Configuration.components.storeVisibility.dropdown.private": "仅对我的域可见", "Apis.Details.Configuration.components.storeVisibility.dropdown.restrict": "按角色限制", "Apis.Details.Configuration.components.storeVisibility.form.helper.text": "默认情况下，API对所有开发者门户用户可见", "Apis.Details.Configuration.components.storeVisibility.head.topic": "开发者门户可见性", "Apis.Details.Configuration.components.storeVisibility.roles": "角色", "Apis.Details.Configuration.components.storeVisibility.tooltip.public": "公开：", "Apis.Details.Configuration.components.storeVisibility.tooltip.public.desc": "该API对所有人可访问，并且可以在多个开发者门户中宣传——中央开发者门户和/或非WSO2开发者门户。", "Apis.Details.Configuration.components.storeVisibility.tooltip.restrict": "按角色限制：", "Apis.Details.Configuration.components.storeVisibility.tooltip.restrict.desc": "该API仅对您指定的租户开发者门户中的特定用户角色可见。", "Apis.Details.Configuration.components.transport.empty": "请至少选择一种传输方式！", "Apis.Details.Configuration.components.transport.sslHttps": "请选择Https作为双向SSL的传输方式！", "Apis.Details.Configurations.api.categories": "API分类", "Apis.Details.Configurations.api.categories.empty": "未定义API分类。", "Apis.Details.Configurations.api.categories.helper.text": "为API选择API分类", "Apis.Details.Configurations.api.categories.placeholder.text": "搜索API分类", "Apis.Details.Documents.Create.Edit.validate.doc.name.error": "验证文档名称时出错", "Apis.Details.Documents.Create.heading": "文档", "Apis.Details.Documents.Create.markdown.editor.add.document.button": "添加文档", "Apis.Details.Documents.Create.markdown.editor.add.document.cancel.button": "取消", "Apis.Details.Documents.Create.markdown.editor.add.error": "添加文档时出错", "Apis.Details.Documents.Create.markdown.editor.success": "添加成功。", "Apis.Details.Documents.Create.markdown.editor.upload.error": "上传文件时出错", "Apis.Details.Documents.Create.successful.file.upload.message": "文件上传成功。", "Apis.Details.Documents.Create.title": "添加新文档", "Apis.Details.Documents.CreateEditForm.document.content.info": "请保存文档。内容可以在下一步中编辑。", "Apis.Details.Documents.CreateEditForm.document.create.type": "类型", "Apis.Details.Documents.CreateEditForm.document.create.type.how.to": "操作指南", "Apis.Details.Documents.CreateEditForm.document.create.type.other": "其他", "Apis.Details.Documents.CreateEditForm.document.create.type.other.document.category": "其他文档类型 *", "Apis.Details.Documents.CreateEditForm.document.create.type.other.document.category.helper.text": "提供文档类型", "Apis.Details.Documents.CreateEditForm.document.create.type.other.error.document.category.helper.text": "文档类型不能为空", "Apis.Details.Documents.CreateEditForm.document.create.type.public.forum": "公共论坛", "Apis.Details.Documents.CreateEditForm.document.create.type.sample": "示例和SDK", "Apis.Details.Documents.CreateEditForm.document.create.type.support.forum": "支持论坛", "Apis.Details.Documents.CreateEditForm.document.docVisibility": "文档可见性", "Apis.Details.Documents.CreateEditForm.document.docVisibility.dropdown.ownerOnly": "仅所有者", "Apis.Details.Documents.CreateEditForm.document.docVisibility.dropdown.private": "私有", "Apis.Details.Documents.CreateEditForm.document.docVisibility.dropdown.public": "与API可见性相同", "Apis.Details.Documents.CreateEditForm.document.name": "名称 *", "Apis.Details.Documents.CreateEditForm.document.name.helper.text": "提供文档名称", "Apis.Details.Documents.CreateEditForm.document.summary": "摘要 *", "Apis.Details.Documents.CreateEditForm.document.summary.error.empty": "文档摘要不能为空", "Apis.Details.Documents.CreateEditForm.document.summary.helper.text": "提供文档的简要描述", "Apis.Details.Documents.CreateEditForm.duplicate.document.name.helper.text": "重复的文档名称", "Apis.Details.Documents.CreateEditForm.empty.document.name.helper.text": "文档名称不能为空", "Apis.Details.Documents.CreateEditForm.exceeds.document.name.length.helper.text": "文档名称超过60个字符的最大长度", "Apis.Details.Documents.CreateEditForm.invalid.document.name.helper.text": "文档名称不能包含特殊字符", "Apis.Details.Documents.CreateEditForm.source": "来源", "Apis.Details.Documents.CreateEditForm.source.file": "文件", "Apis.Details.Documents.CreateEditForm.source.inline": "内联", "Apis.Details.Documents.CreateEditForm.source.markdown": "<PERSON><PERSON>", "Apis.Details.Documents.CreateEditForm.source.url": "URL", "Apis.Details.Documents.CreateEditForm.source.url.helper.text": "提供来源的URL", "Apis.Details.Documents.CreateEditForm.source.url.helper.text.error.empty": "URL字段不能为空", "Apis.Details.Documents.CreateEditForm.source.url.helper.text.error.invalid": "输入有效的来源URL", "Apis.Details.Documents.CreateEditForm.source.url.url": "URL", "Apis.Details.Documents.Delete.document.delete": "删除", "Apis.Details.Documents.Delete.document.delete.error": "删除文档时出错！", "Apis.Details.Documents.Delete.document.delete.successfully": "删除成功。", "Apis.Details.Documents.Delete.document.listing.delete": "是的，删除", "Apis.Details.Documents.Delete.document.listing.delete.cancel": "取消", "Apis.Details.Documents.Delete.document.listing.delete.confirm": "确认删除", "Apis.Details.Documents.Delete.document.listing.delete.confirm.body": "所选文档将从API中删除。您将无法撤消此操作。", "Apis.Details.Documents.Delete.document.listing.delete.confirm.title": "删除文档", "Apis.Details.Documents.Delete.document.listing.delete.yes": "删除", "Apis.Details.Documents.Delete.document.listing.label.cancel": "取消", "Apis.Details.Documents.Delete.document.listing.label.ok.confirm": "您确定要删除该作用域 {scope} 吗？", "Apis.Details.Documents.Delete.document.listing.label.ok.yes": "是的", "Apis.Details.Documents.Delete.selected.document.listing.delete.confirm.body": "所选文档将从API中删除。您将无法撤消此操作。", "Apis.Details.Documents.Delete.selected.document.listing.delete.confirm.title": "删除所选文档", "Apis.Details.Documents.Download.documents.listing.download": "下载", "Apis.Details.Documents.Download.documents.markdown.editor.download.error": "下载文件时出错", "Apis.Details.Documents.Edit.documents.text.editor.cancel": "取消", "Apis.Details.Documents.Edit.documents.text.editor.edit": "编辑元数据", "Apis.Details.Documents.Edit.documents.text.editor.edit.content": "编辑", "Apis.Details.Documents.Edit.documents.text.editor.update.content": "保存", "Apis.Details.Documents.Edit.markdown.editor.update.error.message": "添加文档时出错", "Apis.Details.Documents.Edit.markdown.editor.update.success.message": "更新成功。", "Apis.Details.Documents.Edit.markdown.editor.upload.error.message": "上传文件时出错。", "Apis.Details.Documents.Edit.markdown.editor.upload.success.message": "文件上传成功。", "Apis.Details.Documents.GoToEdit.add.content": "添加内容", "Apis.Details.Documents.GoToEdit.back.to.listing": "返回列表", "Apis.Details.Documents.GoToEdit.description.content": "您可以向文档添加内容或返回文档列表页面。", "Apis.Details.Documents.GoToEdit.description.file": "您可以返回文档列表页面并通过编辑文档上传文件。", "Apis.Details.Documents.GoToEdit.title": "文档创建成功", "Apis.Details.Documents.Listing.APIProduct.add.new.msg.content": "您可以为API添加不同类型的文档。适当的文档有助于API发布者更好地推广其API并保持竞争力。", "Apis.Details.Documents.Listing.add.new.document.button": "添加新文档", "Apis.Details.Documents.Listing.add.new.msg.button": "添加新文档", "Apis.Details.Documents.Listing.add.new.msg.content": "您可以为API添加不同类型的文档。适当的文档有助于API发布者更好地推广其API并保持竞争力。", "Apis.Details.Documents.Listing.add.new.msg.title": "创建文档", "Apis.Details.Documents.Listing.column.header.action": "操作", "Apis.Details.Documents.Listing.column.header.name": "名称", "Apis.Details.Documents.Listing.column.header.source.type": "来源类型", "Apis.Details.Documents.Listing.column.header.type": "类型", "Apis.Details.Documents.Listing.documents.generated.title": "生成的文档", "Apis.Details.Documents.Listing.documents.listing.fetching.error.message": "获取API文档列表时出错", "Apis.Details.Documents.Listing.documents.listing.fetching.error.message.api.product": "获取API产品文档列表时出错", "Apis.Details.Documents.Listing.documents.listing.title": "文档", "Apis.Details.Documents.Listing.documents.open": "打开", "Apis.Details.Documents.Listing.documents.uploaded.title": "上传的文档", "Apis.Details.Documents.Listing.loading": "加载中...", "Apis.Details.Documents.MarkdownEditor.cancel.button": "取消", "Apis.Details.Documents.MarkdownEditor.edit.content": "编辑内容", "Apis.Details.Documents.MarkdownEditor.edit.content.of": "编辑内容于", "Apis.Details.Documents.MarkdownEditor.update.content.button": "更新内容", "Apis.Details.Documents.MarkdownEditor.update.success.message": "更新成功。", "Apis.Details.Documents.TextEditor.cancel.button": "取消", "Apis.Details.Documents.TextEditor.edit.content": "编辑内容", "Apis.Details.Documents.TextEditor.edit.content.of": "编辑内容于", "Apis.Details.Documents.TextEditor.update.content.button": "更新内容", "Apis.Details.Documents.TextEditor.update.error.message": "更新失败。", "Apis.Details.Documents.TextEditor.update.success.message": "更新成功。", "Apis.Details.Documents.View.btn.download": "下载", "Apis.Details.Documents.View.error.downloading": "下载文件时出错", "Apis.Details.Documents.View.heading": "文档", "Apis.Details.Documents.View.meta.catogery": "分类为", "Apis.Details.Documents.View.meta.name": "名称", "Apis.Details.Documents.View.meta.source": "来源类型", "Apis.Details.Documents.View.meta.summary": "摘要", "Apis.Details.Documents.ViewDocument.view.document": "查看文档", "Apis.Details.EditScopes.Roles.Invalid": "角色无效", "Apis.Details.Endpoints.\n                                                                GenericEndpoint.config.endpoint": "端点配置", "Apis.Details.Endpoints.\n                                                                GenericEndpoint.security.endpoint": "端点安全性", "Apis.Details.Endpoints..EndpointOverview.change.type.proceed": "继续", "Apis.Details.Endpoints.AIEndpoints.AIEndpoints.delete.primary.error": "无法删除主端点。请先移除主状态。", "Apis.Details.Endpoints.AIEndpoints.AIEndpoints.endpoint.delete.error": "删除端点时出错", "Apis.Details.Endpoints.AIEndpoints.AIEndpoints.endpoint.delete.success": "端点删除成功", "Apis.Details.Endpoints.AIEndpoints.AIEndpoints.fetch.error": "获取端点时出错", "Apis.Details.Endpoints.AIEndpoints.AIEndpoints.general.config.header": "通用端点配置", "Apis.Details.Endpoints.AIEndpoints.AIEndpoints.no.production.endpoints": "未配置生产端点", "Apis.Details.Endpoints.AIEndpoints.AIEndpoints.no.sandbox.endpoints": "未配置沙盒端点", "Apis.Details.Endpoints.AIEndpoints.AIEndpoints.primary.remove.error": "至少需要一个端点作为主端点。", "Apis.Details.Endpoints.AIEndpoints.AIEndpoints.primary.set.error": "设置主端点时出错", "Apis.Details.Endpoints.AIEndpoints.AIEndpoints.primary.set.success": "主端点更新成功", "Apis.Details.Endpoints.AIEndpoints.AIEndpoints.primary.update.error": "更新主端点时出错", "Apis.Details.Endpoints.AIEndpoints.AIEndpoints.primary.update.success": "主端点更新成功", "Apis.Details.Endpoints.AIEndpoints.AIEndpoints.production.endpoints.label": "生产端点", "Apis.Details.Endpoints.AIEndpoints.AIEndpoints.sandbox.endpoints.label": "沙盒端点", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.add.success": "端点添加成功", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.advanced.configurations": "高级配置", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.api.key.header": "授权头", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.api.key.placeholder": "输入API密钥", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.api.key.query.param": "授权查询参数", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.api.key.value": "API密钥", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.cancel": "取消", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.create.btn": "创建", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.create.new.endpoint": "添加新端点", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.edit.endpoint": "编辑端点", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.edit.success": "端点更新成功", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.endpoint.configuration": "端点配置", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.error.duplicate.name": "此端点名称已存在", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.error.empty.apiKey": "API密钥不能为空", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.error.empty.name": "端点名称不能为空", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.error.empty.url": "端点URL不能为空", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.error.invalid.url": "请输入有效的端点URL", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.error.loading": "加载端点时出错", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.error.saving": "保存端点时出错。请重试。", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.form.has.errors": "一个或多个字段包含错误", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.heading": "端点", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.production": "生产", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.sandbox": "沙盒", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.saving": "保存中", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.select.endpoint.type": "选择端点类型", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.test.endpoint": "检查端点状态", "Apis.Details.Endpoints.AIEndpoints.AddEditAIEndpoint.update.btn": "更新", "Apis.Details.Endpoints.API.Definition.fetch.error": "获取API定义时出错", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.action": "操作", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.cancel.button": "关闭", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.config.save.button": "保存", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.connect.cancel": "连接取消", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.connect.timeout": "连接超时", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.connection.closed": "连接关闭", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.connection.failed": "连接失败", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.connection.timed.out": "连接超时", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.discard.message": "丢弃消息", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.duration.ms": "持续时间（毫秒）", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.endpoint.timeout.state": "端点超时状态", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.error.code": "错误代码", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.execute.fault.sequence": "执行错误序列", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.factor": "因子", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.format.select": "格式", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.initial.duration.ms": "初始持续时间（毫秒）", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.leave.as.is": "保持原样", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.max.duration.ms": "最大持续时间（毫秒）", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.message.content": "消息内容", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.optimize.select": "优化", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.plain.old.xml": "传统XML（POX）", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.receiver.io.error.receiving": "接收方IO接收错误", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.receiver.io.error.sending": "检索方IO发送错误", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.representational.state.transfer": "表述性状态传递（REST）", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.response.processing.failure": "响应处理失败", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.retries.before.suspension": "暂停前重试次数", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.retry.delay.ms": "重试延迟（毫秒）", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.send.abort": "发送中止", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.sender.io.error.receiving": "发送方IO接收错误", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.sender.io.error.sending": "发送方IO发送错误", "Apis.Details.Endpoints.AdvancedConfig.AdvanceEndpointConfig.tpp.protocol.violation": "TTP协议违规", "Apis.Details.Endpoints.CustomBackend.uploadCustomBackend": "上传序列后端", "Apis.Details.Endpoints.EndpointOverview.advance.endpoint.configuration": "高级配置", "Apis.Details.Endpoints.EndpointOverview.awslambda.endpoint.accessKey": "访问密钥", "Apis.Details.Endpoints.EndpointOverview.awslambda.endpoint.accessMethod": "访问方法", "Apis.Details.Endpoints.EndpointOverview.awslambda.endpoint.accessMethod.roleSupplied": "使用IAM角色提供的临时AWS凭证", "Apis.Details.Endpoints.EndpointOverview.awslambda.endpoint.accessMethod.stored": "使用存储的AWS凭证", "Apis.Details.Endpoints.EndpointOverview.awslambda.endpoint.enableSTSAssumeRole": "启用STS AssumeRole", "Apis.Details.Endpoints.EndpointOverview.awslambda.endpoint.linkToResources": "前往资源映射ARN", "Apis.Details.Endpoints.EndpointOverview.awslambda.endpoint.region": "区域", "Apis.Details.Endpoints.EndpointOverview.awslambda.endpoint.roleArn": "角色ARN", "Apis.Details.Endpoints.EndpointOverview.awslambda.endpoint.roleRegion": "区域", "Apis.Details.Endpoints.EndpointOverview.awslambda.endpoint.roleSessionName": "角色会话名称", "Apis.Details.Endpoints.EndpointOverview.awslambda.endpoint.secretKey": "密钥", "Apis.Details.Endpoints.EndpointOverview.awslambda.endpoint.tooltip": "您可以使用IAM角色管理在AWS实例上运行的应用程序的临时凭证", "Apis.Details.Endpoints.EndpointOverview.change.type.cancel": "取消", "Apis.Details.Endpoints.EndpointOverview.endpoint.security.configuration": "端点安全配置", "Apis.Details.Endpoints.EndpointOverview.endpoint.type\n                                        .change.confirmation.message": "您当前的端点配置将丢失。", "Apis.Details.Endpoints.EndpointOverview.endpoint.type.change.confirmation": "更改端点类型", "Apis.Details.Endpoints.EndpointOverview.general.config.header": "通用端点配置", "Apis.Details.Endpoints.EndpointOverview.lb.failover.endpoints.header": "负载均衡和故障转移配置", "Apis.Details.Endpoints.EndpointOverview.loadbalance.config.cancel.button": "关闭", "Apis.Details.Endpoints.EndpointOverview.loadbalance.config.save.button": "保存", "Apis.Details.Endpoints.EndpointOverview.production.endpoint.production.header": "生产端点", "Apis.Details.Endpoints.EndpointOverview.production.endpoint.production.label": "生产端点", "Apis.Details.Endpoints.EndpointOverview.prototype.endpoint.prototype.header": "原型端点", "Apis.Details.Endpoints.EndpointOverview.prototype.endpoint.prototype.label": "原型端点", "Apis.Details.Endpoints.EndpointOverview.sandbox.endpoint": "沙盒端点", "Apis.Details.Endpoints.EndpointOverview.sandbox.endpoint.sandbox.header": "沙盒端点", "Apis.Details.Endpoints.EndpointOverview.upload.mediation.message": "请上传中介序列文件至消息中介策略以设置端点。", "Apis.Details.Endpoints.Endpoints.cancel": "取消", "Apis.Details.Endpoints.Endpoints.delete.sequence.backend.error": "删除沙盒序列后端错误", "Apis.Details.Endpoints.Endpoints.endpoints.header": "端点", "Apis.Details.Endpoints.Endpoints.missing.accessKey.secretKey.error": "访问密钥、密钥和区域不能为空", "Apis.Details.Endpoints.Endpoints.missing.endpoint.ai.error": "必须添加生产与沙盒端点安全配置", "Apis.Details.Endpoints.Endpoints.missing.endpoint.error": "必须添加生产或沙盒端点之一。", "Apis.Details.Endpoints.Endpoints.missing.prod.endpoint.loadbalance": "默认生产端点不能为空", "Apis.Details.Endpoints.Endpoints.missing.prototype.url": "原型端点URL不能为空", "Apis.Details.Endpoints.Endpoints.missing.sandbox.endpoint.loadbalance": "默认沙盒端点不能为空", "Apis.Details.Endpoints.Endpoints.missing.security.apikey.error": "端点安全API密钥不能为空", "Apis.Details.Endpoints.Endpoints.missing.security.oauth.client.error": "端点安全令牌URL/API密钥/API密钥不能为空", "Apis.Details.Endpoints.Endpoints.missing.security.oauth.password.error": "端点安全令牌URL/API密钥/API密钥/用户名/密码不能为空", "Apis.Details.Endpoints.Endpoints.missing.security.username.error": "端点安全用户名/密码不能为空", "Apis.Details.Endpoints.Endpoints.missing.stsAssumeRole.config": "角色ARN、角色会话名称和区域不能为空", "Apis.Details.Endpoints.Endpoints.upload.sequence.backend.error": "上传沙盒序列后端错误", "Apis.Details.Endpoints.GeneralConfiguration.Certificates.NoCertificatesUploaded": "您尚未上传任何证书", "Apis.Details.Endpoints.GeneralConfiguration.Certificates.certificate.add.success": "证书添加成功", "Apis.Details.Endpoints.GeneralConfiguration.Certificates.certificate.alias.exist": "添加证书失败。证书别名已存在。", "Apis.Details.Endpoints.GeneralConfiguration.Certificates.certificate.delete.error": "删除证书错误", "Apis.Details.Endpoints.GeneralConfiguration.Certificates.certificate.delete.success": "证书删除成功", "Apis.Details.Endpoints.GeneralConfiguration.Certificates.certificate.details.of": "详情", "Apis.Details.Endpoints.GeneralConfiguration.Certificates.certificate.error": "添加证书时出错。", "Apis.Details.Endpoints.GeneralConfiguration.Certificates.certificates": "证书", "Apis.Details.Endpoints.GeneralConfiguration.Certificates.certificates.AddCertificate": "添加证书", "Apis.Details.Endpoints.GeneralConfiguration.Certificates.confirm.certificate.delete": "确认删除", "Apis.Details.Endpoints.GeneralConfiguration.Certificates.delete.cancel.button": "取消", "Apis.Details.Endpoints.GeneralConfiguration.Certificates.delete.ok.button": "确定", "Apis.Details.Endpoints.GeneralConfiguration.Certificates.deleteCertificate": "谨慎删除！", "Apis.Details.Endpoints.GeneralConfiguration.Certificates.details.close.button": "关闭", "Apis.Details.Endpoints.GeneralConfiguration.Certificates.production.certificates": "生产", "Apis.Details.Endpoints.GeneralConfiguration.Certificates.sandbox.certificates": "沙盒", "Apis.Details.Endpoints.GeneralConfiguration.Certificates.status": "状态", "Apis.Details.Endpoints.GeneralConfiguration.Certificates.subject": "主题", "Apis.Details.Endpoints.GeneralConfiguration.CustomBackend.sandbox.backend": "沙盒", "Apis.Details.Endpoints.GeneralConfiguration.EditableParameterRow.Parameter.Name": "参数名称", "Apis.Details.Endpoints.GeneralConfiguration.EditableParameterRow.Parameter.Value": "参数值", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.add.new.parameter": "添加新参数", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.add.new.parameter.\n                                    info": "您可以在下方添加端点所需的任何额外负载参数", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.basic": "基本认证", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.clientId.input": "客户端ID", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.clientId.message": "输入客户端ID", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.clientSecret.input": "客户端密钥", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.clientSecret.message": "输入客户端密钥", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.connection.\n                                                request.timeout.duration": "连接请求超时时间（毫秒）", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.connection.\n                                                timeout.duration": "连接超时时间（毫秒）", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.digest.auth": "摘要认证", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.grant.type.input": "授权类型", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.input.parameter.name": "参数名称", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.input.parameter.value": "参数值", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.label.parameter.name": "参数名称", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.label.parameter.value": "参数值", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.no.clientId.error": "客户端ID不能为空", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.no.clientSecret.error": "客户端密钥不能为空", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.no.password.error": "密码不能为空", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.no.tokenUrl.error": "令牌URL不能为空或格式错误", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.no.username.error": "用户名不能为空", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.none": "无", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.oauth": "OAuth 2.0", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.oauth.grant.type.client": "客户端凭证", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.oauth.grant.type.password": "资源所有者密码", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.password.input": "密码", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.password.message": "输入密码", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.proxyHost.input": "代理主机名", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.proxyPassword.input": "代理密码", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.proxyPort.input": "代理端口", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.proxyProtocol.input": "代理协议", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.proxyUsername.input": "代理用户名", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.socket.\n                                                timeout.duration": "套接字超时时间（毫秒）", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.token.endpoint.\n                                            connection.configurations": "令牌端点连接配置", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.token.url.input": "令牌URL", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.tokenUrl.message": "输入令牌URL", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.user.name.input": "用户名", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurity.username.message": "输入用户名", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurityConfig.cancel.button": "关闭", "Apis.Details.Endpoints.GeneralConfiguration.EndpointSecurityConfig.config.save.button": "提交", "Apis.Details.Endpoints.GeneralConfiguration.UploadCertificate.alias": "别名", "Apis.Details.Endpoints.GeneralConfiguration.UploadCertificate.alias.default.message": "证书别名", "Apis.Details.Endpoints.GeneralConfiguration.UploadCertificate.alias.exist.error": "别名已存在", "Apis.Details.Endpoints.GeneralConfiguration.UploadCertificate.cancel.button": "关闭", "Apis.Details.Endpoints.GeneralConfiguration.UploadCertificate.click.or.drop.to.upload.file": "点击或拖拽证书文件上传。", "Apis.Details.Endpoints.GeneralConfiguration.UploadCertificate.config.save.button": "保存", "Apis.Details.Endpoints.GeneralConfiguration.UploadCertificate.endpoint": "端点", "Apis.Details.Endpoints.GeneralConfiguration.UploadCertificate.endpoint.empty.error": "添加证书前请先保存端点", "Apis.Details.Endpoints.GeneralConfiguration.UploadCertificate.endpoint.error": "端点不能为空", "Apis.Details.Endpoints.GeneralConfiguration.UploadCertificate.endpoint.helpertext": "证书端点", "Apis.Details.Endpoints.GeneralConfiguration.UploadCertificate.invalid.file": "无效文件类型", "Apis.Details.Endpoints.GeneralConfiguration.UploadCertificate.keyType": "选择证书密钥类型", "Apis.Details.Endpoints.GeneralConfiguration.UploadCertificate.uploadCertificate": "上传证书", "Apis.Details.Endpoints.GeneralConfiguration.UploadCertificate.uploadCertificate.alias.placeholder": "我的别名", "Apis.Details.Endpoints.GeneralConfiguration.UploadCertificate.uploadCertificate.throttle.policy": "选择证书的节流策略", "Apis.Details.Endpoints.GeneralConfiguration.certificates.sub.heading": "证书", "Apis.Details.Endpoints.GeneralConfiguration.no.production.certifcates": "您尚未上传任何生产类型证书", "Apis.Details.Endpoints.GeneralConfiguration.no.sandbox.certifcates": "您尚未上传任何沙盒类型证书", "Apis.Details.Endpoints.GeneralConfiguration.not.allowed": "*由于权限不足，您无权查看证书", "Apis.Details.Endpoints.GenericEndpoint.check.endpoint": "检查端点状态", "Apis.Details.Endpoints.GenericEndpoint.config.endpoint": "端点配置", "Apis.Details.Endpoints.GenericEndpoint.config.service.placeholder": "从服务列表中选择服务", "Apis.Details.Endpoints.GenericEndpoint.no.ep.error": "端点URL不能为空", "Apis.Details.Endpoints.GenericEndpoint.security.endpoint": "端点安全", "Apis.Details.Endpoints.GenericEndpoint.service.url.input": "服务URL", "Apis.Details.Endpoints.GenericEndpoint.service.url.input.placeholder": "输入端点URL并按+按钮", "Apis.Details.Endpoints.LoadBalanceConfig.algorithm": "算法", "Apis.Details.Endpoints.LoadBalanceConfig.algorithm.helper.text": "请选择负载均衡算法。", "Apis.Details.Endpoints.LoadBalanceConfig.class.name.for.algorithm": "算法类名", "Apis.Details.Endpoints.LoadBalanceConfig.class.name.for.algorithm.helper.text": "输入负载均衡算法的类名", "Apis.Details.Endpoints.LoadBalanceConfig.failover": "启用故障转移", "Apis.Details.Endpoints.LoadBalanceConfig.session.management": "会话管理", "Apis.Details.Endpoints.LoadBalanceConfig.session.management.helper.text": "请选择会话管理机制。", "Apis.Details.Endpoints.LoadBalanceConfig.session.timeout": "会话超时（毫秒）", "Apis.Details.Endpoints.LoadbalanceFailoverConfig.endpoint.placeholder": "端点", "Apis.Details.Endpoints.LoadbalanceFailoverConfig.endpoint.type": "端点类型", "Apis.Details.Endpoints.LoadbalanceFailoverConfig.failover.heading": "故障转移端点", "Apis.Details.Endpoints.LoadbalanceFailoverConfig.load.balance.configuration.title": "负载均衡配置", "Apis.Details.Endpoints.LoadbalanceFailoverConfig.loadbalance.heading": "负载均衡端点", "Apis.Details.Endpoints.LoadbalanceFailoverConfig.no.endpoints.message": "添加生产/沙盒端点以配置。", "Apis.Details.Endpoints.LoadbalanceFailoverConfig.none.heading": "未配置", "Apis.Details.Endpoints.LoadbalanceFailoverConfig.production.failover.endpoint": "生产故障转移端点", "Apis.Details.Endpoints.LoadbalanceFailoverConfig.production.loadbalance.endpoint": "生产负载均衡端点", "Apis.Details.Endpoints.LoadbalanceFailoverConfig.sandbox.failover.endpoint": "沙盒故障转移端点", "Apis.Details.Endpoints.LoadbalanceFailoverConfig.sandbox.loadbalance.endpoint": "沙盒负载均衡端点", "Apis.Details.Endpoints.LoadbalanceFailoverConfig.types.failover": "故障转移", "Apis.Details.Endpoints.LoadbalanceFailoverConfig.types.load.balanced": "负载均衡", "Apis.Details.Endpoints.LoadbalanceFailoverConfig.types.none": "无", "Apis.Details.Endpoints.NewEndpointCreate.add.endpoints.header": "选择要添加的端点类型", "Apis.Details.Endpoints.NewEndpointCreate.create.awslambda.endpoint": "AWS Lambda端点", "Apis.Details.Endpoints.NewEndpointCreate.create.awslambda.endpoint.description": "如需通过API网关调用AWS Lambda函数。", "Apis.Details.Endpoints.NewEndpointCreate.create.button": "添加", "Apis.Details.Endpoints.NewEndpointCreate.create.dynamic.endpoint": "动态端点", "Apis.Details.Endpoints.NewEndpointCreate.create.dynamic.endpoint.description": "如需将请求发送至TO头中指定的URI。", "Apis.Details.Endpoints.NewEndpointCreate.create.http.endpoint": "HTTP/REST端点", "Apis.Details.Endpoints.NewEndpointCreate.create.http.endpoint.description": "基于URI模板的REST API端点。", "Apis.Details.Endpoints.NewEndpointCreate.create.prototype.endpoint": "模拟实现", "Apis.Details.Endpoints.NewEndpointCreate.create.prototype.endpoint.description": "使用内置JavaScript引擎原型化API。内置JavaScript引擎支持原型化SOAP API。", "Apis.Details.Endpoints.NewEndpointCreate.create.sequencebackend.endpoint": "序列后端", "Apis.Details.Endpoints.NewEndpointCreate.create.sequencebackend.endpoint.description": "如需为API提供序列作为后端。", "Apis.Details.Endpoints.NewEndpointCreate.create.service.endpoint": "服务端点", "Apis.Details.Endpoints.NewEndpointCreate.create.service.endpoint.description": "基于服务目录中服务的REST API端点。", "Apis.Details.Endpoints.NewEndpointCreate.create.soap.endpoint": "HTTP/SOAP端点", "Apis.Details.Endpoints.NewEndpointCreate.create.soap.endpoint.description": "Web服务的直接URI。", "Apis.Details.Endpoints.Prototype.MockImplEndpoints.script": "脚本", "Apis.Details.Endpoints.Prototype.MockImplEndpoints.script.reset": "重置", "Apis.Details.Endpoints.Prototype.MockedOAS.Response.Example": "示例值", "Apis.Details.Endpoints.Prototype.MockedOAS.Response.Example.NotProvided": "API定义中未提供响应示例", "Apis.Details.Endpoints.Prototype.MockedOAS.Response.NotProvided": "API定义中未提供响应", "Apis.Details.Endpoints.Prototype.MockedOAS.subTitle": "从OAS生成的模拟示例", "Apis.Details.Endpoints.Prototype.MockedOAS.title": "响应", "Apis.Details.Endpoints.SequenceBackend.AddCertificat": "添加序列后端", "Apis.Details.Endpoints.SequenceBackend.AddSequence": "添加序列后端", "Apis.Details.Endpoints.SequenceBackend.delete.cancel.button": "取消", "Apis.Details.Endpoints.SequenceBackend.delete.ok.button": "确定", "Apis.Details.Endpoints.SequenceBackend.deleteCustomBackend": "谨慎删除！", "Apis.Details.Endpoints.UploadCustomBackend.cancel.button": "关闭", "Apis.Details.Endpoints.UploadCustomBackend.click.or.drop.to.upload.file": "点击或拖拽序列后端文件上传。", "Apis.Details.Endpoints.UploadCustomBackend.config.save.button": "保存", "Apis.Details.Endpoints.UploadCustomBackend.invalid.file": "无效的文件类型", "Apis.Details.Endpoints.add.new.endpoint": "添加新端点", "Apis.Details.Environments.Environments\n                                                                    .select.vhost": "选择访问URL", "Apis.Details.Environments.Environments.APIGateways": "API网关", "Apis.Details.Environments.Environments.Create.Revision.Deploy": "创建新版本并部署", "Apis.Details.Environments.Environments.Deployments": "版本", "Apis.Details.Environments.Environments.RetiredApi.ToolTip": "无法为已停用的API部署新版本", "Apis.Details.Environments.Environments.advertise.only.warning": "此API标记为第三方API。请求不通过网关代理，因此无需部署。", "Apis.Details.Environments.Environments.api.gateways.heading": "API网关", "Apis.Details.Environments.Environments.api.gateways.name": "名称", "Apis.Details.Environments.Environments.api.revision.deploy.success": "版本部署成功", "Apis.Details.Environments.Environments.cancel.btn": "取消", "Apis.Details.Environments.Environments.create.cancel": "取消", "Apis.Details.Environments.Environments.create.create": "创建", "Apis.Details.Environments.Environments.deploy.button": "部署", "Apis.Details.Environments.Environments.deploy.cancel": "取消", "Apis.Details.Environments.Environments.deploy.deploy": "部署", "Apis.Details.Environments.Environments.deploy.new.revision": "部署新版本", "Apis.Details.Environments.Environments.deploy.new.revision.heading": "部署新版本", "Apis.Details.Environments.Environments.deployments.heading": "部署", "Apis.Details.Environments.Environments.deployments.sub.heading": "在网关环境中创建版本并部署", "Apis.Details.Environments.Environments.display.devportal": "在开发者门户中显示网关访问URL。", "Apis.Details.Environments.Environments.external.gateways.heading": "API网关", "Apis.Details.Environments.Environments.gateway\n                                                    .deployment.current.revision": "当前版本", "Apis.Details.Environments.Environments.gateway.accessUrl": "网关访问URL", "Apis.Details.Environments.Environments.gateway.action": "操作", "Apis.Details.Environments.Environments.gateway.deployment.next.revision": "下一个版本", "Apis.Details.Environments.Environments.no.env.heading": "未为所选网关类型配置网关环境", "Apis.Details.Environments.Environments.pending.chip": "待处理", "Apis.Details.Environments.Environments.revision\n                                                            .description.deploy": "为版本添加描述", "Apis.Details.Environments.Environments.revision.create.error": "创建版本时出错", "Apis.Details.Environments.Environments.revision.create.error.governance": "版本创建失败。发现治理策略违规", "Apis.Details.Environments.Environments.revision.create.error.governance.download": "下载违规", "Apis.Details.Environments.Environments.revision.create.heading": "创建新版本（从当前API）", "Apis.Details.Environments.Environments.revision.create.success": "版本创建成功", "Apis.Details.Environments.Environments.revision.delete": "删除", "Apis.Details.Environments.Environments.revision.delete.cancel": "取消", "Apis.Details.Environments.Environments.revision.delete.confirm.message": "确定要删除{revision}吗？", "Apis.Details.Environments.Environments.revision.delete.confirm.ok": "是", "Apis.Details.Environments.Environments.revision.delete.confirm.title": "确认删除", "Apis.Details.Environments.Environments.revision.delete.error": "删除版本时出错", "Apis.Details.Environments.Environments.revision.delete.success": "版本删除成功", "Apis.Details.Environments.Environments.revision.deploy.error": "部署版本时出错", "Apis.Details.Environments.Environments.revision.deploy.error.governance": "版本部署失败。发现治理策略违规", "Apis.Details.Environments.Environments.revision.deploy.error.governance.download": "下载违规", "Apis.Details.Environments.Environments.revision.deploy.request.cancel": "版本部署请求已成功取消", "Apis.Details.Environments.Environments.revision.deploy.request.cancel.error": "取消版本部署请求时出错", "Apis.Details.Environments.Environments.revision.deploy.request.success": "部署版本请求已成功发送", "Apis.Details.Environments.Environments.revision.deploy.success": "版本部署成功", "Apis.Details.Environments.Environments.revision.description.add": "添加描述", "Apis.Details.Environments.Environments.revision.description.add.optional.text": "（可选）", "Apis.Details.Environments.Environments.revision.description.create": "新版本的简要描述", "Apis.Details.Environments.Environments.revision.description.deploy": "新版本的简要描述", "Apis.Details.Environments.Environments.revision.description.deploy.helper": "为版本添加描述", "Apis.Details.Environments.Environments.revision.description.label": "描述", "Apis.Details.Environments.Environments.revision.restore": "恢复", "Apis.Details.Environments.Environments.revision.restore.cancel": "取消", "Apis.Details.Environments.Environments.revision.restore.confirm.message": "确定要恢复{revision}（到当前API）吗？", "Apis.Details.Environments.Environments.revision.restore.confirm.ok": "是", "Apis.Details.Environments.Environments.revision.restore.confirm.title": "确认恢复", "Apis.Details.Environments.Environments.revision.restore.error": "恢复版本时出错", "Apis.Details.Environments.Environments.revision.restore.success": "版本恢复成功", "Apis.Details.Environments.Environments.revision.undeploy.error": "取消部署版本时出错", "Apis.Details.Environments.Environments.revision.undeploy.success": "版本取消部署成功", "Apis.Details.Environments.Environments.select.rev.delete": "要删除的版本", "Apis.Details.Environments.Environments.select.rev.helper": "版本数量已达到最大值{count}，请选择一个版本删除", "Apis.Details.Environments.Environments.select.rev.helper1": "版本数量已达到最大值{count}，请取消部署并删除一个版本", "Apis.Details.Environments.Environments.select.rev.warning": "版本数量已达到最大值{count}，请删除一个版本", "Apis.Details.Environments.Environments.select.table": "选择版本", "Apis.Details.Environments.Environments.status.not.deployed": "未部署版本", "Apis.Details.Environments.Environments.undeploy.btn": "取消部署", "Apis.Details.Environments.Environments.visibility.in.devportal": "网关URL可见性", "Apis.Details.Environments.Environments.visibility.permission": "开发者门户中的网关环境可见性。", "Apis.Details.Environments.GovernanceViolations.column.message": "消息", "Apis.Details.Environments.GovernanceViolations.column.path": "路径", "Apis.Details.Environments.GovernanceViolations.column.rule": "规则", "Apis.Details.Environments.GovernanceViolations.column.severity": "严重性", "Apis.Details.Environments.GovernanceViolations.column.type": "类型", "Apis.Details.Environments.GovernanceViolations.download.button": "下载为JSON", "Apis.Details.Environments.GovernanceViolations.download.tooltip": "将违规下载为JSON", "Apis.Details.Environments.GovernanceViolations.empty.default": "未找到所选API的治理规则违规。", "Apis.Details.Environments.GovernanceViolations.empty.error": "未找到错误级别的治理规则违规。", "Apis.Details.Environments.GovernanceViolations.empty.info": "未找到信息级别的治理规则违规。", "Apis.Details.Environments.GovernanceViolations.empty.warn": "未找到警告级别的治理规则违规。", "Apis.Details.Environments.GovernanceViolations.title": "治理规则违规", "Apis.Details.Environments.deploy.api.external.gateways.text": "API网关", "Apis.Details.Environments.deploy.api.gateways.text": "API网关", "Apis.Details.Environments.deploy.env.text": "将API部署到网关环境", "Apis.Details.Environments.deploy.text": "部署API", "Apis.Details.Environments.deploy.vhost": "虚拟主机", "Apis.Details.External.Gateways": "API网关", "Apis.Details.ExternalStores.ExternalStores.cancel": "取消", "Apis.Details.ExternalStores.ExternalStores.endpoint": "端点", "Apis.Details.ExternalStores.ExternalStores.error.getting.published.external.stores": "获取已发布的外部开发者门户时出错！！{reason}", "Apis.Details.ExternalStores.ExternalStores.error.while.updating.external.stores": "更新外部开发者门户时出错！！{reason}", "Apis.Details.ExternalStores.ExternalStores.external-stores": "外部开发者门户", "Apis.Details.ExternalStores.ExternalStores.external.stores.not.found.body": "当前租户未配置外部开发者门户", "Apis.Details.ExternalStores.ExternalStores.external.stores.not.found.title": "未找到API的外部开发者门户：{apiUUID}", "Apis.Details.ExternalStores.ExternalStores.name": "名称", "Apis.Details.ExternalStores.ExternalStores.save": "保存", "Apis.Details.ExternalStores.ExternalStores.successfully.published.to.external.stores": "已成功发布到外部开发者门户：{successfulStores}", "Apis.Details.ExternalStores.ExternalStores.type": "类型", "Apis.Details.ExternalStores.ExternalStores.update.not.allowed": "* 由于权限不足，您无权将API发布到外部开发者门户", "Apis.Details.GoTo.Components.GoToSuggestions.no.result": "此查询无结果", "Apis.Details.GoTo.GoTo.btn": "前往", "Apis.Details.GoTo.button.placeholder": "页面搜索", "Apis.Details.Index.get.revisions.error": "获取版本时出错！", "Apis.Details.LifeCycle.CheckboxLabels.business.plans.selected": "已选择商业计划", "Apis.Details.LifeCycle.CheckboxLabels.cert.provided": "已提供证书", "Apis.Details.LifeCycle.CheckboxLabels.endpoints.provided": "已提供端点", "Apis.Details.LifeCycle.CheckboxLabels.mandatory.properties.provided": "已提供必需属性", "Apis.Details.LifeCycle.LifeCycle.change.not.allowed": "* 由于权限不足，您无权更改API的生命周期状态", "Apis.Details.LifeCycle.LifeCycle.history": "历史", "Apis.Details.LifeCycle.LifeCycle.lifecycle": "生命周期", "Apis.Details.LifeCycle.LifeCycleHistory.action": "操作", "Apis.Details.LifeCycle.LifeCycleHistory.lifecycle.state.history": "生命周期状态从{previous}更改为{post}", "Apis.Details.LifeCycle.LifeCycleHistory.time": "时间", "Apis.Details.LifeCycle.LifeCycleHistory.user": "用户", "Apis.Details.LifeCycle.LifeCycleUpdate.LifecyclePending.current.state": "当前状态为", "Apis.Details.LifeCycle.LifeCycleUpdate.LifecyclePending.delete.task": "删除任务", "Apis.Details.LifeCycle.LifeCycleUpdate.LifecyclePending.dialog,delete": "删除", "Apis.Details.LifeCycle.LifeCycleUpdate.LifecyclePending.dialog.cancel": "取消", "Apis.Details.LifeCycle.LifeCycleUpdate.LifecyclePending.dialog.text.description": "生命周期任务将被删除", "Apis.Details.LifeCycle.LifeCycleUpdate.LifecyclePending.dialog.title": "删除任务", "Apis.Details.LifeCycle.LifeCycleUpdate.LifecyclePending.error": "删除任务时出错", "Apis.Details.LifeCycle.LifeCycleUpdate.LifecyclePending.pending": "待处理的生命周期状态更改。", "Apis.Details.LifeCycle.LifeCycleUpdate.LifecyclePending.success": "生命周期任务删除成功", "Apis.Details.LifeCycle.LifeCycleUpdate.State.Block": "阻止", "Apis.Details.LifeCycle.LifeCycleUpdate.State.Demote.to.Created": "降级为创建", "Apis.Details.LifeCycle.LifeCycleUpdate.State.Deprecate": "弃用", "Apis.Details.LifeCycle.LifeCycleUpdate.State.Pre-Release": "预发布", "Apis.Details.LifeCycle.LifeCycleUpdate.State.Publish": "发布", "Apis.Details.LifeCycle.LifeCycleUpdate.State.Re.Publish": "重新发布", "Apis.Details.LifeCycle.LifeCycleUpdate.State.Retire": "停用", "Apis.Details.LifeCycle.LifeCycleUpdate.approve.approveStatus": "生命周期状态更改操作已成功批准", "Apis.Details.LifeCycle.LifeCycleUpdate.error": "更新生命周期时出错", "Apis.Details.LifeCycle.LifeCycleUpdate.error.certs": "检索证书时出错", "Apis.Details.LifeCycle.LifeCycleUpdate.error.governance": "生命周期更新失败。发现治理策略违规。", "Apis.Details.LifeCycle.LifeCycleUpdate.error.governance.download": "下载违规", "Apis.Details.LifeCycle.LifeCycleUpdate.reject.rejectStatus": "由于验证失败，生命周期状态更改操作被拒绝", "Apis.Details.LifeCycle.LifeCycleUpdate.success.createStatus": "生命周期状态更改请求已发送", "Apis.Details.LifeCycle.LifeCycleUpdate.success.otherStatus": "生命周期状态更新成功", "Apis.Details.LifeCycle.Policies.business.plans": "商业计划", "Apis.Details.LifeCycle.Policies.select.plan.api": "为API选择一个计划并启用API级限流。", "Apis.Details.LifeCycle.Policies.select.plan.api.product": "为API产品选择一个计划。", "Apis.Details.LifeCycle.Policies.update.error": "更新API时出错", "Apis.Details.LifeCycle.Policies.update.error.governance": "违反了一个或多个治理策略。请尝试在生命周期页面中使用发布选项以获取更多详细信息。", "Apis.Details.LifeCycle.Policies.update.error.governance.link": "前往生命周期页面", "Apis.Details.LifeCycle.Policies.update.success": "生命周期状态更新成功", "Apis.Details.LifeCycle.PublishWithoutDeploy.advertise": "发布", "Apis.Details.LifeCycle.PublishWithoutDeploy.see.less": "收起", "Apis.Details.LifeCycle.PublishWithoutDeploy.see.more": "展开", "Apis.Details.LifeCycle.State.Status.BLOCKED": "已阻止", "Apis.Details.LifeCycle.State.Status.CREATED": "已创建", "Apis.Details.LifeCycle.State.Status.DEPRECATED": "已弃用", "Apis.Details.LifeCycle.State.Status.PRE-RELEASED": "预发布", "Apis.Details.LifeCycle.State.Status.PRE_RELEASED": "预发布", "Apis.Details.LifeCycle.State.Status.PUBLISHED": "已发布", "Apis.Details.LifeCycle.State.Status.RETIRED": "已停用", "Apis.Details.LifeCycle.components.api.update.error": "更新API时出错", "Apis.Details.LifeCycle.components.api.update.success": "API更新成功", "Apis.Details.LifeCycle.components.confirm.publish.apiProducts.message": "在发布API之前部署API产品", "Apis.Details.LifeCycle.components.confirm.publish.apiProducts.title": "在发布之前部署API产品", "Apis.Details.LifeCycle.components.confirm.publish.message": "除非部署，否则API将不可用。", "Apis.Details.LifeCycle.components.confirm.publish.message.advertise.only": "如果要作为第三方API发布，请提供外部端点并按“发布”。", "Apis.Details.LifeCycle.components.confirm.publish.title": "发布API而不部署", "Apis.Details.LifeCycle.components.externalEndpoint": "外部端点", "Apis.Details.LifeCycle.externalEndpoint.error": "无效的端点URL", "Apis.Details.LifeCycle.publish.content.info.deployments": "部署", "Apis.Details.Monetization.BusinessPlans.commercial.no.policies.banner": "没有可货币化的商业策略", "Apis.Details.Monetization.BusinessPlans.commercial.policies": "商业策略", "Apis.Details.Monetization.BusinessPlans.commercial.policies.banner.save": "点击保存以货币化所有未货币化的策略", "Apis.Details.Monetization.BusinessPlans.monetized": "已货币化", "Apis.Details.Monetization.BusinessPlans.not.monetized": "未货币化", "Apis.Details.Monetization.Index.cancel": "取消", "Apis.Details.Monetization.Index.monetization": "货币化", "Apis.Details.Monetization.Index.monetization.configured.successfully": "货币化已成功启用", "Apis.Details.Monetization.Index.monetization.disabled.successfully": "货币化已成功禁用", "Apis.Details.Monetization.Index.monetization.enable.label": "启用货币化", "Apis.Details.Monetization.Index.monetization.properties": "货币化属性", "Apis.Details.Monetization.Index.save": "保存", "Apis.Details.Monetization.Index.something.went.wrong.while.configuring.monetization": "配置货币化时出错", "Apis.Details.Monetization.Index.there.are.no .monetization.properties.configured": "未配置货币化属性", "Apis.Details.Monetization.Index.update.not.allowed": "* 由于权限不足，您无权更新API货币化", "Apis.Details.NewOverview.BusinessInformation.business.owner": "业务所有者", "Apis.Details.NewOverview.BusinessInformation.business.owner.not.set": "-", "Apis.Details.NewOverview.BusinessInformation.technical.owner": "技术所有者", "Apis.Details.NewOverview.BusinessInformation.technical.owner.not.set": "-", "Apis.Details.NewOverview.Endpoints.endpoint.security": "端点安全", "Apis.Details.NewOverview.Endpoints.endpoints": "端点", "Apis.Details.NewOverview.Endpoints.external.production.endpoint": "外部生产端点", "Apis.Details.NewOverview.Endpoints.external.sandbox.endpoint": "外部沙盒端点", "Apis.Details.NewOverview.Endpoints.not.set": "-", "Apis.Details.NewOverview.Endpoints.production.endpoint": "生产端点", "Apis.Details.NewOverview.Endpoints.production.endpoint.security": "生产端点安全", "Apis.Details.NewOverview.Endpoints.production.security.not.set": "-", "Apis.Details.NewOverview.Endpoints.prototype.endpoint": "原型端点", "Apis.Details.NewOverview.Endpoints.sandbox.endpoint": "沙盒端点", "Apis.Details.NewOverview.Endpoints.sandbox.endpoint.security": "沙盒端点安全", "Apis.Details.NewOverview.Endpoints.sandbox.not.set": "-", "Apis.Details.NewOverview.Endpoints.sandbox.security.not.set": "-", "Apis.Details.NewOverview.Endpoints.security.not.set": "-", "Apis.Details.NewOverview.MetaData.access.control": "访问控制", "Apis.Details.NewOverview.MetaData.access.control.all.tooltip": "全部：所有发布者和创建者均可查看和修改该API。", "Apis.Details.NewOverview.MetaData.access.control.tooltip": "按角色限制：仅限具有您指定角色的特定发布者和创建者查看和修改该API。", "Apis.Details.NewOverview.MetaData.config": "配置", "Apis.Details.NewOverview.MetaData.context:": "上下文", "Apis.Details.NewOverview.MetaData.created.time": "创建时间", "Apis.Details.NewOverview.MetaData.createdTime.not.set": "-", "Apis.Details.NewOverview.MetaData.description": "描述", "Apis.Details.NewOverview.MetaData.description.not.set": "-", "Apis.Details.NewOverview.MetaData.gateway.type": "网关类型", "Apis.Details.NewOverview.MetaData.last.updated.time": "最后更新时间", "Apis.Details.NewOverview.MetaData.lastUpdatedTime.not.set": "-", "Apis.Details.NewOverview.MetaData.metadata": "元数据", "Apis.Details.NewOverview.MetaData.originalDevPortalUrl": "原始开发者门户URL", "Apis.Details.NewOverview.MetaData.originalDevPortalUrl.not.set": "-", "Apis.Details.NewOverview.MetaData.provider": "提供者", "Apis.Details.NewOverview.MetaData.securityScheme": "API安全", "Apis.Details.NewOverview.MetaData.securityScheme.not.set": "-", "Apis.Details.NewOverview.MetaData.securityScheme.tooltip": "默认使用OAuth2作为安全方案。", "Apis.Details.NewOverview.MetaData.solace.transports": "可用协议", "Apis.Details.NewOverview.MetaData.subscription.validation.tooltip": "如果订阅验证被禁用，API消费不需要订阅。", "Apis.Details.NewOverview.MetaData.subvalidation": "订阅验证", "Apis.Details.NewOverview.MetaData.subvalidation.disabled": "禁用", "Apis.Details.NewOverview.MetaData.subvalidation.enabled": "启用", "Apis.Details.NewOverview.MetaData.tags": "标签", "Apis.Details.NewOverview.MetaData.tags.not.set": "-", "Apis.Details.NewOverview.MetaData.transport.tooltip": "HTTP比HTTPS安全性低，会使您的API容易受到安全威胁。", "Apis.Details.NewOverview.MetaData.transports": "传输", "Apis.Details.NewOverview.MetaData.transports.not.set": "-", "Apis.Details.NewOverview.MetaData.type.not.set": "-", "Apis.Details.NewOverview.MetaData.type:": "类型", "Apis.Details.NewOverview.MetaData.version": "版本", "Apis.Details.NewOverview.MetaData.visibility.store": "开发者门户可见性", "Apis.Details.NewOverview.MetaData.visibility.store.all.tooltip": "公开：API对所有人可见，可以在多个开发者门户中宣传——中央开发者门户和/或非WSO2开发者门户。", "Apis.Details.NewOverview.MetaData.visibility.store.res.tooltip": "按角色限制：API仅对您在租户开发者门户中指定的特定用户角色可见。", "Apis.Details.NewOverview.MetaData.workflow.status": "工作流状态", "Apis.Details.NewOverview.MetaData.workflowStatus.not.set": "-", "Apis.Details.NewOverview.Operations.ShowMore": "显示更多", "Apis.Details.NewOverview.Operations.operation": "操作", "Apis.Details.NewOverview.Operations.operations": "操作", "Apis.Details.NewOverview.Policies.business.plans": "业务计划", "Apis.Details.NewOverview.Resources.loading": "加载中...", "Apis.Details.NewOverview.Resources.resource.not.found": "资源未找到...", "Apis.Details.NewOverview.Resources.resources": "资源", "Apis.Details.NewOverview.async.api.topics": "主题", "Apis.Details.NewVersion.NewVersion.cancel": "取消", "Apis.Details.NewVersion.NewVersion.create": "创建", "Apis.Details.NewVersion.NewVersion.create.new.version": "创建新版本", "Apis.Details.NewVersion.NewVersion.default": "将此设为默认版本", "Apis.Details.NewVersion.NewVersion.error": "创建新版本时出错！错误：", "Apis.Details.NewVersion.NewVersion.helper.field.is.empty": "此字段不能为空", "Apis.Details.NewVersion.NewVersion.helper.version.exists": "版本为{newVersion}的API已存在。", "Apis.Details.NewVersion.NewVersion.helper.version.is.invalid": "API版本不能包含特殊字符", "Apis.Details.NewVersion.NewVersion.helper.version.is.too.long": "API版本超过最大长度30个字符", "Apis.Details.NewVersion.NewVersion.new.version": "新版本", "Apis.Details.NewVersion.NewVersion.new.version.placeholder": "例如：2.0.0", "Apis.Details.NewVersion.NewVersion.service.version": "服务版本", "Apis.Details.NewVersion.NewVersion.success": "成功创建新版本", "Apis.Details.NewVersion.NewVersion.tooltip": "指示这是否为API的默认版本。如果调用API时未指定版本，API网关会将请求路由到API的默认版本。", "Apis.Details.NewVersion.loading.services.error": "加载服务版本时出错", "Apis.Details.Operations\n                                                    .Operation.operation.no.sharedpi.scope.available": "无共享作用域可用", "Apis.Details.Operations.Operation\n                                                            .throttling.policy": "速率限制", "Apis.Details.Operations.Operation.OperationType": "操作类型", "Apis.Details.Operations.Operation.authType": "启用安全", "Apis.Details.Operations.Operation.cancel": "取消", "Apis.Details.Operations.Operation.operation.no.api.scope.available": "无API作用域可用", "Apis.Details.Operations.Operation.operation.scope.helperText": "选择一个作用域以控制此操作的权限", "Apis.Details.Operations.Operation.operation.scope.label.default": "操作作用域", "Apis.Details.Operations.Operation.operation.scope.label.notAvailable": "无作用域可用", "Apis.Details.Operations.Operation.operation.scope.select.local": "API作用域", "Apis.Details.Operations.Operation.operation.scope.select.shared": "共享作用域", "Apis.Details.Operations.Operation.save": "保存", "Apis.Details.Operations.Operation.scopes": "作用域", "Apis.Details.Operations.Operations.title": "操作", "Apis.Details.Operations.filter.label": "操作", "Apis.Details.Operations.filter.placeholder": "过滤操作", "Apis.Details.Operations.operation.operationName": "操作", "Apis.Details.Overview.CustomizedStepper.Deploy": "部署", "Apis.Details.Overview.CustomizedStepper.Develop": "开发", "Apis.Details.Overview.CustomizedStepper.Endpoint": "端点", "Apis.Details.Overview.CustomizedStepper.Test": "测试", "Apis.Details.Overview.CustomizedStepper.Tier": "业务计划", "Apis.Details.Overview.CustomizedStepper.ToolTip.DeploymentAvailable": "无法部署已退役的API", "Apis.Details.Overview.CustomizedStepper.ToolTip.DeploymentUnavailable": "将此API的修订版本部署到网关", "Apis.Details.Overview.CustomizedStepper.ToolTip.cannot.test": "API处于退役状态时无法使用测试选项", "Apis.Details.Overview.CustomizedStepper.blocked": "已阻止", "Apis.Details.Overview.CustomizedStepper.btn.prototyped": "预发布", "Apis.Details.Overview.CustomizedStepper.btn.publish": "发布", "Apis.Details.Overview.CustomizedStepper.deprecated": "已弃用", "Apis.Details.Overview.CustomizedStepper.pending": "请求待处理", "Apis.Details.Overview.CustomizedStepper.prototyped": "预发布", "Apis.Details.Overview.CustomizedStepper.publish": "已发布", "Apis.Details.Overview.CustomizedStepper.publish.current.api": "(当前API)", "Apis.Details.Overview.CustomizedStepper.retired": "已退役", "Apis.Details.Overview.CustomizedStepper.view.devportal": "在开发者门户中查看", "Apis.Details.Overview.Overview.topic.header": "概览", "Apis.Details.Overview.ProductResources.resources": "资源", "Apis.Details.Policies.AttachedPolicyCard.apiSpecificPolicy.download.error": "下载策略时出错", "Apis.Details.Policies.AttachedPolicyCard.commonPolicy.download.error": "下载策略时出错", "Apis.Details.Policies.AttachedPolicyForm.General.apply.to.all.resources": "应用于所有资源", "Apis.Details.Policies.AttachedPolicyForm.General.cancel": "取消", "Apis.Details.Policies.AttachedPolicyForm.General.description.title": "描述", "Apis.Details.Policies.AttachedPolicyForm.General.description.value.not.provided": "糟糕！看起来此策略没有描述", "Apis.Details.Policies.AttachedPolicyForm.General.description.value.provided": "{description}", "Apis.Details.Policies.AttachedPolicyForm.General.regex.error": "请输入有效输入", "Apis.Details.Policies.AttachedPolicyForm.General.required.error": "必填字段为空", "Apis.Details.Policies.AttachedPolicyForm.General.reset": "重置", "Apis.Details.Policies.AttachedPolicyForm.General.save": "保存", "Apis.Details.Policies.AttachedPolicyForm.General.saving": "保存中", "Apis.Details.Policies.Components.TabPanel.Components.API.Policy.List": "API策略", "Apis.Details.Policies.Components.TabPanel.Components.Common.Policy.List": "通用策略", "Apis.Details.Policies.CreatePolicy.create.new.policy": "创建新策略", "Apis.Details.Policies.CreatePolicy.create.new.policy.link": "想要创建一个对所有API可见的通用策略吗？", "Apis.Details.Policies.Custom.Policies.model.add": "添加模型", "Apis.Details.Policies.CustomPolicies.ModelFailover.accordion.production": "生产环境", "Apis.Details.Policies.CustomPolicies.ModelFailover.accordion.sandbox": "沙盒环境", "Apis.Details.Policies.CustomPolicies.ModelFailover.fallback.models": "备用模型", "Apis.Details.Policies.CustomPolicies.ModelFailover.no.models": "无可用模型。请为LLM提供者配置模型。", "Apis.Details.Policies.CustomPolicies.ModelFailover.no.production.endpoints": "无生产环境端点可用。请先{configureLink}。", "Apis.Details.Policies.CustomPolicies.ModelFailover.no.sandbox.endpoints": "无沙盒环境端点可用。请先{configureLink}。", "Apis.Details.Policies.CustomPolicies.ModelFailover.target.model": "目标模型", "Apis.Details.Policies.CustomPolicies.ModelRoundRobin.accordion.production": "生产环境", "Apis.Details.Policies.CustomPolicies.ModelRoundRobin.accordion.sandbox": "沙盒环境", "Apis.Details.Policies.CustomPolicies.ModelRoundRobin.no.models": "无可用模型。请为LLM提供者配置模型。", "Apis.Details.Policies.CustomPolicies.ModelRoundRobin.no.production.endpoints": "无生产环境端点可用。请先{configureLink}。", "Apis.Details.Policies.CustomPolicies.ModelRoundRobin.no.sandbox.endpoints": "无沙盒环境端点可用。请先{configureLink}。", "Apis.Details.Policies.CustomPolicies.ModelRoundRobin.select.endpoint": "端点", "Apis.Details.Policies.CustomPolicies.ModelRoundRobin.select.model": "模型", "Apis.Details.Policies.CustomPolicies.ModelWeightedRoundRobin.accordion.production": "生产环境", "Apis.Details.Policies.CustomPolicies.ModelWeightedRoundRobin.accordion.sandbox": "沙盒环境", "Apis.Details.Policies.CustomPolicies.ModelWeightedRoundRobin.no.models": "无可用模型。请为LLM提供者配置模型。", "Apis.Details.Policies.CustomPolicies.ModelWeightedRoundRobin.no.production.endpoints": "无生产环境端点可用。请先{configureLink}。", "Apis.Details.Policies.CustomPolicies.ModelWeightedRoundRobin.no.sandbox.endpoints": "无沙盒环境端点可用。请先{configureLink}。", "Apis.Details.Policies.DeletePolicy.cancel": "取消", "Apis.Details.Policies.DeletePolicy.confirm": "删除", "Apis.Details.Policies.DeletePolicy.delete.confirm": "确认删除", "Apis.Details.Policies.DeletePolicy.delete.confirm.content": "确定要删除{policy}策略吗？", "Apis.Details.Policies.DeletePolicy.delete.title": "删除", "Apis.Details.Policies.DraggablePolicyCard.policy.view": "查看", "Apis.Details.Policies.OperationPolicy.operation.used.in.products": "此操作用于{isUsedInAPIProduct}个API产品", "Apis.Details.Policies.Policies.title": "策略", "Apis.Details.Policies.PoliciesExpansion.fault.flow.title": "故障流", "Apis.Details.Policies.PoliciesExpansion.request.flow.title": "请求流", "Apis.Details.Policies.PoliciesExpansion.response.flow.title": "响应流", "Apis.Details.Policies.PoliciesSection.info": "API级别策略将在操作级别策略之前执行", "Apis.Details.Policies.PolicyConfigurationEditDrawer.title": "配置{policy}", "Apis.Details.Policies.PolicyConfiguringDrawer.title": "配置{policy}", "Apis.Details.Policies.PolicyForm.GeneralDetails.description": "提供策略的名称、描述和适用流程。", "Apis.Details.Policies.PolicyForm.GeneralDetails.form.applicable.flows.label": "适用流程", "Apis.Details.Policies.PolicyForm.GeneralDetails.form.description.helperText": "策略的简短描述", "Apis.Details.Policies.PolicyForm.GeneralDetails.form.description.label": "描述", "Apis.Details.Policies.PolicyForm.GeneralDetails.form.flow.type.fault": "故障", "Apis.Details.Policies.PolicyForm.GeneralDetails.form.flow.type.request": "请求", "Apis.Details.Policies.PolicyForm.GeneralDetails.form.flow.type.response": "响应", "Apis.Details.Policies.PolicyForm.GeneralDetails.form.name.helperText": "输入策略名称（例如：添加标头）", "Apis.Details.Policies.PolicyForm.GeneralDetails.form.name.label": "名称", "Apis.Details.Policies.PolicyForm.GeneralDetails.form.supported.api.label": "支持的API类型", "Apis.Details.Policies.PolicyForm.GeneralDetails.form.version.helperText": "输入策略版本（例如：v1）", "Apis.Details.Policies.PolicyForm.GeneralDetails.form.version.label": "版本", "Apis.Details.Policies.PolicyForm.GeneralDetails.title": "通用详情", "Apis.Details.Policies.PolicyForm.PolicyAttributes.add.policy.attribute": "添加策略属性", "Apis.Details.Policies.PolicyForm.PolicyAttributes.defaultValue.validation.error": "请输入有效输入", "Apis.Details.Policies.PolicyForm.PolicyAttributes.description": "定义策略的属性。", "Apis.Details.Policies.PolicyForm.PolicyAttributes.displayName.required.error": "显示名称为空", "Apis.Details.Policies.PolicyForm.PolicyAttributes.form.allowed.values.helperText": "枚举属性允许值的逗号分隔列表", "Apis.Details.Policies.PolicyForm.PolicyAttributes.form.allowed.values.label": "允许值", "Apis.Details.Policies.PolicyForm.PolicyAttributes.form.default.value.helperText": "属性的默认值（如果有）", "Apis.Details.Policies.PolicyForm.PolicyAttributes.form.default.value.label": "默认值", "Apis.Details.Policies.PolicyForm.PolicyAttributes.form.delete.tooltip": "删除", "Apis.Details.Policies.PolicyForm.PolicyAttributes.form.description.helperText": "策略属性的简短描述", "Apis.Details.Policies.PolicyForm.PolicyAttributes.form.description.label": "描述", "Apis.Details.Policies.PolicyForm.PolicyAttributes.form.description.tooltip": "描述", "Apis.Details.Policies.PolicyForm.PolicyAttributes.form.displayName.helperText": "属性显示名称", "Apis.Details.Policies.PolicyForm.PolicyAttributes.form.displayName.label": "显示名称", "Apis.Details.Policies.PolicyForm.PolicyAttributes.form.name.helperText": "属性名称", "Apis.Details.Policies.PolicyForm.PolicyAttributes.form.name.label": "名称", "Apis.Details.Policies.PolicyForm.PolicyAttributes.form.required.tooltip": "必填", "Apis.Details.Policies.PolicyForm.PolicyAttributes.form.type.label": "类型", "Apis.Details.Policies.PolicyForm.PolicyAttributes.form.validation.regex.helperText": "属性验证的正则表达式（例如：^([a-zA-Z]+)$）", "Apis.Details.Policies.PolicyForm.PolicyAttributes.form.validation.regex.label": "验证正则表达式", "Apis.Details.Policies.PolicyForm.PolicyAttributes.form.value.properties.popover.title": "值属性", "Apis.Details.Policies.PolicyForm.PolicyAttributes.form.value.properties.tooltip": "值属性", "Apis.Details.Policies.PolicyForm.PolicyAttributes.name.required.error": "名称为空", "Apis.Details.Policies.PolicyForm.PolicyAttributes.no.attributes.found": "看起来此策略没有任何属性", "Apis.Details.Policies.PolicyForm.PolicyAttributes.title": "策略属性", "Apis.Details.Policies.PolicyForm.PolicyAttributes.validationRegex.invalid": "提供的正则表达式无效", "Apis.Details.Policies.PolicyForm.PolicyCreateForm.policy.cancel": "取消", "Apis.Details.Policies.PolicyForm.PolicyCreateForm.policy.save": "保存", "Apis.Details.Policies.PolicyForm.PolicyViewForm.done": "完成", "Apis.Details.Policies.PolicyForm.SourceDetails.apiSpecificPolicy.download.error": "下载策略时出错", "Apis.Details.Policies.PolicyForm.SourceDetails.commonPolicy.download.error": "下载策略时出错", "Apis.Details.Policies.PolicyForm.SourceDetails.description": "定义支持此策略的网关。基于此选择，您可以上传包含相关业务逻辑的策略文件。", "Apis.Details.Policies.PolicyForm.SourceDetails.form.policy.file.description": "策略文件包含策略的业务逻辑", "Apis.Details.Policies.PolicyForm.SourceDetails.form.policy.file.download": "下载策略", "Apis.Details.Policies.PolicyForm.SourceDetails.form.policy.file.title": "策略文件", "Apis.Details.Policies.PolicyForm.SourceDetails.title": "网关特定详情", "Apis.Details.Policies.PolicyForm.UploadPolicyDropzone.description": "策略文件包含策略的业务逻辑", "Apis.Details.Policies.PolicyForm.UploadPolicyDropzone.dropzone.description": "点击或拖动策略文件以上传", "Apis.Details.Policies.PolicyForm.UploadPolicyDropzone.file.tooltip": "仅支持上传.j2和.xml文件", "Apis.Details.Policies.PolicyForm.UploadPolicyDropzone.title": "上传策略文件", "Apis.Details.Policies.PolicyList.add.fault.tab": "故障", "Apis.Details.Policies.PolicyList.add.new.policy": "添加新策略", "Apis.Details.Policies.PolicyList.add.request.tab": "请求", "Apis.Details.Policies.PolicyList.add.response.tab": "响应", "Apis.Details.Policies.PolicyList.title": "策略列表", "Apis.Details.Policies.SaveOperationPolicies.save": "保存", "Apis.Details.Policies.tab.api.level": "API级别策略", "Apis.Details.Policies.tab.operation.level": "操作级别策略", "Apis.Details.ProductResources.ProductResourcesEdit.api.resources": "选择API资源", "Apis.Details.ProductResources.ProductResourcesEdit.cancel": "取消", "Apis.Details.ProductResources.ProductResourcesEdit.title": "管理资源", "Apis.Details.ProductResources.ProductResourcesWorkspace.ApisnotFound": "尚未创建任何REST API", "Apis.Details.ProductResources.ProductResourcesWorkspace.empty.title": "使用左侧面板添加资源", "Apis.Details.ProductResources.ProductResourcesWorkspace.filter.an.api.helper.text": "按名称筛选", "Apis.Details.ProductResources.ProductResourcesWorkspace.filter.an.api.label": "API", "Apis.Details.ProductResources.ProductResourcesWorkspace.filter.an.api.placeholder": "筛选API", "Apis.Details.ProductResources.ProductResourcesWorkspace.find.and.select": "查找并选择API产品的资源", "Apis.Details.ProductResources.ProductResourcesWorkspace.select.an.api": "选择一个API", "Apis.Details.ProductResources.ProductResourcesWorkspace.selected": "API产品选定的资源", "Apis.Details.ProductResources.ProductResourcesWorkspace.toolbar.add.all": "全部添加", "Apis.Details.ProductResources.ProductResourcesWorkspace.toolbar.add.selected": "添加选定项", "Apis.Details.Properties.Properties.APIProduct.add.new.property.message.content": "在此为您的API添加特定的自定义属性。", "Apis.Details.Properties.Properties.add": "添加", "Apis.Details.Properties.Properties.add.new.property": "添加新属性", "Apis.Details.Properties.Properties.add.new.property.action": "操作", "Apis.Details.Properties.Properties.add.new.property.message.content": "在此为您的API添加特定的自定义属性。", "Apis.Details.Properties.Properties.add.new.property.message.title": "创建附加属性", "Apis.Details.Properties.Properties.add.new.property.table": "属性名称", "Apis.Details.Properties.Properties.add.new.property.value": "属性值", "Apis.Details.Properties.Properties.add.new.property.visibility": "可见性", "Apis.Details.Properties.Properties.api.product.properties": "API产品属性", "Apis.Details.Properties.Properties.api.properties": "API属性", "Apis.Details.Properties.Properties.cancel": "取消", "Apis.Details.Properties.Properties.editable.cancel": "取消", "Apis.Details.Properties.Properties.editable.row.edit.mode.property.value": "属性值", "Apis.Details.Properties.Properties.editable.row.property.name": "属性名称", "Apis.Details.Properties.Properties.editable.show.in.devporal": "在开发者门户显示", "Apis.Details.Properties.Properties.editable.show.in.devportal": "在开发者门户显示", "Apis.Details.Properties.Properties.editable.update": "更新", "Apis.Details.Properties.Properties.editable.visible.in.store": "在开发者门户可见", "Apis.Details.Properties.Properties.help": "属性名称应唯一，不能包含空格，不能超过80个字符，且不能是以下保留关键字：provider, version, context, status, description, subcontext, doc, lcState, name, tags。", "Apis.Details.Properties.Properties.help.main": "通常，API具有一组预定义的属性，如名称、版本、上下文等。API属性允许您为API添加特定的自定义属性。", "Apis.Details.Properties.Properties.is.mandatory": "必填字段不能为空", "Apis.Details.Properties.Properties.no.changes.to.save": "无更改可保存", "Apis.Details.Properties.Properties.property.name.empty.error": "属性名称/值不能为空", "Apis.Details.Properties.Properties.property.name.exists": "属性名称已存在", "Apis.Details.Properties.Properties.property.name.has.whitespaces": "属性名称不能包含空格", "Apis.Details.Properties.Properties.property.name.keyword.error": "属性名称不能是系统保留关键字", "Apis.Details.Properties.Properties.property.value": "值", "Apis.Details.Properties.Properties.show.add.property.custom.property.name": "{message}", "Apis.Details.Properties.Properties.show.add.property.invalid.error": "无效的属性名称", "Apis.Details.Properties.Properties.show.add.property.property.name": "名称", "Apis.Details.Properties.Properties.update.not.allowed": "*由于权限不足，您无权更新API的属性", "Apis.Details.QueryAnalysis.UpdateComplexity.fieldcomplexity": "字段", "Apis.Details.QueryAnalysis.UpdateComplexity.fieldcomplexitysum": "复杂度总和", "Apis.Details.QueryAnalysis.UpdateComplexity.table.complexity.value": "复杂度值", "Apis.Details.QueryAnalysis.UpdateComplexity.table.field": "字段", "Apis.Details.QueryAnalysis.UpdateComplexity.type.label": "类型", "Apis.Details.QueryAnalysis.UpdateComplexity.type.placeholder": "按类型搜索", "Apis.Details.QueryAnalysis.UpdateComplexity.typeName": "类型", "Apis.Details.QyeryAnalysis.UpdateComplexity.save": "设置", "Apis.Details.Rate.Limiting.operations.api.product.message.body": "将应用源操作的速率限制策略", "Apis.Details.Rate.Limiting.operations.api.product.message.caption": "单个操作的速率限制策略将由源操作中指定的策略控制", "Apis.Details.Rate.Limiting.operations.configuration": "操作配置", "Apis.Details.Rate.Limiting.operations.configuration.tooltip": "影响所有资源的配置", "Apis.Details.Rate.Limiting.operations.message.body": "您可以更改每个操作的速率限制策略", "Apis.Details.Rate.Limiting.operations.message.caption": "展开下面的操作以选择操作的速率限制策略", "Apis.Details.Rate.Limiting.operations.reset.btn": "重置", "Apis.Details.Rate.Limiting.operations.save.btn": "保存", "Apis.Details.Rate.Limiting.rate.limiting.level.api.level": "API级别", "Apis.Details.Rate.Limiting.rate.limiting.level.operation.level": "操作级别", "Apis.Details.Rate.Limiting.rate.limiting.policies": "速率限制策略", "Apis.Details.Rate.Limiting.rate.limiting.policies.helper.text": "选定的速率限制策略将应用于整个API", "Apis.Details.Resources.APIOperations.title": "资源", "Apis.Details.Resources.Components.Go.To.Definition": "编辑API定义", "Apis.Details.Resources.Components.Operations.title.disable.security.all": "禁用所有安全性", "Apis.Details.Resources.Components.Operations.title.enable.security.all": "启用所有安全性", "Apis.Details.Resources.Components.Operations.tooltip.clear.selections": "清除选择", "Apis.Details.Resources.Components.Operations.tooltip.delete.selections": "标记全部删除", "Apis.Details.Resources.Components.async.api.description": "描述", "Apis.Details.Resources.Components.async.api.description.title": "描述", "Apis.Details.Resources.Components.operation.async.api.payload.properties": "负载属性", "Apis.Details.Resources.Components.operationComponents.parameters.async.api.topic": "主题参数", "Apis.Details.Resources.Operation.Components.Description": "描述", "Apis.Details.Resources.Operation.Components.Description.title": "描述", "Apis.Details.Resources.Operation.Components.Summary": "摘要", "Apis.Details.Resources.Operation.Components.Summary.title": "摘要", "Apis.Details.Resources.Operation.Parameters": "参数", "Apis.Details.Resources.Policy.Dialog.close.editor.btn": "关闭", "Apis.Details.Resources.Policy.Dialog.save.and.close.btn": "保存并关闭", "Apis.Details.Resources.Policy.update.error": "更新资源策略时出错！", "Apis.Details.Resources.Policy.update.success": "资源策略更新成功", "Apis.Details.Resources.Resources.api.scope.deleted.successfully": "API作用域删除成功！", "Apis.Details.Resources.Resources.edit.resources.button": "编辑资源", "Apis.Details.Resources.Resources.edit.resources.title": "产品资源", "Apis.Details.Resources.Resources.resources.reset.button": "重置", "Apis.Details.Resources.Resources.resources.save.button": "保存", "Apis.Details.Resources.Resources.something.went.wrong.while.updating.the.api": "更新API时出错", "Apis.Details.Resources.components.APIRateLimiting.rate.limiting.level": "速率限制级别", "Apis.Details.Resources.components.AddOperation.add.tooltip": "添加新操作", "Apis.Details.Resources.components.AddOperation.clear.inputs.tooltip": "清除输入", "Apis.Details.Resources.components.AddOperation.http.verb": "HTTP动词", "Apis.Details.Resources.components.AddOperation.operation.target.cannot.contains.white.spaces": "操作目标不能包含空格", "Apis.Details.Resources.components.AddOperation.operation.target.or.verb.cannot.be.empty.warning": "操作目标或操作动词不能为空", "Apis.Details.Resources.components.AddOperation.operation.target.topic.name": "输入主题名称", "Apis.Details.Resources.components.AddOperation.operation.target.topic.name.label": "主题名称", "Apis.Details.Resources.components.AddOperation.operation.target.uri.pattern": "输入URI模式", "Apis.Details.Resources.components.AddOperation.operation.target.uri.pattern.label": "URI模式", "Apis.Details.Resources.components.AddOperation.operation.topic.cannot.have.path.params.warning": "WebSub主题不能有路径参数", "Apis.Details.Resources.components.AddOperation.option": "选项", "Apis.Details.Resources.components.AddOperation.option.title": "选择OPTIONS方法以将OPTIONS调用发送到后端。如果未选择OPTIONS方法，OPTIONS调用将从网关返回允许的方法。", "Apis.Details.Resources.components.AddParameter.clear.inputs.tooltip": "清除输入", "Apis.Details.Resources.components.Operation.Delete": "删除", "Apis.Details.Resources.components.Operation.cannot.delete.when.used.in.api.products": "当用于API产品时无法删除操作", "Apis.Details.Resources.components.Operation.delete.operation": "删除操作", "Apis.Details.Resources.components.Operation.disable.security.when.used.in.api.products": "安全性已启用", "Apis.Details.Resources.components.Operation.security.operation": "安全性", "Apis.Details.Resources.components.Operation.this.operation.used.in.products": "此操作用于{isUsedInAPIProduct}个API产品", "Apis.Details.Resources.components.Operation.undo.delete": "撤销删除", "Apis.Details.Resources.components.async.api.runtime.title": "运行时", "Apis.Details.Resources.components.async.api.url.http.callback.url": "HTTP回调URL", "Apis.Details.Resources.components.async.api.url.https.callback.url": "HTTPS回调URL", "Apis.Details.Resources.components.async.api.url.mapping.label": "URL映射", "Apis.Details.Resources.components.enabled.security": "无安全性", "Apis.Details.Resources.components.operationComponents.AWSLambdaSettings.Title": "AWS Lambda设置", "Apis.Details.Resources.components.operationComponents.AWSLambdaSettings.encode": "检查请求体是否应进行base64编码", "Apis.Details.Resources.components.operationComponents.AddParameter.add": "添加", "Apis.Details.Resources.components.operationComponents.AddParameter.add.tooltip": "添加新参数", "Apis.Details.Resources.components.operationComponents.AddParameter.enter.content.type": "输入内容类型", "Apis.Details.Resources.components.operationComponents.AddParameter.enter.parameter.name": "输入参数名称", "Apis.Details.Resources.components.operationComponents.AddParameter.parameter.name.already.exists": "参数名称已存在", "Apis.Details.Resources.components.operationComponents.AddParameter.type": "参数类型", "Apis.Details.Resources.components.operationComponents.EditParameter.close": "关闭", "Apis.Details.Resources.components.operationComponents.EditParameter.data.format": "数据格式", "Apis.Details.Resources.components.operationComponents.EditParameter.data.type": "数据类型", "Apis.Details.Resources.components.operationComponents.EditParameter.description": "描述", "Apis.Details.Resources.components.operationComponents.EditParameter.done": "完成", "Apis.Details.Resources.components.operationComponents.EditParameter.name": "名称", "Apis.Details.Resources.components.operationComponents.EditParameter.required": "必填", "Apis.Details.Resources.components.operationComponents.EditParameter.select.format.of.data.type": "选择数据类型的格式", "Apis.Details.Resources.components.operationComponents.EditParameter.select.format.of.data.type.none.option": "无数据类型", "Apis.Details.Resources.components.operationComponents.EditParameter.select.schema.data.type": "选择模式类型", "Apis.Details.Resources.components.operationComponents.EditParameter.title": "编辑", "Apis.Details.Resources.components.operationComponents.EditParameter.type": "参数类型", "Apis.Details.Resources.components.operationComponents.EditParameter.use.done.button.to.persist.changes": "使用页面上的完成按钮保存更改", "Apis.Details.Resources.components.operationComponents.EditPayloadProperty.close": "关闭", "Apis.Details.Resources.components.operationComponents.EditPayloadProperty.done": "完成", "Apis.Details.Resources.components.operationComponents.EditPayloadProperty.title": "编辑", "Apis.Details.Resources.components.operationComponents.ListParameter.actions": "操作", "Apis.Details.Resources.components.operationComponents.ListParameter.body": "正文", "Apis.Details.Resources.components.operationComponents.ListParameter.data.type": "数据类型", "Apis.Details.Resources.components.operationComponents.ListParameter.delete": "删除", "Apis.Details.Resources.components.operationComponents.ListParameter.edit": "编辑", "Apis.Details.Resources.components.operationComponents.ListParameter.no": "否", "Apis.Details.Resources.components.operationComponents.ListParameter.parameter.name": "名称/内容类型", "Apis.Details.Resources.components.operationComponents.ListParameter.parameter.type": "参数类型", "Apis.Details.Resources.components.operationComponents.ListParameter.required": "必填", "Apis.Details.Resources.components.operationComponents.ListParameter.yes": "是", "Apis.Details.Resources.components.operationComponents.ListPayloadProperties.edit": "编辑", "Apis.Details.Resources.components.operationComponents.ListPayloadProps.delete": "删除", "Apis.Details.Resources.components.operationComponents.OperationGovernance.Security.tooltip": "这将启用/禁用运行时配置页面中定义的应用程序级别安全性。", "Apis.Details.Resources.components.operationComponents.OperationGovernance.operation.scope.create.new.scope": "创建新作用域", "Apis.Details.Resources.components.operationComponents.OperationGovernance.operation.scope.helperText": "选择一个作用域以控制对此操作的权限", "Apis.Details.Resources.components.operationComponents.OperationGovernance.operation.scope.label.default": "操作作用域", "Apis.Details.Resources.components.operationComponents.OperationGovernance.operation.scope.label.notAvailable": "无可用作用域", "Apis.Details.Resources.components.operationComponents.OperationGovernance.rate.limiting.API.level": "API级别", "Apis.Details.Resources.components.operationComponents.OperationGovernance.rate.limiting.button.text": "更改速率限制级别", "Apis.Details.Resources.components.operationComponents.OperationGovernance.rate.limiting.governed.by": "速率限制由以下控制", "Apis.Details.Resources.components.operationComponents.OperationGovernance.rate.limiting.helperText.section1": "使用", "Apis.Details.Resources.components.operationComponents.OperationGovernance.rate.limiting.helperText.section2": "操作级别", "Apis.Details.Resources.components.operationComponents.OperationGovernance.rate.limiting.helperText.section3": "速率限制以", "Apis.Details.Resources.components.operationComponents.OperationGovernance.rate.limiting.helperText.section4": "启用", "Apis.Details.Resources.components.operationComponents.OperationGovernance.rate.limiting.helperText.section5": "每个操作的速率限制", "Apis.Details.Resources.components.operationComponents.OperationGovernance.rate.limiting.policy": "速率限制策略", "Apis.Details.Resources.components.operationComponents.OperationGovernance.rate.limiting.policy.select": "为此操作选择速率限制策略", "Apis.Details.Resources.components.operationComponents.OperationGovernance.subTitle": "(安全性、速率限制和作用域)", "Apis.Details.Resources.components.operationComponents.OperationGovernance.title": "操作治理", "Apis.Details.Resources.components.operationComponents.content.type": "内容类型", "Apis.Details.Resources.components.operationComponents.data.type": "数据类型", "Apis.Details.Resources.components.operationComponents.data.type.helper": "选择数据类型", "Apis.Details.Resources.components.operationComponents.name.helper.text": "输入属性名称", "Apis.Details.Resources.components.operationComponents.name.label": "名称", "Apis.Details.Resources.components.operationComponents.parameter.name": "参数名称", "Apis.Details.Resources.components.operationComponents.parameter.name.exists": "参数类型已存在", "Apis.Details.Resources.components.operationComponents.required": "必填", "Apis.Details.Resources.components.operationComponents.required.helper.text": "检查参数是否为必填。", "Apis.Details.Resources.components.operationComponents.select.parameter.type": "选择参数类型", "Apis.Details.Scopes.Create.CreateScope.label.display.name": "显示名称", "Apis.Details.Scopes.Create.CreateScope.label.name": "名称", "Apis.Details.Scopes.Create.CreateScope.placeholder.display.name": "作用域显示名称", "Apis.Details.Scopes.Create.CreateScope.placholder.name": "作用域名称", "Apis.Details.Scopes.Create.CreateScope.roles.label": "角色", "Apis.Details.Scopes.Create.CreateScope.roles.placeholder": "输入角色并按Enter", "Apis.Details.Scopes.Create.Scope.validate.role.error": "验证角色时出错：{role}", "Apis.Details.Scopes.Create.Scope.validate.scope.error": "验证作用域时出错：{value}", "Apis.Details.Scopes.CreateScope.cancel": "取消", "Apis.Details.Scopes.CreateScope.create.new.scope": "创建新作用域", "Apis.Details.Scopes.CreateScope.description.about.the.scope": "关于作用域的简短描述", "Apis.Details.Scopes.CreateScope.description.about.the.scope.label": "描述", "Apis.Details.Scopes.CreateScope.description.about.the.scope.placeholder": "关于此作用域的简短描述", "Apis.Details.Scopes.CreateScope.description.label.about.the.scope": "描述", "Apis.Details.Scopes.CreateScope.description.placeholder.about.the.scope": "关于此作用域的简短描述", "Apis.Details.Scopes.CreateScope.roles.help": "输入有效角色并按`Enter`键", "Apis.Details.Scopes.CreateScope.save": "保存", "Apis.Details.Scopes.CreateScope.saving": "保存中", "Apis.Details.Scopes.CreateScope.scope.added.successfully": "作用域添加成功", "Apis.Details.Scopes.CreateScope.scope.updated.successfully": "作用域更新成功", "Apis.Details.Scopes.CreateScope.short.description.name": "输入作用域名称（例如：creator）", "Apis.Details.Scopes.Edit.Scope.validate.role.error": "验证角色时出错：{role}", "Apis.Details.Scopes.EditScope.cancel": "取消", "Apis.Details.Scopes.EditScope.description.about.the.scope.label": "描述", "Apis.Details.Scopes.EditScope.display.name.label": "显示名称", "Apis.Details.Scopes.EditScope.display.name.placeholder": "作用域显示名称", "Apis.Details.Scopes.EditScope.name.label": "名称", "Apis.Details.Scopes.EditScope.roles.label": "角色", "Apis.Details.Scopes.EditScope.roles.placeholder": "输入角色并按Enter键", "Apis.Details.Scopes.EditScope.short.description.about.the.scope": "关于此作用域的简短描述", "Apis.Details.Scopes.EditScope.short.description.about.the.scope.placeholder": "关于此作用域的简短描述", "Apis.Details.Scopes.EditScope.update": "更新", "Apis.Details.Scopes.EditScope.update.scope": "更新作用域", "Apis.Details.Scopes.EditScopes.roles.help": "输入有效角色并按`Enter`键", "Apis.Details.Scopes.Roles.Invalid": "角色无效", "Apis.Details.Scopes.Roles.User.Invalid": "至少需要一个与API创建者关联的角色", "Apis.Details.Scopes.Scopes.create.scopes.button": "创建作用域", "Apis.Details.Scopes.Scopes.create.scopes.title": "创建API本地作用域", "Apis.Details.Scopes.Scopes.heading.edit.scope.heading": "作用域", "Apis.Details.Scopes.Scopes.heading.scope.add_new": "添加新本地作用域", "Apis.Details.Scopes.Scopes.heading.scope.heading": "作用域", "Apis.Details.Scopes.Scopes.heading.scope.title.tooltip": "管理此API的本地作用域", "Apis.Details.Scopes.Scopes.heading.scope.title.tooltip2": "管理此API的本地作用域", "Apis.Details.Scopes.Scopes.scopes.enable.fine.gained.access.control": "作用域可根据用户角色对API资源进行细粒度访问控制。", "Apis.Details.Scopes.Scopes.table.header.actions": "操作", "Apis.Details.Scopes.Scopes.table.header.description": "描述", "Apis.Details.Scopes.Scopes.table.header.name": "名称", "Apis.Details.Scopes.Scopes.table.header.roles": "角色", "Apis.Details.Scopes.Scopes.table.header.usages": "使用场景", "Apis.Details.Scopes.Scopes.update.not.allowed": "*由于权限不足，您无权更新此API的作用域", "Apis.Details.Scopes.visibility.CreateScope.roles.help": "输入有效角色并按Enter键", "Apis.Details.Scopes.visibility.CreateScope.roles.placeholder": "输入角色并按Enter键", "Apis.Details.Security.AddPolicy.add.policy.to.api": "向API添加策略", "Apis.Details.Security.AddPolicy.add.threat.protection.policy": "添加新威胁防护策略", "Apis.Details.Security.AddPolicy.cancel": "取消", "Apis.Details.Security.AddPolicy.policy": "策略", "Apis.Details.Security.AddPolicy.policy.label": "策略：", "Apis.Details.Security.AddPolicy.policy.type.label": "策略类型：", "Apis.Details.Security.AddPolicy.select.policy": "请选择策略", "Apis.Details.Security.AddPolicy.threat.protection.policy.add.failure": "添加威胁防护策略失败。", "Apis.Details.Security.AddPolicy.threat.protection.policy.add.success": "威胁防护策略添加成功。", "Apis.Details.Security.SecurityOverview.add.threat.protection.policy": "添加新威胁防护策略", "Apis.Details.Security.SecurityOverview.delete": "删除", "Apis.Details.Security.SecurityOverview.manage.threat.protection.policies": "管理威胁防护策略", "Apis.Details.Security.SecurityOverview.policy": "策略", "Apis.Details.Security.SecurityOverview.policy.name": "策略名称", "Apis.Details.Security.SecurityOverview.policy.remove.failure": "删除策略失败。", "Apis.Details.Security.SecurityOverview.policy.remove.success": "策略删除成功。", "Apis.Details.Security.SecurityOverview.policy.type": "策略类型", "Apis.Details.Security.SecurityOverview.threat.protection.policies": "威胁防护策略", "Apis.Details.ShareAPI.Organization.Subscriptions.no.selected.organizations": "未选择组织", "Apis.Details.ShareAPI.Shared.organizations.selection.all.tooltip": "选择此项可将API共享给当前组织下的所有现有组织及未来注册的新组织。", "Apis.Details.ShareAPI.Shared.organizations.selection.none.tooltip": "这将阻止API被共享给当前组织下的任何现有组织或未来注册的新组织。", "Apis.Details.ShareAPI.cancel": "取消", "Apis.Details.ShareAPI.save": "保存", "Apis.Details.ShareAPI.subValidationDisabled.dialog.description": "此API的订阅验证已禁用。这将允许共享组织内任何持有有效令牌的用户无需订阅即可调用API。确认继续？", "Apis.Details.ShareAPI.subValidationDisabled.dialog.title": "警告！", "Apis.Details.ShareAPI.subValidationEnabled.dialog.description": "部分组织未设置订阅策略。将为其应用根组织的订阅策略。确认继续？", "Apis.Details.ShareAPI.title": "共享API", "Apis.Details.ShareAPI.update.error": "更新API共享配置时出错", "Apis.Details.ShareAPI.update.success": "API共享配置更新成功", "Apis.Details.Subscriptions.Listing.column.header.application": "应用", "Apis.Details.Subscriptions.Listing.column.header.subscriber": "订阅者", "Apis.Details.Subscriptions.Listing.column.header.subscription.actions": "操作", "Apis.Details.Subscriptions.Listing.column.header.subscription.invoice": "发票", "Apis.Details.Subscriptions.Listing.column.header.subscription.status": "状态", "Apis.Details.Subscriptions.Listing.column.header.throttling.tier": "限流层级", "Apis.Details.Subscriptions.Subscriber.no.claims": "无订阅者声明数据", "Apis.Details.Subscriptions.SubscriptionAvailability.all.tenants": "对所有租户可用", "Apis.Details.Subscriptions.SubscriptionAvailability.current.tenant.only": "仅对当前租户可用", "Apis.Details.Subscriptions.SubscriptionAvailability.specific.tenants": "对指定租户可用", "Apis.Details.Subscriptions.SubscriptionAvailability.sub.heading": "设置订阅对租户的可用性", "Apis.Details.Subscriptions.SubscriptionAvailability.subscription.availability": "订阅可用性", "Apis.Details.Subscriptions.SubscriptionPoliciesManage.APIProduct.sub.heading": "为API关联业务计划", "Apis.Details.Subscriptions.SubscriptionPoliciesManage.business.plans": "业务计划", "Apis.Details.Subscriptions.SubscriptionPoliciesManage.sub.heading": "为API关联业务计划", "Apis.Details.Subscriptions.SubscriptionPoliciesManage.sub.migrated": "以下策略是从旧版APIM迁移而来。您可以取消勾选并选择其他策略。注意：此操作不可逆。", "Apis.Details.Subscriptions.Subscriptions.cancel": "取消", "Apis.Details.Subscriptions.Subscriptions.save": "保存", "Apis.Details.Subscriptions.Subscriptions.subValidationDisabled.dialog.description": "取消选择所有订阅策略将禁用此API的订阅验证。这将允许任何持有有效令牌的用户无需订阅即可调用API。", "Apis.Details.Subscriptions.Subscriptions.subValidationDisabled.dialog.description.question": "确认禁用订阅验证？", "Apis.Details.Subscriptions.Subscriptions.subValidationDisabled.dialog.title": "警告！", "Apis.Details.Subscriptions.Subscriptions.update.error": "更新订阅配置时出错", "Apis.Details.Subscriptions.Subscriptions.update.success": "订阅配置更新成功", "Apis.Details.Subscriptions.Subscriptions.validation.disabled": "此API的订阅验证已禁用", "Apis.Details.Subscriptions.SubscriptionsTable.active.subs.state": "活跃", "Apis.Details.Subscriptions.SubscriptionsTable.blocked.production.only.subs.state": "仅生产环境禁用", "Apis.Details.Subscriptions.SubscriptionsTable.blocked.subs.state": "已禁用", "Apis.Details.Subscriptions.SubscriptionsTable.contact.subscribers": "联系订阅者", "Apis.Details.Subscriptions.SubscriptionsTable.error.subscription.block": "错误：无法禁用订阅（原因：{message}）", "Apis.Details.Subscriptions.SubscriptionsTable.error.subscription.block.prod.only": "错误：无法禁用订阅（原因：{message}）", "Apis.Details.Subscriptions.SubscriptionsTable.error.subscription.unblock": "错误：无法启用订阅（原因：{message}）", "Apis.Details.Subscriptions.SubscriptionsTable.manage.subscriptions": "管理订阅", "Apis.Details.Subscriptions.SubscriptionsTable.no.subscriptions": "无订阅数据", "Apis.Details.Subscriptions.SubscriptionsTable.sub.heading": "管理API订阅", "Apis.Details.Subscriptions.SubscriptionsTable.subscriber.info.error": "获取订阅者信息时出错", "Apis.Details.Subscriptions.SubscriptionsTable.subscription.blocked": "订阅已禁用。", "Apis.Details.Subscriptions.SubscriptionsTable.subscription.blocked.prod.only": "订阅仅在生产环境禁用。", "Apis.Details.Subscriptions.SubscriptionsTable.subscription.unblocked": "订阅已启用。", "Apis.Details.Subscriptions.SubscriptionsTable.subscriptions.error": "获取订阅数据时出错", "Apis.Details.Topic.components.operationComponents.OperationGovernance.security.label": "安全", "Apis.Details.Topics.components.AddOperation.op.type": "类型", "Apis.Details.Topics.components.AddOperation.operation.topic.or.type.cannot.be.empty.warning": "主题名称或主题类型不能为空", "Apis.Details.Topics.components.operationComponents.EditParameter.data.type": "数据类型", "Apis.Details.Topics.components.operationComponents.EditParameter.description": "描述", "Apis.Details.Topics.components.operationComponents.EditParameter.select.data.type": "选择数据类型", "Apis.Details.Topics.components.operationComponents.EditPayloadProperty.data.type": "数据类型", "Apis.Details.Topics.components.operationComponents.EditPayloadProperty.description": "描述", "Apis.Details.Topics.components.operationComponents.EditPayloadProperty.select.data.type": "选择数据类型", "Apis.Details.Topics.components.operationComponents.ListParameter.actions": "操作", "Apis.Details.Topics.components.operationComponents.ListParameter.param.datatype": "数据类型", "Apis.Details.Topics.components.operationComponents.ListParameter.param.description": "描述", "Apis.Details.Topics.components.operationComponents.ListParameter.param.name": "名称", "Apis.Details.Topics.components.operationComponents.ListPayloadProps.actions": "操作", "Apis.Details.Topics.components.operationComponents.ListPayloadProps.data.type": "数据类型", "Apis.Details.Topics.components.operationComponents.ListPayloadProps.description": "描述", "Apis.Details.Topics.components.operationComponents.ListPayloadProps.name": "名称", "Apis.Details.Topics.components.operationComponents.OperationGovernance.Security.tooltip": "这将启用/禁用运行时配置页面中定义的应用程序级安全策略。", "Apis.Details.Topics.components.operationComponents.OperationGovernance.no.scopes.available": "无可用作用域", "Apis.Details.Topics.components.operationComponents.OperationGovernance.operation.scope.create.new.scope": "创建新作用域", "Apis.Details.Topics.components.operationComponents.OperationGovernance.operation.scope.helperText": "选择作用域以控制此操作的权限", "Apis.Details.Topics.components.operationComponents.OperationGovernance.operation.scope.label.default": "操作作用域", "Apis.Details.Topics.components.operationComponents.OperationGovernance.operation.scope.label.notAvailable": "无可用作用域", "Apis.Details.Topics.components.operationComponents.OperationGovernance.search.scopes.placeholder": "搜索作用域", "Apis.Details.Topics.components.operationComponents.OperationGovernance.security.switch": "安全", "Apis.Details.Topics.components.operationComponents.OperationGovernance.subTitle": "（安全与作用域）", "Apis.Details.Topics.components.operationComponents.OperationGovernance.title": "操作治理", "Apis.Details.TryOutConsole.token.helper": "生成或提供内部API密钥", "Apis.Details.TryOutConsole.token.label": "内部API密钥", "Apis.Details.components.APIDetailsTopMenu.advertise.only.label": "第三方", "Apis.Details.components.APIDetailsTopMenu.ai.api.label": "AI/LLM API", "Apis.Details.components.APIDetailsTopMenu.created.by": "创建者：", "Apis.Details.components.APIDetailsTopMenu.current.api": "当前API", "Apis.Details.components.APIDetailsTopMenu.error": "下载API时出错。", "Apis.Details.components.APIDetailsTopMenu.last.updated.time": "最后更新：", "Apis.Details.components.APIDetailsTopMenu.read.only.label": "只读", "Apis.Details.components.APIDetailsTopMenu.state": "状态", "Apis.Details.components.APIDetailsTopMenu.view.in.portal": "在开发者门户查看", "Apis.Details.components.CreateNewVersionButton.create.new.version": "创建新版本", "Apis.Details.components.DeleteApiButton.button.cancel": "取消", "Apis.Details.components.DeleteApiButton.button.delete": "删除", "Apis.Details.components.DeleteApiButton.delete": "删除", "Apis.Details.components.DeleteApiButton.text.description": "{type} <b> {name} {version} </b> 将被永久删除。", "Apis.Details.components.DeleteApiButton.title": "删除{type}", "Apis.Details.components.ShareButton.share": "共享", "Apis.Details.components.TransitionStateApiButton.button.cancel": "取消", "Apis.Details.components.TransitionStateApiButton.button.confirm": "{selectedState}", "Apis.Details.components.TransitionStateApiButton.text.description": "{type} <b> {name} {version} </b> 将被永久{selectedState}。", "Apis.Details.components.TransitionStateApiButton.title": "{selectedState} {type}", "Apis.Details.components.api.delete.error": "删除API时出错！", "Apis.Details.components.api.delete.success": "API {name} 删除成功", "Apis.Details.components.api.product.delete.error": "删除API产品时出错！", "Apis.Details.components.api.product.delete.success": "API产品 {name} 删除成功", "Apis.Details.environments.deploymentOnBoarding.formattedMessage.description": "已停用的API无法部署新修订版本。", "Apis.Details.environments.deploymentOnBoarding.formattedMessage.head": "部署", "Apis.Details.environments.deploymentOnBoarding.formattedMessage.warningTitle": "无法部署已停用的API", "Apis.Details.index.Tryout.menu.name": "测试", "Apis.Details.index.api.definition2": "API定义", "Apis.Details.index.api.not.found.body": "找不到指定ID的API", "Apis.Details.index.api.not.found.title": "在{environmentLabel}环境中未找到API", "Apis.Details.index.api.product.update.error": "更新{apiName} API产品时出错！", "Apis.Details.index.api.product.update.success": "{updatedAPIName} API产品更新成功", "Apis.Details.index.api.update.success": "{updatedAPIName} API更新成功", "Apis.Details.index.asyncApi.definition": "AsyncAPI定义", "Apis.Details.index.business.info": "商业信息", "Apis.Details.index.comments": "评论", "Apis.Details.index.compliance": "合规性", "Apis.Details.index.deploy.title": "部署", "Apis.Details.index.design.api.configs.title": "API配置", "Apis.Details.index.design.api.configs.title.tooltip": "如果修改了API配置，需要重新部署API才能使更改在API网关上生效。", "Apis.Details.index.design.configs": "基本信息", "Apis.Details.index.design.portal.configs.title": "门户配置", "Apis.Details.index.develop.title": "开发", "Apis.Details.index.documents": "文档", "Apis.Details.index.endpoints": "端点", "Apis.Details.index.environments": "部署", "Apis.Details.index.external-stores": "外部开发者门户", "Apis.Details.index.left.menu.scope": "本地作用域", "Apis.Details.index.lifecycle": "生命周期", "Apis.Details.index.monetization": "变现", "Apis.Details.index.operations": "操作", "Apis.Details.index.overview": "概览", "Apis.Details.index.policies": "策略", "Apis.Details.index.properties": "属性", "Apis.Details.index.publish.title": "发布", "Apis.Details.index.resources": "资源", "Apis.Details.index.runtime.configs": "运行时", "Apis.Details.index.schema.definition": "Schema定义", "Apis.Details.index.subscriptions": "订阅", "Apis.Details.index.test.title": "测试", "Apis.Details.index.topics": "主题", "Apis.Details.index.wsdl.definition": "WSDL定义", "Apis.Details.local.Scopes.heading.edit.heading": "本地作用域", "Apis.Details.local.Scopes.heading.scope.heading": "本地作用域", "Apis.Details.scopes.Edit.text.editor.edit": "编辑", "Apis.Listing.AIAPI.ai.api": "AI/LLM API", "Apis.Listing.ApiTableView.context": "上下文", "Apis.Listing.ApiTableView.items.per.page": "每页条目数", "Apis.Listing.ApiTableView.name": "名称", "Apis.Listing.ApiTableView.provider": "提供商", "Apis.Listing.ApiTableView.version": "版本", "Apis.Listing.ApiThumb.by": "由", "Apis.Listing.ApiThumb.by.colon": "：", "Apis.Listing.ApiThumb.channel": "通道", "Apis.Listing.ApiThumb.context": "上下文", "Apis.Listing.ApiThumb.owners": "所有者", "Apis.Listing.ApiThumb.owners.business": "业务", "Apis.Listing.ApiThumb.owners.technical": "技术", "Apis.Listing.ApiThumb.version": "版本", "Apis.Listing.Components.Create.API": "创建API", "Apis.Listing.SampleAPI.SampleAPI.ai.api.create.title": "创建AI/LLM API", "Apis.Listing.SampleAPI.SampleAPI.ai.api.import.content": "通过导入服务提供商API创建AI/LLM API", "Apis.Listing.SampleAPI.SampleAPI.create.new": "开始吧！", "Apis.Listing.SampleAPI.SampleAPI.create.new.description": "选择创建API的方式", "Apis.Listing.SampleAPI.SampleAPI.graphql.api": "GraphQL", "Apis.Listing.SampleAPI.SampleAPI.graphql.import.sdl.content": "使用现有模式或端点", "Apis.Listing.SampleAPI.SampleAPI.graphql.import.sdl.title": "创建GraphQL API", "Apis.Listing.SampleAPI.SampleAPI.no.apis.deployed": "尚未部署任何API", "Apis.Listing.SampleAPI.SampleAPI.rest.api": "REST API", "Apis.Listing.SampleAPI.SampleAPI.rest.api.import.open.content": "导入OAS 3或Swagger 2.0定义", "Apis.Listing.SampleAPI.SampleAPI.rest.api.import.open.title": "导入Open API", "Apis.Listing.SampleAPI.SampleAPI.rest.api.scratch.content": "设计和原型化新的REST API", "Apis.Listing.SampleAPI.SampleAPI.rest.api.scratch.title": "从零开始", "Apis.Listing.SampleAPI.SampleAPI.rest.d.sample.content": "示例Pizza Shack API", "Apis.Listing.SampleAPI.SampleAPI.rest.d.sample.title": "部署示例API", "Apis.Listing.SampleAPI.SampleAPI.service.catalog.api": "服务目录", "Apis.Listing.SampleAPI.SampleAPI.soap.api": "SOAP API", "Apis.Listing.SampleAPI.SampleAPI.soap.import.wsdl.content": "生成REST或创建直通API", "Apis.Listing.SampleAPI.SampleAPI.soap.import.wsdl.title": "导入WSDL", "Apis.Listing.SampleAPI.SampleAPI.streaming.api": "流式API", "Apis.Listing.SampleAPI.SampleAPI.streaming.design.new.title": "Web Socket API", "Apis.Listing.SampleAPI.SampleAPI.streaming.design.new.ws.content": "创建Web Socket API", "Apis.Listing.SampleAPI.SampleAPI.streaming.import.content": "上传文件或提供Async API URL", "Apis.Listing.SampleAPI.SampleAPI.streaming.import.title": "导入AsyncAPI", "Apis.Listing.SampleAPI.SampleAPI.streaming.sse.content": "创建服务器发送事件API", "Apis.Listing.SampleAPI.SampleAPI.streaming.sse.title": "SSE API", "Apis.Listing.SampleAPI.SampleAPI.streaming.websub.content": "创建Webhook/WebSub API", "Apis.Listing.SampleAPI.SampleAPI.streaming.websub.title": "Webhook API", "Apis.Listing.SampleAPI.SampleAPIProduct.title": "开始吧！", "Apis.Listing.SampleAPI.continue.on.close": "关闭", "Apis.Listing.SampleAPI.continue.on.error": "继续", "Apis.Listing.SampleAPI.popup.create.complete": "API创建成功！", "Apis.Listing.SampleAPI.popup.create.inprogress": "正在创建示例API...", "Apis.Listing.SampleAPI.popup.deploy.complete": "API部署成功！", "Apis.Listing.SampleAPI.popup.deploy.inprogress": "正在部署示例API...", "Apis.Listing.SampleAPI.popup.deploy.pending": "API修订部署请求已发送", "Apis.Listing.SampleAPI.popup.publish.complete": "API发布成功！", "Apis.Listing.SampleAPI.popup.publish.inprogress": "正在将示例API发布到开发者门户...", "Apis.Listing.SampleAPI.popup.revision.complete": "API修订创建成功！", "Apis.Listing.SampleAPI.popup.revision.inprogress": "正在创建示例API的修订...", "Apis.Listing.SampleAPI.popup.update.complete": "API更新成功！", "Apis.Listing.SampleAPI.popup.update.inprogress": "正在更新示例API...", "Apis.Listing.SampleAPIProduct.description": "将多个API资源组合成单个API", "Apis.Listing.SampleAPIProduct.onboarding.menu.card.name": "API产品", "Apis.Listing.TableView.TableView.def.flag": "[定义]", "Apis.Listing.TableView.TableView.doc.flag": "[文档]", "Apis.Listing.TableView.TableView.error.loading": "加载API时出错", "Apis.Listing.TaskState.generic.error.prefix": "操作出错：", "Apis.Listing.TaskState.governance.violation": "违反了一个或多个治理策略。请检查配置。", "Apis.Listing.components.ImageGenerator.DefThumb.apiName": "API名称", "Apis.Listing.components.ImageGenerator.DefThumb.apiVersion": "API版本", "Apis.Listing.components.ImageGenerator.DocThumb.apiName": "API名称", "Apis.Listing.components.ImageGenerator.DocThumb.apiVersion": "API版本", "Apis.Listing.components.ImageGenerator.DocThumb.sourceType": "来源类型：", "Apis.Listing.components.ImageGenerator.ThumbnailView.cancel.btn": "取消", "Apis.Listing.components.ImageGenerator.ThumbnailView.remove": "移除", "Apis.Listing.components.ImageGenerator.ThumbnailView.remove.btn": "移除", "Apis.Listing.components.ImageGenerator.ThumbnailView.save.btn": "保存", "Apis.Listing.components.ImageGenerator.ThumbnailView.saving.btn": "保存中", "Apis.Listing.components.ImageGenerator.ThumbnailView.thumbnail.remove.success": "缩略图移除成功", "Apis.Listing.components.ImageGenerator.ThumbnailView.thumbnail.upload.error": "上传新缩略图时出错，请重试。", "Apis.Listing.components.ImageGenerator.ThumbnailView.thumbnail.upload.success": "缩略图上传成功", "Apis.Listing.components.ImageGenerator.ThumbnailView.thumbnail.validation.error": "文件无效或API信息未正确设置。", "Apis.Listing.components.ImageGenerator.ThumbnailView.upload": "上传", "Apis.Listing.components.ImageGenerator.ThumbnailView.upload.btn": "上传", "Apis.Listing.components.ImageGenerator.ThumbnailView.uploading.btn": "上传中", "Apis.Listing.components.TopMenu.api.singular": "API", "Apis.Listing.components.TopMenu.apiproduct.singular": "API产品", "Apis.Listing.components.TopMenu.apiproducts": "API产品", "Apis.Listing.components.TopMenu.apiproducts.results": "API产品", "Apis.Listing.components.TopMenu.apis": "API", "Apis.Listing.components.TopMenu.create.an.api.product": "创建API产品", "Apis.Listing.components.TopMenu.create.api": "创建API", "Apis.Listing.components.TopMenu.create.api.with.ai": "使用AI创建API", "Apis.Listing.components.TopMenu.displaying": "总计：", "Apis.Listing.components.TopMenu.search.results": "搜索结果", "Apis.Listing.components.TopMenu.search.results.singular": "搜索结果", "Apis.Listing.components.TopMenu.unified.search": "统一搜索", "Apis.Listing.import.from.service.catalog.title": "从服务目录导入", "Apis.Shared.AppErrorBoundary.something.went.wrong": "糟糕！出错了", "Apis.Shared.ConfirmDialog.are.you.sure": "确定吗？", "Apis.Shared.ConfirmDialog.cancel": "取消", "Apis.Shared.ConfirmDialog.ok": "确定", "Apis.Shared.ConfirmDialog.please.confirm": "请确认", "Apis.Shared.PublisherRootErrorBoundary.refresh": "刷新", "Apis.Shared.PublisherRootErrorBoundary.refresh.or.try.again.message": "您可以现在刷新页面或稍后重试", "Apis.Shared.PublisherRootErrorBoundary.something.went.wrong.while.rendering.button": "渲染时出错：", "Apis.Shared.PublisherRootErrorBoundary.something.went.wrong.while.rendering.heading": "渲染时出错：", "Apis.Shared.RedirectToLogin.you.will.be.redirected.to": "您将被重定向到{page}", "App.Components.Common.Policies.Create.Policy.success": "策略创建成功！", "App.Components.Footer.Feedback.response.msg": "感谢您的反馈。", "App.Components.Footer.Feedback.response.msg.dialog.cancel.btn": "取消", "App.Components.Footer.Feedback.response.msg.dialog.feedback.description": "描述您的问题或分享想法", "App.Components.Footer.Feedback.response.msg.dialog.feedback.text": "消息", "App.Components.Footer.Feedback.response.msg.dialog.feedback.type": "在下方输入您的消息", "App.Components.Footer.Feedback.response.msg.dialog.send.btn": "发送", "App.Components.Footer.Feedback.response.msg.dialog.title": "发送反馈", "App.Components.Policies.Drop.Zone.text.label": "将策略拖放到此处", "App.Components.Scopes.Create.Scope.validate.role.error": "验证角色时出错：{role}", "App.Components.Scopes.Create.Scope.validate.scope.error": "验证作用域时出错：{scope}", "Applications.Details.Invoice.Dialog.No.Data.Available": "无可用数据", "Applications.Details.Invoice.Dialog.close": "关闭", "Applications.Details.Invoice.Dialog.data.not.found": "未找到此订阅的待处理发票数据。", "Applications.Details.Invoice.upcoming.invoice.table": "即将到来的发票", "Applications.Details.Invoice.view.btn": "查看发票", "Base.Errors.AuthorizedError.default_body": "用户无权访问该API", "Base.Errors.AuthorizedError.default_tittle": "授权错误", "Base.Errors.ResourceNotFound.api.list": "API列表", "Base.Errors.ResourceNotFound.api.product.list": "API产品列表", "Base.Errors.ResourceNotFound.more.links": "您可以查看以下链接", "Base.Errors.ResourceNotfound.default_body": "您查找的页面不可用", "Base.Errors.ResourceNotfound.default_tittle": "页面未找到", "Base.Footer.Footer.product_details": "WSO2 API-M v4.5.0 | © 2025 WSO2 LLC", "Base.Header.avatar.Avatar.logout": "注销", "Base.Header.headersearch.HeaderSearch.search_api.tooltip": "搜索", "Base.Header.headersearch.HeaderSearch.tooltip.option0": "内容 [默认]", "Base.Header.headersearch.HeaderSearch.tooltip.option1": "名称 [语法 - name:xxxx]", "Base.Header.headersearch.HeaderSearch.tooltip.option10": "属性 [语法 - property_name:property_value]", "Base.Header.headersearch.HeaderSearch.tooltip.option11": "标签 [语法 - tags:xxxx]", "Base.Header.headersearch.HeaderSearch.tooltip.option12": "API分类 [语法 - api-category:xxxx]", "Base.Header.headersearch.HeaderSearch.tooltip.option2": "提供商 [语法 - provider:xxxx]", "Base.Header.headersearch.HeaderSearch.tooltip.option3": "版本 [语法 - version:xxxx]", "Base.Header.headersearch.HeaderSearch.tooltip.option4": "上下文 [语法 - context:xxxx]", "Base.Header.headersearch.HeaderSearch.tooltip.option5": "状态 [语法 - status:xxxx]", "Base.Header.headersearch.HeaderSearch.tooltip.option6": "描述 [语法 - description:xxxx]", "Base.Header.headersearch.HeaderSearch.tooltip.title": "API和API产品的搜索选项", "Base.Header.navbar.GlobalNavBar.Service.Catalog": "服务", "Base.Header.navbar.GlobalNavBar.Tasks": "任务", "Base.Header.navbar.GlobalNavBar.analytics": "分析", "Base.Header.navbar.GlobalNavBar.api.products": "API产品", "Base.Header.navbar.GlobalNavBar.apis": "API", "Base.Header.navbar.GlobalNavBar.common.policies": "策略", "Base.Header.navbar.GlobalNavBar.global.policies": "全局策略", "Base.Header.navbar.GlobalNavBar.scopes": "作用域", "Base.Header.navbar.GlobalNavBar.title.Policies": "策略", "Base.Header.navbar.GlobalNavBar.title.analytics": "分析", "Base.Header.navbar.GlobalNavBar.title.api.products": "API产品", "Base.Header.navbar.GlobalNavBar.title.apis": "API", "Base.Header.navbar.GlobalNavBar.title.global.Policies": "全局策略", "Base.Header.navbar.GlobalNavBar.title.global.Tasks": "任务", "Base.Header.navbar.GlobalNavBar.title.scopes": "作用域", "Base.Header.navbar.GlobalNavBar.title.services": "服务", "Cancel": "取消", "Cannot.Find.PolicyObj.For.PolicyId": "找不到ID为{policId}的策略", "CommonPolicies.CreatePolicy.breadcrumb.create.new.policy": "创建新策略", "CommonPolicies.CreatePolicy.breadcrumb.policies": "策略", "CommonPolicies.DeletePolicy.confirm.dialog.cancel.delete": "取消", "CommonPolicies.DeletePolicy.confirm.dialog.confirm.content": "确定要删除{policy}策略吗？", "CommonPolicies.DeletePolicy.confirm.dialog.confirm.delete": "确定", "CommonPolicies.DeletePolicy.confirm.dialog.confirm.title": "确认删除", "CommonPolicies.DeletePolicy.policy.delete": "删除", "CommonPolicies.Listing.onboarding.create.new": "开始吧！", "CommonPolicies.Listing.onboarding.policies.tooltip": "策略提供修改API资源行为的能力", "CommonPolicies.Listing.policies.title.add.new.policy": "添加新策略", "CommonPolicies.Listing.policies.title.name": "策略", "CommonPolicies.Listing.policies.title.tooltip": "您可以通过导航到任何所需API的策略选项卡，在操作级别使用这些策略", "CommonPolicies.Listing.policies.title.update.not.allowed": "*由于权限不足，您无权管理策略", "CommonPolicies.Listing.table.header.actions.title": "操作", "CommonPolicies.Listing.table.header.actions.view": "查看", "CommonPolicies.Listing.table.header.applicable.flows": "适用流程", "CommonPolicies.Listing.table.header.description": "描述", "CommonPolicies.Listing.table.header.policy.name": "策略名称", "CommonPolicies.Listing.table.header.policy.version": "策略版本", "CommonPolicies.ViewPolicy.policies.title": "策略", "Confirm.Delete": "确认删除", "Confirm.Delete.Verify": "确定要删除该策略吗？", "Confirm.Deploy": "确认部署", "Confirm.Deploy.Verify": "确定要在选定的网关上部署该策略吗？", "Confirm.UnDeploy": "确认取消部署", "Confirm.Undeploy.Verify": "确定要取消部署该策略吗？", "Connection.Timeout": "连接超时", "CreateAPIWithAI.components.AlertDialog.error.create.API": "创建API时出错", "Custom.Split.Button.Save": "保存", "Custom.Split.Button.Save.And.Deploy": "保存并部署", "Delete": "删除", "Deploy": "部署", "Deploy.Helper": "如果另一个全局策略已部署到网关，则该网关将不可用于部署此策略。请先取消部署先前部署的全局策略。", "Deployed.Gateway.Listing.Table.Header.Description": "描述", "Deployed.Gateway.Listing.Table.Header.Name": "已部署网关", "Deployed.Gateway.Listing.Table.Not.Available": "无已部署网关", "Endpoint.Suspension.State": "端点暂停状态", "Error.Deploy.Policy": "策略部署时发生错误", "Error.Retrieve.Policy": "获取策略时发生错误", "Error.Retrieve.Policy.List": "获取策略列表时发生错误", "Error.Undeploy.Policy": "策略卸载时发生错误", "Error.Validating.Regex": "正则表达式验证错误", "Error.while.validating.the.imported.graphQLSchema": "导入的Schema验证错误", "Fault.Details.Policies.PolicyList.Title": "故障", "Fetching.Policies.Error": "获取策略时出错", "Fetching.Policies.Settings": "获取设置时出错", "Global.Details.Policies.AttachedPolicyCard.apiSpecificPolicy.download.error": "下载策略时发生错误", "Global.Details.Policies.AttachedPolicyCard.commonPolicy.download.error": "下载策略时发生错误", "Global.Details.Policies.AttachedPolicyForm.General.cancel": "取消", "Global.Details.Policies.AttachedPolicyForm.General.description.title": "描述", "Global.Details.Policies.AttachedPolicyForm.General.description.value.not.provided": "哎呀！此策略未提供描述", "Global.Details.Policies.AttachedPolicyForm.General.description.value.provided": "{description}", "Global.Details.Policies.AttachedPolicyForm.General.reset": "重置", "Global.Details.Policies.AttachedPolicyForm.General.save": "保存", "Global.Details.Policies.AttachedPolicyForm.General.saving": "保存中", "Global.Details.Policies.DraggablePolicyCard.policy.view": "查看", "Global.Details.Policies.PolicyConfigurationEditDrawer.title": "配置 {policy}", "Global.Details.Policies.PolicyConfiguringDrawer.title": "配置 {policy}", "Global.Details.Policies.PolicyList.title": "策略列表", "Global.Details.Policies.SaveOperationPolicies.save": "保存", "Global.Details.Policies.SaveOperationPolicies.update": "更新", "Global.Policies": "全局策略", "Global.Policy.Listing.Table.Header.Name": "全局策略", "GlobalPolicies.Listing.Table.Header.Actions.Edit": "编辑", "GlobalPolicies.Listing.Table.Header.Actions.View": "查看", "GlobalPolicies.Listing.onboarding.create.new": "开始吧！", "GlobalPolicies.Listing.onboarding.policies.tooltip": "全局策略允许您将策略映射部署到特定网关中的所有API，而不仅限于单个API。点击下方创建您的第一个全局策略。", "GlobalPolicies.Listing.onboarding.policies.tooltip.not.allowed": "全局策略允许您将策略映射部署到特定网关中的所有API，而不仅限于单个API。请联系有权限的用户创建全局策略。", "GlobalPolicies.Listing.policies.title.add.new.policy": "新增全局策略", "GlobalPolicies.Listing.policies.title.name": "全局策略", "GlobalPolicies.Listing.policies.title.tooltip": "此操作将全局添加策略到网关。如需添加API或操作级别的策略，请导航至相应API的“策略”选项卡。", "GlobalPolicies.Listing.table.header.actions.delete": "删除", "LoginDenied.logout": "退出登录", "LoginDenied.message": "服务器无法验证您是否有权访问请求的资源。", "LoginDenied.title": "错误 403：禁止访问", "Mui.data.table.pagination.display.rows": "共", "Mui.data.table.pagination.display.tool.download.csv": "下载CSV", "Mui.data.table.pagination.display.tool.print": "打印", "Mui.data.table.pagination.display.tool.view.columns": "查看列", "Mui.data.table.pagination.rows.per.page": "每页行数：", "Mui.data.table.search.no.records.found": "抱歉，未找到匹配记录", "Polcies.TextField.Description": "描述", "Polcies.TextField.Name": "名称", "Policy.Delete.Error": "删除策略时出错", "Policy.Delete.Successful": "策略删除成功", "Policy.Deploy.Successful": "策略部署成功", "Policy.Description.Cannot.Be.Empty": "策略描述不能为空", "Policy.Mapping.Added.Successfully": "策略映射添加成功", "Policy.Mapping.Cannot.Be.Empty": "策略映射不能为空", "Policy.Mapping.Update.Error": "更新策略映射时出错", "Policy.Mapping.Update.Success": "策略映射更新成功", "Policy.Name.Cannot.Be.Empty": "策略名称不能为空", "Policy.Undeploy.Successful": "策略卸载成功", "Publisher.Addons.InlineProgress.message": "加载中...", "Request.Details.Policies.PolicyList.Title": "请求", "Response.Details.Policies.PolicyList.Title": "响应", "Scopes.Create.CreateScope.cancel": "取消", "Scopes.Create.CreateScope.create.new.scope": "创建新Scope", "Scopes.Create.CreateScope.heading.scope.heading": "<PERSON><PERSON><PERSON>", "Scopes.Create.CreateScope.label.display.name": "显示名称", "Scopes.Create.CreateScope.label.name": "名称", "Scopes.Create.CreateScope.label.roles": "角色", "Scopes.Create.CreateScope.placeholder.display.name": "Scope显示名称", "Scopes.Create.CreateScope.placeholder.roles": "输入角色并按Enter", "Scopes.Create.CreateScope.placholder.name": "Scope名称", "Scopes.Create.CreateScope.roles.help": "输入有效角色并按`Enter`。", "Scopes.Create.CreateScope.save": "保存", "Scopes.Create.CreateScope.saving": "保存中", "Scopes.Create.CreateScope.scope.added.successfully": "Scope添加成功", "Scopes.Create.CreateScope.short.description.display.name": "输入Scope显示名称（例如：creator）", "Scopes.Create.CreateScope.short.description.name": "输入Scope名称（例如：creator）", "Scopes.Create.Scope.description.length.exceeded": "超过最大长度限制（512字符）", "Scopes.Create.Scope.display.name.empty": "Scope显示名称不能为空", "Scopes.Create.Scope.display.name.length.exceeded": "超过最大长度限制（512字符）", "Scopes.Create.Scope.displayName.is.empty": "Scope显示名称不能为空", "Scopes.Create.Scope.displayName.length.exceeded": "超过最大长度限制（255字符）", "Scopes.Create.Scope.field.has.special.characters": "字段包含特殊字符", "Scopes.Create.Scope.name.already.exist": "Scope名称已存在", "Scopes.Create.Scope.name.already.used": "Scope名称已被其他API使用", "Scopes.Create.Scope.name.empty": "Scope名称不能为空", "Scopes.Create.Scope.name.exists": "Scope名称已存在", "Scopes.Create.Scope.name.has.spaces": "Scope名称不能包含空格", "Scopes.Create.Scope.name.has.special.characters": "字段包含特殊字符", "Scopes.Create.Scope.name.have.spaces": "Scope名称不能包含空格", "Scopes.Create.Scope.name.is.empty": "Scope名称不能为空", "Scopes.Create.Scope.name.length.exceeded": "超过最大长度限制（60字符）", "Scopes.Create.ScopeCreate.Roles.Invalid": "角色无效", "Scopes.Delete.Delete.document.scope.label.ok.confirm": "确认删除Scope {scope} 吗？", "Scopes.Delete.Delete.scope.delete": "删除", "Scopes.Delete.Delete.scope.deleted.successfully": "API Scope删除成功！", "Scopes.Delete.Delete.scope.listing.delete.confirm": "确认删除", "Scopes.Delete.Delete.scope.listing.label.cancel": "取消", "Scopes.Delete.Delete.scope.listing.label.ok.yes": "是", "Scopes.Delete.Delete.something.went.wrong.while.updating.the.api": "删除Scope时出错", "Scopes.EditScope.Roles.Invalid": "角色无效", "Scopes.EditScope.cancel": "取消", "Scopes.EditScope.heading.scope.heading": "<PERSON><PERSON><PERSON>", "Scopes.EditScope.roles.help": "输入有效角色并按`Enter`。", "Scopes.EditScope.scope.updated.successfully": "Scope更新成功", "Scopes.EditScope.short.description.about.the.scope": "Scope的简短描述", "Scopes.EditScope.short.description.display.name": "输入Scope显示名称（例如：creator）", "Scopes.EditScope.update": "更新", "Scopes.EditScope.update.scope": "更新Scope", "Scopes.Listing.Listing.heading.scope.add_new": "新增Scope", "Scopes.Listing.Listing.heading.scope.heading": "<PERSON><PERSON><PERSON>", "Scopes.Listing.Listing.scopes.enable.fine.gained.access.control": "Scopes支持基于用户角色的细粒度API资源访问控制。", "Scopes.Listing.Listing.scopes.onboarding.menu.card.name": "<PERSON><PERSON><PERSON>", "Scopes.Listing.Listing.scopes.text.editor.edit": "编辑", "Scopes.Listing.Listing.table.header.actions": "操作", "Scopes.Listing.Listing.table.header.description": "描述", "Scopes.Listing.Listing.table.header.display.name": "显示名称", "Scopes.Listing.Listing.table.header.name": "名称", "Scopes.Listing.Listing.table.header.number.of.usages": "使用次数", "Scopes.Listing.Listing.table.header.roles": "角色", "Scopes.Listing.Listing.update.not.allowed": "*由于权限不足，您无权更新API的Scopes", "Scopes.Usage.Usage.api.context": "上下文：", "Scopes.Usage.Usage.api.name": "API名称：", "Scopes.Usage.Usage.api.provider": "提供者：", "Scopes.Usage.Usage.api.version": "版本：", "Scopes.Usage.Usage.scope.usage": "使用情况", "Scopes.Usage.Usage.usage": "{scope}的使用情况", "Scopes.Usage.Usage.usage.cancel": "取消", "Scopes.Usage.UsageView.resource.revision": "修订版本", "Scopes.Usage.UsageView.resource.target": "目标", "Scopes.Usage.UsageView.resource.usage": "资源列表", "Scopes.Usage.UsageView.resource.verb": "请求方法", "Scopes.Usage.UsageViewAPI.api.usage": "API列表", "Select.Gateways.Label": "选择要部署的网关", "Select.Gateways.Placeholder": "选择要部署的网关", "ServiceCatalog.CreateApi.api.actual.context.helper": "API将在网关的 {actualContext} 上下文中暴露", "ServiceCatalog.CreateApi.api.context.label": "上下文", "ServiceCatalog.CreateApi.api.created.successfully": "从服务成功创建API！", "ServiceCatalog.CreateApi.api.name.label": "名称", "ServiceCatalog.CreateApi.api.version.label": "版本", "ServiceCatalog.CreateApi.cancel.btn": "取消", "ServiceCatalog.CreateApi.create.api": "创建API", "ServiceCatalog.CreateApi.create.api.dialog.helper": "从服务 {serviceName} 创建API", "ServiceCatalog.CreateApi.create.api.dialog.title": "创建API", "ServiceCatalog.CreateApi.error.create.api": "从服务创建API时出错", "ServiceCatalog.CreateApi.mandatory.field.label": "必填字段", "ServiceCatalog.CreateApi.select.protocol": "选择协议", "ServiceCatalog.CreateApi.update.btn": "创建API", "ServiceCatalog.CreateApi.update.btn.in.progress": "创建API中...", "ServiceCatalog.CreateSoapPassthroughApi.create.api.dialog.helper": "从服务 {serviceName} 创建SOAP透传API", "ServiceCatalog.Listing.Delete.cancel": "取消", "ServiceCatalog.Listing.Delete.confirm": "确认删除", "ServiceCatalog.Listing.Delete.ok.confirm": "确认删除服务 {service} 吗？", "ServiceCatalog.Listing.Delete.ok.yes": "是", "ServiceCatalog.Listing.Listing.error.delete": "删除服务时出错", "ServiceCatalog.Listing.Listing.error.loading": "加载服务时出错", "ServiceCatalog.Listing.Listing.heading": "服务", "ServiceCatalog.Listing.Listing.heading.displaying.service": "服务", "ServiceCatalog.Listing.Listing.heading.displaying.services": "服务", "ServiceCatalog.Listing.Listing.heading.displaying.total": "总计：", "ServiceCatalog.Listing.Listing.service.deleted.successfully": "服务删除成功！", "ServiceCatalog.Listing.Onboarding.add.sample.success": "示例服务添加成功！", "ServiceCatalog.Listing.Onboarding.error.creating.sample.service": "创建示例服务时出错", "ServiceCatalog.Listing.Onboarding.learn.heading": "学习编写您的第一个", "ServiceCatalog.Listing.Onboarding.learn.heading.sub": "集成服务", "ServiceCatalog.Listing.Onboarding.learn.heading.text": "创建并部署您的第一个集成服务", "ServiceCatalog.Listing.Onboarding.learn.link": "开始使用", "ServiceCatalog.Listing.Onboarding.sample.add": "添加示例服务", "ServiceCatalog.Listing.Onboarding.sample.heading": "添加示例", "ServiceCatalog.Listing.Onboarding.sample.heading.sub": "集成服务", "ServiceCatalog.Listing.Onboarding.sample.heading.text": "一键部署WSO2 API Manager提供的示例集成服务", "ServiceCatalog.Listing.Overview.back.btn": "返回", "ServiceCatalog.Listing.Overview.close.btn": "关闭", "ServiceCatalog.Listing.Overview.created.time": "创建时间", "ServiceCatalog.Listing.Overview.definition.download": "服务定义", "ServiceCatalog.Listing.Overview.definition.type": "Schema类型", "ServiceCatalog.Listing.Overview.display.name": "{serviceDisplayName}", "ServiceCatalog.Listing.Overview.download.service": "下载", "ServiceCatalog.Listing.Overview.download.service.error": "下载服务定义时出错。", "ServiceCatalog.Listing.Overview.error.loading.service": "加载服务时出错", "ServiceCatalog.Listing.Overview.mutual.ssl": "双向SSL", "ServiceCatalog.Listing.Overview.mutual.ssl.disabled": "禁用", "ServiceCatalog.Listing.Overview.mutual.ssl.enabled": "启用", "ServiceCatalog.Listing.Overview.parent.breadcrumb": "服务目录", "ServiceCatalog.Listing.Overview.readonly.breadcrumb": "概览", "ServiceCatalog.Listing.Overview.retrieve.service.def.error": "获取服务定义时出错。", "ServiceCatalog.Listing.Overview.security.type": "安全类型", "ServiceCatalog.Listing.Overview.service.description": "{description}", "ServiceCatalog.Listing.Overview.service.type.async.tooltip": "异步API服务", "ServiceCatalog.Listing.Overview.service.type.graphql.tooltip": "GraphQL服务", "ServiceCatalog.Listing.Overview.service.type.rest.tooltip": "REST服务", "ServiceCatalog.Listing.Overview.service.type.soap.tooltip": "SOAP服务", "ServiceCatalog.Listing.Overview.service.url": "服务URL", "ServiceCatalog.Listing.Overview.service.version": "{serviceVersion}", "ServiceCatalog.Listing.Overview.view.definition": "查看定义", "ServiceCatalog.Listing.Usages.api.context": "上下文", "ServiceCatalog.Listing.Usages.api.name": "API名称", "ServiceCatalog.Listing.Usages.api.version": "版本", "ServiceCatalog.Listing.Usages.cancel": "取消", "ServiceCatalog.Listing.Usages.service.usage": "被 {usage} 个API使用", "ServiceCatalog.Listing.Usages.usage": "{serviceName} 的使用情况", "ServiceCatalog.Listing.components.ServiceCard.create.api": "创建API", "ServiceCatalog.Listing.components.ServiceCard.type": "类型", "ServiceCatalog.Listing.components.ServiceCard.version": "版本", "ServiceCatalog.ServicesTableView.ServicesTableView.created.time": "创建时间", "ServiceCatalog.ServicesTableView.ServicesTableView.name": "服务", "ServiceCatalog.ServicesTableView.ServicesTableView.schema.type": "架构类型", "ServiceCatalog.ServicesTableView.ServicesTableView.service.url": "服务URL", "ServiceCatalog.ServicesTableView.ServicesTableView.usage": "使用次数", "ServiceCatalog.ServicesTableView.ServicesTableView.version": "版本", "SubscriptionApproval.Addons.ListBase.reload": "重新加载", "SubscriptionAproval.Addons.ListBase.nodata.message": "暂无数据", "Task.SubscriptionCreation.table.button.reject": "拒绝", "Task.SubscriptionUpdate.table.button.reject": "拒绝", "Task.title": "任务", "Throttling.Advanced.AddEdit.form.actions.label": "操作", "Undeploy": "取消部署", "UnexpectedError.logout": "退出登录", "UnexpectedError.message": "无效请求导致错误", "UnexpectedError.title": "服务器内部错误", "Uploading.Policies.Error": "文件类型不兼容", "Workflow.ApplicationCreation.updateStatus.has.errors": "无法完成订阅创建的批准/拒绝流程。", "Workflow.SubscriptionCreation.List.empty.content.subscriptioncreations": "暂无待处理的订阅创建工作流请求。", "Workflow.SubscriptionCreation.List.empty.title.subscriptioncreations": "订阅创建", "Workflow.SubscriptionCreation.ListBase.nodata.message": "暂无数据", "Workflow.SubscriptionCreation.Reject.Title": "拒绝", "Workflow.SubscriptionCreation.Reject.button.cancel": "取消", "Workflow.SubscriptionCreation.Reject.button.delete": "拒绝", "Workflow.SubscriptionCreation.Reject.text.description": "确定要拒绝此订阅吗？", "Workflow.SubscriptionCreation.apicall.has.errors": "无法获取订阅创建的工作流待处理请求", "Workflow.SubscriptionCreation.help.link.one": "创建订阅请求", "Workflow.SubscriptionCreation.permission.denied.content": "您没有足够权限查看订阅创建审批任务。请联系站点管理员。", "Workflow.SubscriptionCreation.permission.denied.title": "权限不足", "Workflow.SubscriptionCreation.search.default": "按API、应用或订阅者搜索", "Workflow.SubscriptionCreation.table.button.approve": "批准", "Workflow.SubscriptionCreation.table.header.API": "API", "Workflow.SubscriptionCreation.table.header.Action": "操作", "Workflow.SubscriptionCreation.table.header.Application": "应用", "Workflow.SubscriptionCreation.table.header.Description": "描述", "Workflow.SubscriptionCreation.table.header.Subscriber": "订阅者", "Workflow.SubscriptionCreation.table.header.Tier": "层级转换", "Workflow.SubscriptionCreation.title.subscriptioncreation": "订阅创建 - 审批任务", "Workflow.SubscriptionCreation.title.subscriptionupdate": "订阅更新 - 审批任务", "Workflow.SubscriptionCreation.update.success": "工作流状态更新成功", "Workflow.SubscriptionUpdate.List.empty.content.subscriptionupdate": "暂无待处理的订阅更新工作流请求。", "Workflow.SubscriptionUpdate.List.empty.title.subscriptionupdate": "订阅更新", "Workflow.SubscriptionUpdate.Reject.Title": "拒绝", "Workflow.SubscriptionUpdate.Reject.button.cancel": "取消", "Workflow.SubscriptionUpdate.Reject.button.delete": "拒绝", "Workflow.SubscriptionUpdate.Reject.text.description": "确定要拒绝此订阅更新吗？", "Workflow.SubscriptionUpdate.apicall.has.errors": "无法获取订阅更新的工作流待处理请求", "Workflow.SubscriptionUpdate.help.link.one": "创建订阅更新请求", "Workflow.SubscriptionUpdate.permission.denied.content": "您没有足够权限查看订阅更新审批任务。请联系站点管理员。", "Workflow.SubscriptionUpdate.permission.denied.title": "权限不足", "Workflow.SubscriptionUpdate.table.button.approve": "批准", "Workflow.subscriptionupdate.updateStatus.has.errors": "无法完成订阅更新的批准/拒绝流程。", "api.console.security.heading": "安全", "app.components.Shared.Banner.back": "返回", "block.all": "全部拦截", "block.production.only": "仅拦截生产环境", "by": "由", "cancel": "取消", "colon": "：", "documents.markdown.editor.default": "#输入您的Markdown内容", "error.list.401": "401：需要授权", "error.list.401.description": "服务器无法验证您是否有权访问请求的资源", "error.list.403": "403：禁止访问", "error.list.403.description": "您没有权限以此类请求访问内容", "error.list.404": "404：页面不存在", "error.list.404.description": "您查找的页面可能已被删除、更名或暂时不可用", "error.list.500": "500：页面无法显示", "error.list.500.description": "服务器遇到内部错误或配置问题，无法完成请求", "globalPolicies.create.create.heading": "创建新全局策略", "globalPolicies.create.edit.heading": "编辑全局策略", "globalPolicies.heading": "全局策略", "save": "保存", "the.icon.is.not.modified": "图标未修改", "unblock": "解除拦截", "upload.image": "点击或拖拽图片上传", "upload.image.size.error": "文件过大，大小限制为1MB", "upload.image.size.info": "文件大小限制为1MB"}