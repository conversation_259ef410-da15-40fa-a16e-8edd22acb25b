{"Admin.Addons.Help.Base.title": "Help", "Admin.AiVendor.label.apiVersion": "API Version", "Admin.GatewayEnvironment.form.type": "Gateway Environment Type", "Admin.KeyManager.form.type": "Key Manager Type", "Admin.KeyManager.label.ConsumerKey.Claim": "Consumer Key Claim URI", "Admin.KeyManager.label.DisplayName": "Display Name", "Admin.KeyManager.label.Enable.EnableOAithAppCreation": "Oauth App Creation", "Admin.KeyManager.label.Enable.OutOfBandProvisioning": "Out Of Band Provisioning", "Admin.KeyManager.label.Enable.TokenGen": "Token Generation", "Admin.KeyManager.label.Scopes.Claim": "<PERSON>opes <PERSON><PERSON><PERSON>", "Admin.KeyManager.label.allow.exchange.token": "Token Exchange", "Admin.KeyManager.label.message.allow.direct.token": "Direct Token", "Admin.KeyManager.label.token.audience": "Allowed Token Audience", "Admin.KeyManager.permission.type": "Key Manager Permission", "Admin.Throttling.Advanced.Throttling.policy.table.header.name": "Name", "Admin.Throttling.Advanced.Throttling.policy.table.header.quota": "<PERSON><PERSON><PERSON>", "Admin.Throttling.Advanced.Throttling.policy.table.header.quota.policy": "Quota Policy", "Admin.Throttling.Advanced.Throttling.policy.table.header.unit.time": "Unit Time", "Admin.Throttling.Application.AddEdit.burst.control.add.description": "Define Burst Control Limits (optional)", "Admin.Throttling.Application.Throttling.Policy.add.burst.limits.details": "Burst Control (Rate Limiting)", "Admin.Throttling.Application.Throttling.Policy.add.data.amount.helper.text": "Bandwidth allowed", "Admin.Throttling.Application.Throttling.Policy.add.description.helper.text": "Description of the throttle policy", "Admin.Throttling.Application.Throttling.Policy.add.general.details": "General Details", "Admin.Throttling.Application.Throttling.Policy.add.name.helper.text": "Name of the throttle policy", "Admin.Throttling.Application.Throttling.Policy.add.quota.limits.details": "<PERSON><PERSON><PERSON>", "Admin.Throttling.Application.Throttling.Policy.add.request.count.helper.text": "Number of requests allowed", "Admin.Throttling.Application.Throttling.Policy.add.save.btn": "Save", "Admin.Throttling.Application.Throttling.Policy.add.time.days": "Day(s)", "Admin.Throttling.Application.Throttling.Policy.add.time.helper.text": "Time configuration", "Admin.Throttling.Application.Throttling.Policy.add.time.hours": "Hour(s)", "Admin.Throttling.Application.Throttling.Policy.add.time.minutes": "Minute(s)", "Admin.Throttling.Application.Throttling.Policy.add.time.months": "Month(s)", "Admin.Throttling.Application.Throttling.Policy.add.time.weeks": "Week(s)", "Admin.Throttling.Application.Throttling.Policy.add.time.years": "Year(s)", "Admin.Throttling.Application.Throttling.Policy.form.description": "Description", "Admin.Throttling.Application.Throttling.Policy.form.policyName": "Name", "Admin.Throttling.Application.Throttling.Policy.option.data.bandwidth.label": "Data Bandwith", "Admin.Throttling.Application.Throttling.Policy.option.request.bandwidth.label": "Request Bandwidth", "Admin.Throttling.Application.Throttling.Policy.option.request.count.label": "Request Count", "Admin.Throttling.Application.Throttling.Policy.option.request.unit.time.label": "Unit Time", "Admin.Throttling.Application.Throttling.policy.table.header.name": "Name", "Admin.Throttling.Application.Throttling.policy.table.header.quota": "<PERSON><PERSON><PERSON>", "Admin.Throttling.Application.Throttling.policy.table.header.unit.time": "Unit Time", "Admin.Throttling.Blacklist.Throttling.Policy.add.condition.type": "Condition Type", "Admin.Throttling.Blacklist.Throttling.Policy.add.condition.type.User": "User", "Admin.Throttling.Blacklist.Throttling.Policy.add.condition.type.api.context": "API Context", "Admin.Throttling.Blacklist.Throttling.Policy.add.condition.type.api.context.format": "Format : ${context}", "Admin.Throttling.Blacklist.Throttling.Policy.add.condition.type.application": "Application", "Admin.Throttling.Blacklist.Throttling.Policy.add.condition.type.application.format": "Format : ${userName}:${applicationName}", "Admin.Throttling.Blacklist.Throttling.Policy.add.condition.type.enable.condition": "Enable Condition", "Admin.Throttling.Blacklist.Throttling.Policy.add.condition.type.ip.address": "IP Address", "Admin.Throttling.Blacklist.Throttling.Policy.add.condition.type.ip.format": "Format : ${ip}", "Admin.Throttling.Blacklist.Throttling.Policy.add.condition.type.ip.range": "IP Range", "Admin.Throttling.Blacklist.Throttling.Policy.add.condition.type.user.format": "Format : ${userName}", "Admin.Throttling.Blacklist.Throttling.Policy.add.condition.type.value.ip.end.address": "End IP Address", "Admin.Throttling.Blacklist.Throttling.Policy.add.condition.type.value.ip.start.address": "Start IP Address", "Admin.Throttling.Blacklist.Throttling.Policy.add.condition.type.value.label": "Value", "Admin.Throttling.Blacklist.Throttling.Policy.add.dialog.btn.deny": "<PERSON><PERSON>", "Admin.Throttling.Blacklist.Throttling.Policy.example.abbr": "Eg", "Admin.Throttling.Blacklist.Throttling.policy.table.header.condition.status": "Condition Status", "Admin.Throttling.Blacklist.Throttling.policy.table.header.condition.type": "Condition Type", "Admin.Throttling.Blacklist.Throttling.policy.table.header.conditional.value": "Conditional Value", "Admin.Throttling.Blacklist.policy.add.invert.condition": "Invert Condition:", "Admin.Throttling.Blacklist.policy.enable.condition": "Enable Condition", "Admin.Throttling.Custom.Throttling.policy.add.field.description": "Description", "Admin.Throttling.Custom.Throttling.policy.add.field.key.template": "Key Template", "Admin.Throttling.Custom.Throttling.policy.add.field.name": "Name", "Admin.Throttling.Custom.Throttling.policy.table.header.description": "Description", "Admin.Throttling.Custom.Throttling.policy.table.header.key.template": "Key Template", "Admin.Throttling.Custom.Throttling.policy.table.header.name": "Name", "Admin.Throttling.Custom.policy.add.key.template.helper.text": "Eg: $userId:$apiContext:$apiVersion", "Admin.Throttling.Custom.policy.add.policy.description": "Description of the throttle policy", "Admin.Throttling.Custom.policy.add.policy.name": "Name of the throttle policy", "Admin.Throttling.Custom.policy.add.siddhi.query": "Siddhi Query:", "Admin.Throttling.Custom.policy.add.siddhi.query.description": "The following sample query will allow 5 requests per minute for an Admin user.", "Admin.Throttling.Custom.policy.add.siddhi.query.key.template": "Key Template : $userId", "Admin.Throttling.Subscription.Throttling.Policy.add.completion.token.count.helper.text": "Number of completion tokens allowed", "Admin.Throttling.Subscription.Throttling.Policy.add.prompt.token.count.helper.text": "Number of prompt tokens allowed", "Admin.Throttling.Subscription.Throttling.Policy.add.request.count.helper.text": "Number of requests allowed", "Admin.Throttling.Subscription.Throttling.Policy.add.time.days": "Day(s)", "Admin.Throttling.Subscription.Throttling.Policy.add.time.hours": "Hour(s)", "Admin.Throttling.Subscription.Throttling.Policy.add.time.minutes": "Minute(s)", "Admin.Throttling.Subscription.Throttling.Policy.add.time.months": "Month(s)", "Admin.Throttling.Subscription.Throttling.Policy.add.time.years": "Year(s)", "Admin.Throttling.Subscription.Throttling.Policy.add.total.token.count.helper.text": "Number of total tokens allowed", "Admin.Throttling.Subscription.Throttling.policy.table.header.completion.token.count": "Completion Token Count", "Admin.Throttling.Subscription.Throttling.policy.table.header.name": "Name", "Admin.Throttling.Subscription.Throttling.policy.table.header.prompt.token.count": "Prompt Token Count", "Admin.Throttling.Subscription.Throttling.policy.table.header.quota": "<PERSON><PERSON><PERSON>", "Admin.Throttling.Subscription.Throttling.policy.table.header.quota.policy": "Quota Policy", "Admin.Throttling.Subscription.Throttling.policy.table.header.rate.limit": "Rate Limit", "Admin.Throttling.Subscription.Throttling.policy.table.header.time.unit": "Time Unit", "Admin.Throttling.Subscription.Throttling.policy.table.header.total.token.count": "Total Token Count", "Admin.Throttling.Subscription.Throttling.policy.table.header.unit.time": "Unit Time", "Admin.components.form.delete.btn": "Delete", "AdminPages.Addons.InlineProgress.message": "Loading...", "AdminPages.Addons.ListBase.noDataError": "Error while retrieving data.", "AdminPages.Addons.ListBase.nodata.message": "No items yet", "AdminPages.Addons.ListBase.reload": "Reload", "AdminPages.Addons.ListBaseWithPagination.noDataError": "Error while retrieving data.", "AdminPages.Addons.ListBaseWithPagination.nodata.message": "No items yet", "AdminPages.Addons.ListBaseWithPagination.reload": "Reload", "AdminPages.AiVendor.Delete.form.delete.confirmation.message": "Are you sure you want to delete this AI/LLM Vendor ?", "AdminPages.AiVendor.Delete.form.delete.dialog.btn": "Delete", "AdminPages.AiVendor.Delete.form.delete.dialog.title": "Delete AI/LLM Vendor ?", "AdminPages.AiVendor.Delete.form.delete.successful": "AI/LLM Vendor deleted successfully", "AdminPages.AiVendors.List.empty.content.Aivendors": "It is possible to register an AI/LLM Vendor.", "AdminPages.ApiCategories.AddEdit.form.add.successful": "API Category added successfully", "AdminPages.ApiCategories.AddEdit.form.description": "Description", "AdminPages.ApiCategories.AddEdit.form.description.helper.text": "Description of the API category", "AdminPages.ApiCategories.AddEdit.form.edit.successful": "API Category edited successfully", "AdminPages.ApiCategories.AddEdit.form.error.description.too.long": "API Category description is too long", "AdminPages.ApiCategories.AddEdit.form.error.name.empty": "Name is Empty", "AdminPages.ApiCategories.AddEdit.form.error.name.has.spaces": "Name contains spaces", "AdminPages.ApiCategories.AddEdit.form.error.name.has.special.chars": "Name field contains special characters", "AdminPages.ApiCategories.AddEdit.form.error.name.too.long": "API Category name is too long", "AdminPages.ApiCategories.AddEdit.form.name": "Name", "AdminPages.ApiCategories.AddEdit.form.name.helper.text": "Name of the API category", "AdminPages.ApiCategories.AddEdit.form.save.btn": "Save", "AdminPages.ApiCategories.Delete.form.delete.btn": "Delete", "AdminPages.ApiCategories.Delete.form.delete.content": "Are you sure you want to delete this API Category?", "AdminPages.ApiCategories.Delete.form.delete.successful": "API Category deleted successfully", "AdminPages.ApiCategories.Delete.form.delete.title": "Delete API category?", "AdminPages.ApiCategories.List.addButtonProps.title": "Add API Category", "AdminPages.ApiCategories.List.addButtonProps.triggerButtonText": "Add API Category", "AdminPages.ApiCategories.List.empty.content.apicategories": "You can use API categories to group APIs. In previous versions of WSO2 API Manager, the process of grouping APIs was carried out by using tag-wise groups. Unlike tag-wise grouping, API categories do not use a naming convention. Therefore, the admin does not need to take into consideration any naming conventions when using API category-based grouping.", "AdminPages.ApiCategories.List.empty.title.apicategories": "API Categories", "AdminPages.ApiCategories.List.help.link.one": "API Category based Grouping", "AdminPages.ApiCategories.List.search.default": "Search by API Category name", "AdminPages.ApiCategories.List.title.apicategories": "API Categories", "AdminPages.ApiCategories.table.header.category.description": "Description", "AdminPages.ApiCategories.table.header.category.name": "Category Name", "AdminPages.ApiCategories.table.header.category.number.of.apis": "Number of APIs", "AdminPages.ApiSettings.EditApi.form.edit.error": "API provider should not be empty.", "AdminPages.ApiSettings.EditApi.form.edit.other.error": "Given Username is not valid.", "AdminPages.ApiSettings.EditApi.form.edit.provider.label": "Provider Name", "AdminPages.ApiSettings.EditApi.form.edit.successful": "API provider changed successfully", "AdminPages.ApiSettings.EditApi.form.edit.user.notvalid": "Error while updating the provider name.", "AdminPages.ApplicationSettings.Edit.form.edit.successful": "Application owner changed successfully", "AdminPages.ApplicationSettings.Edit.form.helperText": "Enter a new Owner. Make sure the new owner has logged into the Developer Portal at least once", "AdminPages.ApplicationSettings.Edit.form.name": "Application Name", "AdminPages.Gateways.Delete.form.delete.confirmation.delete.btn": "Delete", "AdminPages.Gateways.Delete.form.delete.confirmation.message": "Are you sure you want to delete this Gateway Environment?", "AdminPages.Gateways.Delete.form.delete.confirmation.message.title": "Delete Gateway Environment?", "AdminPages.Gateways.Delete.form.delete.successful": "Gateway Environment deleted successfully", "AdminPages.Gateways.List.addButtonProps.title": "Add Gateway Environment", "AdminPages.Gateways.List.addButtonProps.triggerButtonText": "Add Gateway Environment", "AdminPages.Gateways.List.empty.content.Gateways": "It is possible to create a Gateway environment with virtual hosts to access APIs. API revisions can be attached with Vhost to access it.", "AdminPages.Gateways.List.empty.title": "Gateway Environments", "AdminPages.Gateways.List.search.default": "Search Gateway by Name, Description or Host", "AdminPages.Gateways.List.title": "Gateway Environments", "AdminPages.Gateways.table.header.description": "Description", "AdminPages.Gateways.table.header.displayName": "Name", "AdminPages.Gateways.table.header.gatewayType": "Gateway Type", "AdminPages.Gateways.table.header.permission": "Visibility Permission", "AdminPages.Gateways.table.header.type": "Type", "AdminPages.Gateways.table.header.vhosts": "Virtual Host(s)", "AdminPages.Governance.Policy.Delete.form.delete.confirmation.message": "Are you sure you want to delete this Policy?", "AdminPages.Governance.Policy.Delete.form.delete.dialog.btn": "Delete", "AdminPages.Governance.Policy.Delete.form.delete.dialog.title": "Delete Policy?", "AdminPages.Governance.Policy.Delete.form.delete.error": "Something went wrong while deleting the Policy", "AdminPages.Governance.Policy.Delete.form.delete.successful": "Policy deleted successfully", "AdminPages.Governance.Ruleset.Delete.form.delete.confirmation.message": "Are you sure you want to delete this Ruleset?", "AdminPages.Governance.Ruleset.Delete.form.delete.dialog.btn": "Delete", "AdminPages.Governance.Ruleset.Delete.form.delete.dialog.title": "Delete Ruleset?", "AdminPages.Governance.Ruleset.Delete.form.delete.error": "Something went wrong while deleting the Ruleset", "AdminPages.Governance.Ruleset.Delete.form.delete.successful": "Ruleset deleted successfully", "AdminPages.KeyManager.Delete.form.delete.confirmation.message": "Are you sure you want to delete this KeyManager ?", "AdminPages.KeyManagers.Delete.form.delete.dialog.btn": "Delete", "AdminPages.KeyManagers.Delete.form.delete.dialog.title": "Delete KeyManager ?", "AdminPages.KeyManagers.Delete.form.delete.successful": "KeyManager deleted successfully", "AdminPages.KeyManagers.List.empty.content.keymanagers": "It is possible to register an OAuth Provider.", "AdminPages.KeyManagers.Usages.dialog.close.btn": "Close", "AdminPages.KeyManagers.Usages.dialog.title": "Key Manager Usages -", "AdminPages.Labels.AddEdit.form.add.successful": "Label added successfully", "AdminPages.Labels.AddEdit.form.description": "Description", "AdminPages.Labels.AddEdit.form.description.helper.text": "Description of the Label", "AdminPages.Labels.AddEdit.form.edit.successful": "Label edited successfully", "AdminPages.Labels.AddEdit.form.error.description.too.long": "Label description is too long", "AdminPages.Labels.AddEdit.form.error.name.empty": "Name is Empty", "AdminPages.Labels.AddEdit.form.error.name.has.spaces": "Name contains spaces", "AdminPages.Labels.AddEdit.form.error.name.has.special.chars": "Name field contains special characters", "AdminPages.Labels.AddEdit.form.error.name.too.long": "Label name is too long", "AdminPages.Labels.AddEdit.form.name": "Name", "AdminPages.Labels.AddEdit.form.name.helper.text": "Name of the Label", "AdminPages.Labels.AddEdit.form.save.btn": "Save", "AdminPages.Labels.Delete.form.delete.btn": "Delete", "AdminPages.Labels.Delete.form.delete.content": "Are you sure you want to delete this Label?", "AdminPages.Labels.Delete.form.delete.successful": "Label deleted successfully", "AdminPages.Labels.Delete.form.delete.title": "Delete Label?", "AdminPages.Labels.List.addButtonProps.title": "Add Label", "AdminPages.Labels.List.addButtonProps.triggerButtonText": "Add Label", "AdminPages.Labels.List.empty.content.labels": "Labels help you organize and group your artifacts, such as APIs, in a simple and flexible way. You can define labels to tag your artifacts based on usecases, categories, domains, or any criteria you choose.", "AdminPages.Labels.List.empty.title.labels": "Labels", "AdminPages.Labels.List.search.default": "Search by Label name", "AdminPages.Labels.List.title.labels": "Labels", "AdminPages.Labels.Usages.dialog.close.btn": "Close", "AdminPages.Labels.Usages.dialog.title": "Labels Usages -", "AdminPages.Labels.table.header.label.description": "Description", "AdminPages.Labels.table.header.label.name": "Label Name", "AdminPages.Labels.table.header.label.usage": "Usage", "AdminPages.Organization.AddEdit.form.error.description.too.long": "Organization description is too long", "AdminPages.Organizations.AddEdit.form.add.successful": "Organizations added successfully", "AdminPages.Organizations.AddEdit.form.description": "Description", "AdminPages.Organizations.AddEdit.form.edit.successful": "Organizations edited successfully", "AdminPages.Organizations.AddEdit.form.error.name.empty": "Name is Empty", "AdminPages.Organizations.AddEdit.form.error.referenceId.empty": "Reference ID is Empty", "AdminPages.Organizations.AddEdit.form.error.referenceId.has.spaces": "Reference ID contains spaces", "AdminPages.Organizations.AddEdit.form.id": "Reference ID", "AdminPages.Organizations.AddEdit.form.name": "Name", "AdminPages.Organizations.AddEdit.form.save.btn": "Save", "AdminPages.Organizations.Delete.form.delete.btn": "Delete", "AdminPages.Organizations.Delete.form.delete.content": "Are you sure you want to delete this Organization?", "AdminPages.Organizations.Delete.form.delete.successful": "Organization deleted successfully", "AdminPages.Organizations.Delete.form.delete.title": "Delete Organization?", "AdminPages.Organizations.List.addButtonProps.title": "Register Organization", "AdminPages.Organizations.List.addButtonProps.triggerButtonText": "Register Organization", "AdminPages.Organizations.List.empty.content.organization": "Manage your organizations by registering new organizations or updating existing entries.", "AdminPages.Organizations.List.empty.content.organization.no.orguser": "Users who belong to an organization can manage their organizations by registering new organizations or updating existing entries.", "AdminPages.Organizations.List.empty.title.organization": "Organizations", "AdminPages.Organizations.List.search.default": "Search by Organization Name", "AdminPages.Organizations.List.title.organizations": "Organizations", "AdminPages.Organizations.table.header.organization.description": "Description", "AdminPages.Organizations.table.header.organization.name": "Organization Name", "AdminPagesGatewayEnvironments.AddEditGWEnvironment.form.environment.displayName.empty": "Display Name is Empty", "AdminPagesGatewayEnvironments.AddEditGWEnvironment.form.environment.vhost.duplicate": "VHosts are duplicated", "AdminPagesGatewayEnvironments.AddEditGWEnvironment.form.environment.vhost.empty": "VHost is empty", "AiVendor.add.success.msg": "- AI/LLM Vendor added successfully.", "AiVendor.edit.success": "- <PERSON>/<PERSON><PERSON> edited successfully.", "AiVendors.AddEditAiVendor.AiVendor.configurations.llm": "LLM Configurations", "AiVendors.AddEditAiVendor.AiVendor.configurations.llm.auth": "LLM Provider Auth Configurations", "AiVendors.AddEditAiVendor.AiVendor.general.details.description.llm": "Configure to extract LLM related metadata", "AiVendors.AddEditAiVendor.AiVendor.general.details.description.llm.auth": "Configure to add LLM provider authorization", "AiVendors.AddEditAiVendor.apiDefinition": "API Definition", "AiVendors.AddEditAiVendor.apiDefinition.description": "Upload API Definition of the AI/LLM Vendor", "AiVendors.AddEditAiVendor.apiDefinition.upload": "Upload API Definition", "AiVendors.AddEditAiVendor.connectorType": "Connector Type for AI/LLM Vendor", "AiVendors.AddEditAiVendor.connectorType.description": "Reference to the connector model for the AI/LLM vendor", "AiVendors.AddEditAiVendor.form.add": "Add", "AiVendors.AddEditAiVendor.form.cancel": "Cancel", "AiVendors.AddEditAiVendor.form.connectorType": "Connector Type", "AiVendors.AddEditAiVendor.form.connectorType.help": "Connector Type for AI/LLM Vendor", "AiVendors.AddEditAiVendor.form.description": "Description", "AiVendors.AddEditAiVendor.form.description.help": "Description of the AI/LLM Vendor.", "AiVendors.AddEditAiVendor.form.displayName.help": "API Version of the AI/LLM Vendor.", "AiVendors.AddEditAiVendor.form.has.errors": "One or more fields contain errors.", "AiVendors.AddEditAiVendor.form.name": "Name", "AiVendors.AddEditAiVendor.form.name.help": "Name of the AI/LLM Vendor.", "AiVendors.AddEditAiVendor.form.update.btn": "Update", "AiVendors.AddEditAiVendor.general.details": "General Details", "AiVendors.AddEditAiVendor.general.details.description": "Provide name and description of the AI/LLM Vendor", "AiVendors.AddEditAiVendor.is.empty.error": "is empty", "AiVendors.AddEditAiVendor.is.empty.error.apiVersion": "Required field is empty.", "AiVendors.AddEditAiVendor.is.empty.error.attributeIdentifier": "Attribute identifier is required.", "AiVendors.AddEditAiVendor.is.empty.error.connectorType": "Connector type is required.", "AiVendors.AddEditAiVendor.is.empty.error.inputSource": "Input source is required.", "AiVendors.AddEditAiVendor.modelList": "Model List", "AiVendors.AddEditAiVendor.modelList.description": "AI/LLM Vendor supported model list", "AiVendors.AddEditAiVendor.modelList.help": "Type available models and press enter/return to add them.", "AiVendors.AddEditAiVendor.modelList.placeholder": "Type Model name and press Enter", "AiVendors.AddEditAiVendor.title.edit": "AI/LLM Vendor - Edit", "AiVendors.AddEditAiVendor.title.new": "AI/LLM Vendor - Create new", "AiVendors.AiAPIDefinition.browse.files.to.upload": "Browse File to Upload", "AiVendors.AiAPIDefinition.drag.and.drop.message": "Drag and Drop files here {break} or {break}", "AiVendors.ListAiVendors.List.title": "AI/LLM Vendors", "AiVendors.ListAiVendors.addNewAiVendor": "Add AI/LLM Vendor", "AiVendors.ListAiVendors.empty.title": "AI/LLM Vendors", "AiVendors.ListAiVendors.table.header.label.aiVendorName": "AI/LLM Vendor Name", "AiVendors.ListAiVendors.table.header.label.apiVersion": "API Version", "AiVendors.ListAiVendors.table.header.label.builtInSupport": "Type", "AiVendors.ListAiVendors.table.header.label.description": "Description", "AiVendors.ListAiVendors.table.is.used.delete.tooltip": "Default AI/LLM Vendors cannot be deleted", "AiVendors.OpenAPI.file.error": "Error reading file", "Api.Name": "API Name", "Api.Provider": "Provider", "Api.Version": "Version", "Api.organization.dropdown.tooltip": "Organization ID assigned by the External Identity Provider.", "Apis.Details.Compliance.not.applicable.message": "No governance policies have been attached to this API.", "Apis.Details.Configurations.organizations": "Organizations", "Apis.Details.Configurations.organizations.placeholder.text": "Search Organizations", "Apis.Details.Scopes.CreateScope.roles.help": "Enter a valid role and press `Enter`.", "Apis.Details.Scopes.permission.status.allow": "Allow", "Apis.Details.Scopes.permission.status.deny": "<PERSON><PERSON>", "Apis.Details.Scopes.permission.status.none": "None", "Apis.Listing.Listing.apis.search": "Search", "Apis.Listing.Listing.apis.searching": "Searching", "Apis.Listing.Listing.clear.search": "Clear Search", "Apis.Listing.Listing.empty.message": "No Data to Display", "Apis.Listing.Listing.search.placeholder": "Search by API Name", "Apis.Listing.Listing.title": "Change API Provider", "Apis.Listing.apiTableHead.name": "Name", "Apis.Listing.apiTableHead.provider": "Provider", "Apis.Listing.apiTableHead.version": "Version", "Apis.Shared.AdminRootErrorBoundary.refresh": "Refresh", "Apis.Shared.AdminRootErrorBoundary.refresh.or.try.again.message": "You may refresh the page now or try again later", "Apis.Shared.AdminRootErrorBoundary.something.went.wrong.while.rendering.button": "Something went wrong while rendering the", "Apis.Shared.AdminRootErrorBoundary.something.went.wrong.while.rendering.heading": "Something went wrong while rendering the", "Apis.Shared.AppErrorBoundary.something.went.wrong": "Something went wrong", "Apis.Shared.ConfirmDialog.are.you.sure": "Are you sure?", "Apis.Shared.ConfirmDialog.cancel": "Cancel", "Apis.Shared.ConfirmDialog.ok": "OK", "Apis.Shared.ConfirmDialog.please.confirm": "Please Confirm", "Apis.Shared.RedirectToLogin.you.will.be.redirected.to": "You will be redirected to {page}", "Application.Name": "Application Name", "Application.Owner": "Application Owner", "Application.organization": "Application Organization", "Applications.Listing.ApplicationTableHead.actions": "Actions", "Applications.Listing.ApplicationTableHead.name": "Name", "Applications.Listing.ApplicationTableHead.owner": "Owner", "Applications.Listing.Listing.applications.edit.error.already.exist": "{owner} already has an application with name: {name}", "Applications.Listing.Listing.applications.edit.error.default": "Something went wrong when validating user", "Applications.Listing.Listing.applications.edit.error.owner.invalid": "{owner} is not a valid Subscriber", "Applications.Listing.Listing.applications.edit.error.subscriber.invalid": "Error while updating ownership to {owner}", "Applications.Listing.Listing.applications.edit.error.unknown": "Something went wrong when updating owner", "Applications.Listing.Listing.applications.edit.owner.label": "Owner", "Applications.Listing.Listing.applications.edit.save.btn": "Save", "Applications.Listing.Listing.applications.list.rows.more.than.label": "more than {to}", "Applications.Listing.Listing.applications.list.rows.range.label": "{from}-{to} of {count}", "Applications.Listing.Listing.applications.list.rows.show.label": "Show", "Applications.Listing.Listing.applications.list.title": "Change Application Owner", "Applications.Listing.Listing.applications.search": "Search", "Applications.Listing.Listing.applications.searching": "Searching", "Applications.Listing.Listing.clear.search": "Clear Search", "Applications.Listing.Listing.empty.message": "No Data to Display", "Applications.Listing.Listing.search.placeholder": "Search Application by Name/Owner", "Applications.Listing.Listing.title": "Change Application Owner", "Applications.Listing.apis.list.rows.more.than.label": "more than {to}", "Applications.Listing.apis.list.rows.range.label": "{from}-{to} of {count}", "Applications.Listing.apis.list.rows.show.label": "Show", "Base.Errors.ResourceNotFound.api.list": "API List", "Base.Errors.ResourceNotFound.api.product.list": "API Product List", "Base.Errors.ResourceNotFound.more.links": "You may check the links below", "Base.Errors.ResourceNotfound.default_body": "The page you are looking for is not available", "Base.Errors.ResourceNotfound.default_tittle": "Page Not Found", "Base.Footer.Footer.product_details": "WSO2 API-M v4.5.0 | © 2025 WSO2 LLC", "Base.Header.avatar.Avatar.logout": "Logout", "Base.RouteMenuMapping.advanced": "Advanced", "Base.RouteMenuMapping.advanced.throttling.policies": "Advanced Policies", "Base.RouteMenuMapping.advanced.throttling.policies.Adding": "Add Advanced Policy", "Base.RouteMenuMapping.advanced.throttling.policies.Editing": "Edit Advanced Policy", "Base.RouteMenuMapping.aivendors": "AI/LLM Vendors", "Base.RouteMenuMapping.aivendors.items.Adding": "Add AI/LLM Vendor", "Base.RouteMenuMapping.aivendors.items.Editing": "Edit AI/LLM Vendor", "Base.RouteMenuMapping.api.categories": "API Categories", "Base.RouteMenuMapping.api.product.state.change": "API Product State Change", "Base.RouteMenuMapping.api.revision.deployment": "API Revision Deployment", "Base.RouteMenuMapping.api.state.change": "API State Change", "Base.RouteMenuMapping.apis": "Change API Provider", "Base.RouteMenuMapping.application.creation": "Application Creation", "Base.RouteMenuMapping.application.deletion": "Application Deletion", "Base.RouteMenuMapping.application.reg": "Application Registration", "Base.RouteMenuMapping.application.throttling.policies": "Application Policies", "Base.RouteMenuMapping.applications": "Change Application Owner", "Base.RouteMenuMapping.blacklisted.items": "<PERSON><PERSON>", "Base.RouteMenuMapping.compliance": "Compliance", "Base.RouteMenuMapping.custom.throttling.policies": "Custom Policies", "Base.RouteMenuMapping.custom.throttling.policies.items.Adding": "Add Custom Policy", "Base.RouteMenuMapping.custom.throttling.policies.items.Editing": "Edit Custom Policy", "Base.RouteMenuMapping.dashboard": "Dashboard", "Base.RouteMenuMapping.gateways": "Gateways", "Base.RouteMenuMapping.gateways.items.Adding": "Gateway Environment", "Base.RouteMenuMapping.gateways.items.Editing": "Edit Gateway Environment", "Base.RouteMenuMapping.governance": "Governance", "Base.RouteMenuMapping.governance.policies": "Policies", "Base.RouteMenuMapping.keymanagers": "Key Managers", "Base.RouteMenuMapping.keymanagers.items.Adding": "Add Key Manager", "Base.RouteMenuMapping.keymanagers.items.Editing": "Edit Key Manager", "Base.RouteMenuMapping.labels": "Labels", "Base.RouteMenuMapping.organizations": "Organizations", "Base.RouteMenuMapping.role.permissions": "Scope Assignments", "Base.RouteMenuMapping.ruleset.catalog": "Ruleset Catalog", "Base.RouteMenuMapping.settings": "Settings", "Base.RouteMenuMapping.subscription.creation": "Subscription Creation", "Base.RouteMenuMapping.subscription.deletion": "Subscription Deletion", "Base.RouteMenuMapping.subscription.throttling.policies": "Subscription Policies", "Base.RouteMenuMapping.subscription.throttling.policies.Adding": "Add Subscription Policy", "Base.RouteMenuMapping.subscription.throttling.policies.Editing": "Edit Subscription Policy", "Base.RouteMenuMapping.subscription.update": "Subscription Update", "Base.RouteMenuMapping.tasks": "Tasks", "Base.RouteMenuMapping.tenant.theme": "Tenant Theme", "Base.RouteMenuMapping.throttling.policies": "Rate Limiting Policies", "Base.RouteMenuMapping.usage.report": "Usage Report", "Base.RouteMenuMapping.user.creation": "User Creation", "Claim.Mapping.already.exists": "Claim Mapping already exists.", "Dashboard.apiCategories.apiCategoriesListing.card.title": "API Categories", "Dashboard.apiCategories.apiCategoriesListing.card.view.all.link.text": "View All", "Dashboard.apiCategories.apiCategoriesListing.no.description": "No description available", "Dashboard.apiCategories.noApiCategories.card.add.new.link.text": "Add new Category", "Dashboard.apiCategories.noApiCategories.card.description": "API categories allow API providers to categorize APIs that have similar attributes. When a categorized API gets published to the Developer Portal, its categories appear as clickable links to the API consumers. The API consumers can use the available API categories to quickly jump to a category of interest. {learnMoreLink}", "Dashboard.apiCategories.noApiCategories.card.document.link.text": "Go to Category Documentation", "Dashboard.apiCategories.noApiCategories.card.title": "API Category based grouping", "Dashboard.header.title": "Dashboard", "Dashboard.rateLimiting.card.advancedPolicies.description": "Control access per API or API resource using advanced rules", "Dashboard.rateLimiting.card.advancedPolicies.name": "Advanced Policies", "Dashboard.rateLimiting.card.applicationPolicies.description": "Applicable per access token generated for an application", "Dashboard.rateLimiting.card.applicationPolicies.name": "Application Policies", "Dashboard.rateLimiting.card.customPolicies.description": "Allows system administrators to define dynamic rules for specific use cases, which are applied globally across all tenants.", "Dashboard.rateLimiting.card.customPolicies.name": "Custom Policies", "Dashboard.rateLimiting.card.subscriptionPolicies.description": "Control access per Subscription", "Dashboard.rateLimiting.card.subscriptionPolicies.name": "Subscription Policies", "Dashboard.rateLimiting.card.title": "Rate Limiting", "Dashboard.tasksWorkflow.compactTasks.apiProductStateChange.name": "API Product State Change", "Dashboard.tasksWorkflow.compactTasks.apiRevisionDeployment.name": "API Revision Deployment", "Dashboard.tasksWorkflow.compactTasks.apiStateChange.name": "API State Change", "Dashboard.tasksWorkflow.compactTasks.applicationCreation.name": "Application Creation", "Dashboard.tasksWorkflow.compactTasks.applicationDeletion.name": "Application Deletion", "Dashboard.tasksWorkflow.compactTasks.applicationRegistration.name": "Application Registration", "Dashboard.tasksWorkflow.compactTasks.card.numberOfPendingTasks.postFix.plural": "Pending tasks", "Dashboard.tasksWorkflow.compactTasks.card.numberOfPendingTasks.postFix.singular": "Pending task", "Dashboard.tasksWorkflow.compactTasks.card.title": "Pending tasks", "Dashboard.tasksWorkflow.compactTasks.subscriptionCreation.name": "Subscription Creation", "Dashboard.tasksWorkflow.compactTasks.subscriptionDeletion.name": "Subscription Deletion", "Dashboard.tasksWorkflow.compactTasks.subscriptionUpdate.name": "Subscription Update", "Dashboard.tasksWorkflow.compactTasks.userCreation.name": "User Creation", "Dashboard.tasksWorkflow.noTasks.card.description": "Manage workflow tasks, increase productivity and enhance competitiveness by enabling developers to easily deploy business processes and models.", "Dashboard.tasksWorkflow.noTasks.card.title": "All the pending tasks completed", "Environment.add.success": "- Gateway Environment added successfully.", "Environment.edit.success": "- Gateway Environment edited successfully.", "Form.Dialog.Base.cancel.btn": "Cancel", "Gateway.AddEditGWEnvironment.permission\n                                                                            .denied": "Use of this Gateway Environment is \"Denied\" for above roles.", "Gateway.AddEditGWEnvironment.permission.\n                                                                            allowed": "Use of this Gateway Environment is \"Allowed\" for above roles.", "Gateway.AddEditGWEnvironment.permission.Invalid": "Invalid Role(s) Found", "Gateway.AddEditGWEnvironment.permission.help": "Enter a valid role and press `Enter`", "Gateway.Configuration.Helper.text": "Add Gateway Agent Configurations", "GatewayEnvironment.type": "Key Type", "GatewayEnvironment.vhosts": "Vhosts", "GatewayEnvironment.visibility": "Visibility", "GatewayEnvironments.AddEditGWEnvironment.External.GatewayEnvironment.general.details.description": "Gateway vendor", "GatewayEnvironments.AddEditGWEnvironment.GatewayEnvironment.type": "Gateway Environment Type", "GatewayEnvironments.AddEditGWEnvironment.connector.configurations": "Gateway Agent Configurations", "GatewayEnvironments.AddEditGWEnvironment.connector.configurations\n                                            .description": "Provide connection params for the selected Gateway Environment.", "GatewayEnvironments.AddEditGWEnvironment.form.add": "Add", "GatewayEnvironments.AddEditGWEnvironment.form.cancel": "Cancel", "GatewayEnvironments.AddEditGWEnvironment.form.description": "Description", "GatewayEnvironments.AddEditGWEnvironment.form.displayName": "Display Name", "GatewayEnvironments.AddEditGWEnvironment.form.environment.name.empty": "Name is Empty", "GatewayEnvironments.AddEditGWEnvironment.form.environment.name.invalid": "Name must not contain special characters or spaces", "GatewayEnvironments.AddEditGWEnvironment.form.name": "Name", "GatewayEnvironments.AddEditGWEnvironment.form.name.form.description.help": "Description of the Gateway Environment.", "GatewayEnvironments.AddEditGWEnvironment.form.name.form.displayName.help": "Display name of the Gateway Environment.", "GatewayEnvironments.AddEditGWEnvironment.form.name.help": "Name of the Gateway Environment.", "GatewayEnvironments.AddEditGWEnvironment.form.type.help": "Select Gateway Environment Type", "GatewayEnvironments.AddEditGWEnvironment.form.type.helper.text": "Supported Key Type of the Gateway Environment", "GatewayEnvironments.AddEditGWEnvironment.form.type.hybrid.option": "Hybrid", "GatewayEnvironments.AddEditGWEnvironment.form.type.label": "Type", "GatewayEnvironments.AddEditGWEnvironment.form.type.prod.option": "Production", "GatewayEnvironments.AddEditGWEnvironment.form.type.sandbox.option": "Sandbox", "GatewayEnvironments.AddEditGWEnvironment.form.update.btn": "Update", "GatewayEnvironments.AddEditGWEnvironment.form.vhost.context.invalid": "Invalid Http context", "GatewayEnvironments.AddEditGWEnvironment.form.vhost.host.empty": "Host of Vhost is empty", "GatewayEnvironments.AddEditGWEnvironment.form.visibility": "Visibility", "GatewayEnvironments.AddEditGWEnvironment.form.visibility.allow.option": "Allow for role(s)", "GatewayEnvironments.AddEditGWEnvironment.form.visibility.deny.option": "Deny for role(s)", "GatewayEnvironments.AddEditGWEnvironment.form.visibility.helper.text": "Visibility of the Gateway Environment", "GatewayEnvironments.AddEditGWEnvironment.form.visibility.public.option": "Public", "GatewayEnvironments.AddEditGWEnvironment.form.visibility.select": "Visibility", "GatewayEnvironments.AddEditGWEnvironment.general.details": "General Details", "GatewayEnvironments.AddEditGWEnvironment.general.details.description": "Provide name and description of the Gateway Environment", "GatewayEnvironments.AddEditGWEnvironment.type.description": "Key type supported by the Gateway Environment", "GatewayEnvironments.AddEditGWEnvironment.visibility.add.description": "Configure vhosts", "GatewayEnvironments.AddEditVhost.add.vhost.btn": "New VHost", "GatewayEnvironments.AddEditVhost.host": "Host", "GatewayEnvironments.AddEditVhost.host.gateway.access.url": "Gateway Access URLs", "GatewayEnvironments.AddEditVhost.host.gateway.advanced.settings": "Advanced Settings", "GatewayEnvironments.AddEditVhost.host.gateway.http.context": "HTTP(s) context", "GatewayEnvironments.AddEditVhost.host.helper.text": "ex: mg.wso2.com", "GatewayEnvironments.AddEditVhost.host.remove.btn": "Remove", "GatewayEnvironments.AddEditVhost.host.remove.dialog.content": "Removing an existing VHost may result in inconsistent state if APIs are deployed with this VHost. Please make sure there are no APIs deployed with this VHost or redeploy those APIs.", "GatewayEnvironments.AddEditVhost.host.remove.dialog.no.btn": "No, Don&apos;t Remove", "GatewayEnvironments.AddEditVhost.host.remove.dialog.title": "Remove Existing Vhost?", "GatewayEnvironments.AddEditVhost.host.remove.dialog.yes.btn": "Yes", "GatewayEnvironments.AddEditVhost.httpPort": "HTTP Port", "GatewayEnvironments.AddEditVhost.httpsPort": "HTTPS Port", "GatewayEnvironments.AddEditVhost.wsPort": "WS Port", "GatewayEnvironments.AddEditVhost.wssPort": "WSS Port", "Gateways.AddEditGateway.title.edit": "Gateway Environment - Edit", "Gateways.AddEditGateway.title.new": "Gateway Environment - Create new", "Gateways.ListGatewayEnvironments.addNewGatewayEnvironment": "Add Gateway Environment", "Governance.Compliance.title": "Compliance Dashboard", "Governance.ComplianceDashboard.APICompliance.PolicyAdherence.column.policy": "Policy", "Governance.ComplianceDashboard.APICompliance.PolicyAdherence.column.rulesets": "Rulesets", "Governance.ComplianceDashboard.APICompliance.PolicyAdherence.column.status": "Status", "Governance.ComplianceDashboard.APICompliance.PolicyAdherence.empty.helper": "No governance policies have been applied to this API.", "Governance.ComplianceDashboard.APICompliance.PolicyAdherence.empty.title": "No Policies Applied", "Governance.ComplianceDashboard.APICompliance.PolicyAdherence.followed.count": "{followed}/{total} Followed", "Governance.ComplianceDashboard.APICompliance.PolicyAdherence.not.applied": "N/A - Policy not applied", "Governance.ComplianceDashboard.APICompliance.PolicyAdherence.pending": "N/A - Waiting for policy evaluation", "Governance.ComplianceDashboard.APICompliance.RuleViolation.column.description": "Description", "Governance.ComplianceDashboard.APICompliance.RuleViolation.column.message": "Message", "Governance.ComplianceDashboard.APICompliance.RuleViolation.column.path": "Path", "Governance.ComplianceDashboard.APICompliance.RuleViolation.column.rule": "Rule", "Governance.ComplianceDashboard.APICompliance.RuleViolation.empty.errors": "No Error violations found", "Governance.ComplianceDashboard.APICompliance.RuleViolation.empty.info": "No Info violations found", "Governance.ComplianceDashboard.APICompliance.RuleViolation.empty.passed": "No Passed rules found", "Governance.ComplianceDashboard.APICompliance.RuleViolation.empty.warnings": "No Warning violations found", "Governance.ComplianceDashboard.APICompliance.RuleViolation.tab.errors": "Errors ({count})", "Governance.ComplianceDashboard.APICompliance.RuleViolation.tab.info": "Info ({count})", "Governance.ComplianceDashboard.APICompliance.RuleViolation.tab.passed": "Passed ({count})", "Governance.ComplianceDashboard.APICompliance.RuleViolation.tab.warnings": "Warnings ({count})", "Governance.ComplianceDashboard.APICompliance.RulesetAdherence.column.ruleset": "Ruleset", "Governance.ComplianceDashboard.APICompliance.RulesetAdherence.column.status": "Status", "Governance.ComplianceDashboard.APICompliance.RulesetAdherence.column.violations": "Violations", "Governance.ComplianceDashboard.APICompliance.RulesetAdherence.empty.helper": "No governance rulesets have been applied for this API.", "Governance.ComplianceDashboard.APICompliance.RulesetAdherence.empty.title": "No Rulesets Found", "Governance.ComplianceDashboard.APICompliance.RulesetAdherence.violations.tooltip": "Errors: {error}, Warnings: {warn}, Info: {info}", "Governance.ComplianceDashboard.APICompliance.column.api": "API", "Governance.ComplianceDashboard.APICompliance.column.policies": "Policies", "Governance.ComplianceDashboard.APICompliance.column.status": "Status", "Governance.ComplianceDashboard.APICompliance.empty.content": "No APIs Available", "Governance.ComplianceDashboard.APICompliance.empty.helper": "Create APIs to start evaluating their compliance.", "Governance.ComplianceDashboard.APICompliance.followed.count": "{followed}/{total} Followed", "Governance.ComplianceDashboard.APICompliance.no.policies": "N/A - No policies to evaluate", "Governance.ComplianceDashboard.APICompliance.pending": "N/A - Waiting for policy evaluation", "Governance.ComplianceDashboard.Compliance.api.owner": "API Owner: {owner}", "Governance.ComplianceDashboard.Compliance.back.to.compliance": "Back to Compliance Dashboard", "Governance.ComplianceDashboard.Compliance.check.progress": "Compliance Check in Progress", "Governance.ComplianceDashboard.Compliance.check.progress.message": "The compliance check is currently in progress. This may take a few moments.", "Governance.ComplianceDashboard.Compliance.failed": "Failed", "Governance.ComplianceDashboard.Compliance.followed": "Followed ({count})", "Governance.ComplianceDashboard.Compliance.help.link": "Compliance Monitoring", "Governance.ComplianceDashboard.Compliance.not.applicable.description": "Please attach governance policies to the API to view the compliance status.", "Governance.ComplianceDashboard.Compliance.not.applied": "Not Applied ({count})", "Governance.ComplianceDashboard.Compliance.passed": "Passed", "Governance.ComplianceDashboard.Compliance.pending": "Pending ({count})", "Governance.ComplianceDashboard.Compliance.policy.adherence": "Policy Adherence", "Governance.ComplianceDashboard.Compliance.policy.adherence.summary": "Policy Adherence Summary", "Governance.ComplianceDashboard.Compliance.rule.adherence": "Rule Adherence", "Governance.ComplianceDashboard.Compliance.rules.errors": "Errors ({count})", "Governance.ComplianceDashboard.Compliance.rules.info": "Info ({count})", "Governance.ComplianceDashboard.Compliance.rules.passed": "Passed ({count})", "Governance.ComplianceDashboard.Compliance.rules.warnings": "Warnings ({count})", "Governance.ComplianceDashboard.Compliance.ruleset.adherence": "Ruleset Adherence", "Governance.ComplianceDashboard.Compliance.ruleset.adherence.summary": "Ruleset Adherence Summary", "Governance.ComplianceDashboard.Compliance.title": "Compliance Summary - {artifactName}", "Governance.ComplianceDashboard.Compliance.unapplied": "Unapplied", "Governance.ComplianceDashboard.Compliance.violated": "Violated ({count})", "Governance.ComplianceDashboard.PolicyAdherence.column.apis": "APIs", "Governance.ComplianceDashboard.PolicyAdherence.column.policy": "Policy", "Governance.ComplianceDashboard.PolicyAdherence.column.status": "Status", "Governance.ComplianceDashboard.PolicyAdherence.compliant.count": "{followed}/{total} Compliant", "Governance.ComplianceDashboard.PolicyAdherence.empty.content": "No Governance Policies Available", "Governance.ComplianceDashboard.PolicyAdherence.empty.helper": "Create a new governance policy to start governing the APIs.", "Governance.ComplianceDashboard.PolicyAdherence.no.apis": "N/A - No APIs to evaluate", "Governance.ComplianceDashboard.Summary.api.compliance": "API Compliance", "Governance.ComplianceDashboard.Summary.api.compliance.details": "API Compliance Details", "Governance.ComplianceDashboard.Summary.api.compliant": "Compliant ({count})", "Governance.ComplianceDashboard.Summary.api.non.compliant": "Non-Compliant ({count})", "Governance.ComplianceDashboard.Summary.api.not.applicable": "Not Applicable ({count})", "Governance.ComplianceDashboard.Summary.api.pending": "Pending ({count})", "Governance.ComplianceDashboard.Summary.help.link": "Compliance Monitoring", "Governance.ComplianceDashboard.Summary.policy.adherence": "Policy Adherence", "Governance.ComplianceDashboard.Summary.policy.adherence.details": "Policy Adherence Details", "Governance.ComplianceDashboard.Summary.policy.followed": "Followed ({count})", "Governance.ComplianceDashboard.Summary.policy.not.applied": "Not Applied ({count})", "Governance.ComplianceDashboard.Summary.policy.violated": "Violated ({count})", "Governance.Policies.AddEdit.action.create.update.info": "Governance rule violations in the API Create and API Update states use the Notify action by default for all severity levels (Error, Warn, Info).", "Governance.Policies.AddEdit.action.edit.disabled.tooltip": "Cannot edit as only notify action is allowed", "Governance.Policies.AddEdit.action.table.actions": "Edit / Delete", "Governance.Policies.AddEdit.action.table.onError": "On Error", "Governance.Policies.AddEdit.action.table.onInfo": "On Info", "Governance.Policies.AddEdit.action.table.onWarn": "On Warn", "Governance.Policies.AddEdit.action.table.state": "State", "Governance.Policies.AddEdit.add.success": "Policy Added Successfully", "Governance.Policies.AddEdit.edit.success": "Policy Updated Successfully", "Governance.Policies.AddEdit.enforcement.action.block": "Block", "Governance.Policies.AddEdit.enforcement.action.notify": "Notify", "Governance.Policies.AddEdit.enforcement.actions.title": "Actions", "Governance.Policies.AddEdit.enforcement.add.button": "Add Enforcement Criteria", "Governance.Policies.AddEdit.enforcement.add.disabled.tooltip": "All available states have been configured", "Governance.Policies.AddEdit.enforcement.description": "Choose when the policy should be applied and the action that should be taken based on the severity of the rule violation.", "Governance.Policies.AddEdit.enforcement.dialog.cancel": "Cancel", "Governance.Policies.AddEdit.enforcement.dialog.save": "Save", "Governance.Policies.AddEdit.enforcement.dialog.title": "Enforcement Criteria", "Governance.Policies.AddEdit.enforcement.severity.title": "Severity Levels", "Governance.Policies.AddEdit.enforcement.state.configured": "(Already configured)", "Governance.Policies.AddEdit.enforcement.state.label": "Governed State", "Governance.Policies.AddEdit.enforcement.title": "Enforcement", "Governance.Policies.AddEdit.error.loading.labels": "Error loading labels", "Governance.Policies.AddEdit.error.loading.rulesets": "Error loading rulesets", "Governance.Policies.AddEdit.form.actions.invalid": "Actions must be properly configured", "Governance.Policies.AddEdit.form.actions.invalid.block": "BLOCK action is not allowed for API_CREATE and API_UPDATE states", "Governance.Policies.AddEdit.form.add.btn": "Create", "Governance.Policies.AddEdit.form.cancel": "Cancel", "Governance.Policies.AddEdit.form.description": "Description", "Governance.Policies.AddEdit.form.description.help": "Description of the governance policy.", "Governance.Policies.AddEdit.form.description.too.long": "Description cannot exceed 1024 characters", "Governance.Policies.AddEdit.form.has.errors": "One or more fields contain errors.", "Governance.Policies.AddEdit.form.labels.required": "At least one label is required when applying to specific APIs", "Governance.Policies.AddEdit.form.name": "Name", "Governance.Policies.AddEdit.form.name.help": "Name of the governance policy.", "Governance.Policies.AddEdit.form.name.invalid": "Policy name can only contain alphanumeric characters, hyphens, underscores, and spaces", "Governance.Policies.AddEdit.form.name.required": "Policy name is required", "Governance.Policies.AddEdit.form.name.too.long": "Policy name cannot exceed 255 characters", "Governance.Policies.AddEdit.form.name.too.short": "Policy name cannot be empty", "Governance.Policies.AddEdit.form.rulesets.duplicate": "Duplicate rulesets are not allowed", "Governance.Policies.AddEdit.form.rulesets.required": "At least one ruleset is required", "Governance.Policies.AddEdit.form.update.btn": "Update", "Governance.Policies.AddEdit.general.details": "General Details", "Governance.Policies.AddEdit.general.details.description": "Provide name and description of the policy.", "Governance.Policies.AddEdit.help.link": "Create and Manage Policies", "Governance.Policies.AddEdit.labels.applyAll": "All APIs", "Governance.Policies.AddEdit.labels.applyNone": "None", "Governance.Policies.AddEdit.labels.applySpecific": "APIs with specific labels", "Governance.Policies.AddEdit.labels.description": "Choose whether to attach this policy to all APIs or only to APIs with specific labels", "Governance.Policies.AddEdit.labels.helper": "Select one or more labels to determine which APIs this policy attaches to", "Governance.Policies.AddEdit.labels.input": "Select Labels", "Governance.Policies.AddEdit.labels.title": "Attachment", "Governance.Policies.AddEdit.rulesets.description": "Search and select rulesets to include in the policy. Selected rulesets will appear above the search bar.", "Governance.Policies.AddEdit.rulesets.empty": "No rulesets available", "Governance.Policies.AddEdit.rulesets.noSearchResults": "No rulesets found matching your search", "Governance.Policies.AddEdit.rulesets.title": "Rulesets", "Governance.Policies.AddEdit.title.edit": "Governance Policy - Edit", "Governance.Policies.AddEdit.title.new": "Governance Policy - Create new", "Governance.Policies.List.add.new.policy": "Create Policy", "Governance.Policies.List.addPolicy.title": "Create Governance Policy", "Governance.Policies.List.addPolicy.triggerButtonText": "Create Governance Policy", "Governance.Policies.List.column.appliesTo": "Applies to", "Governance.Policies.List.column.appliesWhen": "Applies when", "Governance.Policies.List.column.policy": "Policy", "Governance.Policies.List.description": "Create governance policies using rulesets from the catalog to standardize and regulate your APls effectively", "Governance.Policies.List.edit.title": "Edit Policy", "Governance.Policies.List.empty.content": "Governance policies help you enforce standards and compliance across your APIs. Click the Create button to add your first policy.", "Governance.Policies.List.empty.title": "Governance Policies", "Governance.Policies.List.help.link": "Create and Manage Policies", "Governance.Policies.List.search.default": "Search policies by name or label", "Governance.Policies.List.title": "Governance Policies", "Governance.Rulesets.AddEdit.add.success": "Ruleset Added Successfully", "Governance.Rulesets.AddEdit.button.create": "Create", "Governance.Rulesets.AddEdit.button.download": "Download", "Governance.Rulesets.AddEdit.button.update": "Update", "Governance.Rulesets.AddEdit.button.upload": "Upload File", "Governance.Rulesets.AddEdit.confirm.overwrite.cancel": "Cancel", "Governance.Rulesets.AddEdit.confirm.overwrite.message": "The editor contains existing content. Do you want to overwrite it with the uploaded file?", "Governance.Rulesets.AddEdit.confirm.overwrite.ok": "Overwrite", "Governance.Rulesets.AddEdit.confirm.overwrite.title": "Confirm Overwrite", "Governance.Rulesets.AddEdit.content.description": "Define the ruleset content in YAML or JSON format", "Governance.Rulesets.AddEdit.content.title": "Ruleset Content", "Governance.Rulesets.AddEdit.download.empty": "No content to download", "Governance.Rulesets.AddEdit.edit.success": "Ruleset Updated Successfully", "Governance.Rulesets.AddEdit.error.loading": "Error loading ruleset", "Governance.Rulesets.AddEdit.file.read.error": "Error reading file", "Governance.Rulesets.AddEdit.form.artifact.type": "Artifact Type", "Governance.Rulesets.AddEdit.form.artifacttype.required": "Artifact type is required", "Governance.Rulesets.AddEdit.form.cancel": "Cancel", "Governance.Rulesets.AddEdit.form.description": "Description", "Governance.Rulesets.AddEdit.form.description.too.long": "Description cannot exceed 1000 characters", "Governance.Rulesets.AddEdit.form.doclink.invalid": "Documentation link must be a valid HTTP/HTTPS URL", "Governance.Rulesets.AddEdit.form.doclink.too.long": "Documentation link cannot exceed 500 characters", "Governance.Rulesets.AddEdit.form.documentation": "Documentation Link", "Governance.Rulesets.AddEdit.form.has.errors": "One or more fields contain errors.", "Governance.Rulesets.AddEdit.form.name": "Name", "Governance.Rulesets.AddEdit.form.name.invalid": "Ruleset name can only contain alphanumeric characters, spaces, hyphens and underscores.", "Governance.Rulesets.AddEdit.form.name.required": "Ruleset name is required", "Governance.Rulesets.AddEdit.form.name.too.long": "Ruleset name cannot exceed 255 characters", "Governance.Rulesets.AddEdit.form.ruleset.type": "Ruleset Type", "Governance.Rulesets.AddEdit.form.rulesetcontent.required": "Ruleset content is required", "Governance.Rulesets.AddEdit.form.ruletype.required": "Rule type is required", "Governance.Rulesets.AddEdit.general.details": "General Details", "Governance.Rulesets.AddEdit.general.details.description": "Provide name and description of the ruleset.", "Governance.Rulesets.AddEdit.help.link": "Create and Manage Rulesets", "Governance.Rulesets.AddEdit.title.edit": "Edit Ruleset - {name}", "Governance.Rulesets.AddEdit.title.new": "Create New Ruleset", "Governance.Rulesets.List.add.new.ruleset": "Create Ruleset", "Governance.Rulesets.List.addRuleset.title": "Create Ruleset", "Governance.Rulesets.List.addRuleset.triggerButtonText": "Create Ruleset", "Governance.Rulesets.List.column.artifactType": "Artifact Type", "Governance.Rulesets.List.column.provider": "Provider", "Governance.Rulesets.List.column.ruleset": "Ruleset", "Governance.Rulesets.List.column.rulesetType": "Ruleset Type", "Governance.Rulesets.List.description": "Find comprehensive governance rulesets designed to ensure the consistency, security and reliability for your APls", "Governance.Rulesets.List.edit.title": "Edit Ruleset", "Governance.Rulesets.List.empty.content": "Rulesets are the building blocks for creating governance policies. They contain predefined rules and validations that can be used to enforce standards across your APIs. Click Create Ruleset to get started.", "Governance.Rulesets.List.empty.title": "Ruleset Catalog", "Governance.Rulesets.List.help.link": "Create and Manage Rulesets", "Governance.Rulesets.List.search.placeholder": "Search rulesets by name or type", "Governance.Rulesets.List.title": "Ruleset Catalog", "KeyManager.AddEdit.Invalid.Roles.Found": "Invalid Role(s) Found", "KeyManager.AddEdit.roles.help": "Enter a valid role and press `Enter`", "KeyManager.AddEditKeyManager.permissions.add.description": "Permissions for the Key Manager", "KeyManager.Claim.Helper.text": "Add <PERSON><PERSON> Mappings", "KeyManager.Connector.Configuration.Helper.text": "Add Key Manager Connector Configurations", "KeyManager.KeyValidation.CUSTOM": "CUSTOM", "KeyManager.KeyValidation.JWT": "JWT", "KeyManager.KeyValidation.NONE": "NONE", "KeyManager.KeyValidation.REFERENCE": "REFERENCE", "KeyManager.KeyValidation.token.validation.type": "Type", "KeyManager.add.success": "Key Manager added successfully.", "KeyManager.add.success.msg": "- Key Manager added successfully.", "KeyManager.edit.success": "- Key Manager edited successfully.", "KeyManager.enter.permission.allowed": "Use of this Key-Manager is \"Allowed\" for above roles.", "KeyManager.enter.permission.denied": "Use of this Key-Manager is \"Denied\" for above roles.", "KeyManager.permissions": "Permissions", "KeyManagers.AddEditKeyManager.External.KeyManager.general.details.description": "Identity Provider vendor and the token usage mode", "KeyManagers.AddEditKeyManager.KeyManager.type": "Key Manager Type", "KeyManagers.AddEditKeyManager.advanced": "Advanced Configurations", "KeyManagers.AddEditKeyManager.advanced.description": "Advanced options for the Key Manager", "KeyManagers.AddEditKeyManager.api.invocation.method": "API Invocation Method", "KeyManagers.AddEditKeyManager.api.no.usages": "No API usages for this key manager specifically.", "KeyManagers.AddEditKeyManager.application.no.usages": "No Application usages for this key manager specifically.", "KeyManagers.AddEditKeyManager.certificate": "Certificates", "KeyManagers.AddEditKeyManager.certificate.description": "Upload or provide the certificate inline.", "KeyManagers.AddEditKeyManager.claim.mappings.hidden.help": "Expand to add edit claim mappings", "KeyManagers.AddEditKeyManager.claim.mappings.title": "<PERSON><PERSON><PERSON>s", "KeyManagers.AddEditKeyManager.claim.uris": "<PERSON><PERSON><PERSON>", "KeyManagers.AddEditKeyManager.claim.uris.description": "Provide claim URIs for consumer key and scopes.", "KeyManagers.AddEditKeyManager.connector.configurations": "Connector Configurations", "KeyManagers.AddEditKeyManager.connector.configurations.description": "Provide connection params for the selected Key Manager.", "KeyManagers.AddEditKeyManager.endpoints": "Key Manager Endpoints", "KeyManagers.AddEditKeyManager.endpoints.description": "Configure endpoints such as client registration endpoint, the token endpoint for this Key Manager.", "KeyManagers.AddEditKeyManager.exchange.token.form.token.audience.help": "The Audience of the authorization server which the access token is intended for.", "KeyManagers.AddEditKeyManager.form.Issuer": "Issuer", "KeyManagers.AddEditKeyManager.form.add": "Add", "KeyManagers.AddEditKeyManager.form.authorizeEndpoint": "Authorize Endpoint", "KeyManagers.AddEditKeyManager.form.authorizeEndpoint.help": "E.g., https://localhost:9443/oauth2/authorize", "KeyManagers.AddEditKeyManager.form.cancel": "Cancel", "KeyManagers.AddEditKeyManager.form.claim.help": "Type Available Grant Types and press Enter/Return to add them.", "KeyManagers.AddEditKeyManager.form.claim.placeholder": "Type Grant Types and press Enter", "KeyManagers.AddEditKeyManager.form.clientRegistrationEndpoint": "Client Registration Endpoint", "KeyManagers.AddEditKeyManager.form.clientRegistrationEndpoint.help": "E.g., https://localhost:9444/client-registration/v0.17/register", "KeyManagers.AddEditKeyManager.form.consumerKeyClaim.help": "Provide consumer key claim URIs.", "KeyManagers.AddEditKeyManager.form.description": "Description", "KeyManagers.AddEditKeyManager.form.description.help": "Description of the Key Manager.", "KeyManagers.AddEditKeyManager.form.displayName.help": "Display Name of the Key Manager.", "KeyManagers.AddEditKeyManager.form.displayRevokeEndpoint": "Display Revoke Endpoint", "KeyManagers.AddEditKeyManager.form.displayTokenEndpoint": "Display Token Endpoint", "KeyManagers.AddEditKeyManager.form.has.errors": "One or more fields contain errors.", "KeyManagers.AddEditKeyManager.form.introspectionEndpoint": "Introspection Endpoint", "KeyManagers.AddEditKeyManager.form.introspectionEndpoint.help": "E.g., https://localhost:9443/oauth2/introspect", "KeyManagers.AddEditKeyManager.form.issuer.help": "E.g.,: https://localhost:9443/oauth2/token", "KeyManagers.AddEditKeyManager.form.name": "Name", "KeyManagers.AddEditKeyManager.form.name.help": "Name of the Key Manager.", "KeyManagers.AddEditKeyManager.form.orgs.error": "At least one organization should be selected.", "KeyManagers.AddEditKeyManager.form.revokeEndpoint": "Revoke Endpoint", "KeyManagers.AddEditKeyManager.form.revokeEndpoint.help": "E.g., https://localhost:9443/oauth2/revoke", "KeyManagers.AddEditKeyManager.form.scopeManagementEndpoint": "Scope Management Endpoint", "KeyManagers.AddEditKeyManager.form.scopeManagementEndpoint.help": "E.g, https://localhost:9443/oauth2/scope", "KeyManagers.AddEditKeyManager.form.scopesClaim.help": "Provide scope claim URI.", "KeyManagers.AddEditKeyManager.form.token.audience.help": "The Audience of the authorization server which the access token is intended for.", "KeyManagers.AddEditKeyManager.form.tokenEndpoint": "Token Endpoint", "KeyManagers.AddEditKeyManager.form.tokenEndpoint.help": "E.g., https://localhost:9443/oauth2/token", "KeyManagers.AddEditKeyManager.form.tokentype.error": "At least one API Invocation Method should supported by the Key Manager", "KeyManagers.AddEditKeyManager.form.type.help": "Select Key Manager Type", "KeyManagers.AddEditKeyManager.form.update.btn": "Update", "KeyManagers.AddEditKeyManager.form.userInfoEndpoint": "UserInfo Endpoint", "KeyManagers.AddEditKeyManager.form.userInfoEndpoint.help": "E.g., https://localhost:9443/oauth2/userInfo", "KeyManagers.AddEditKeyManager.form.wellKnownUrl": "Well-known URL", "KeyManagers.AddEditKeyManager.form.wellKnownUrl.help": "Provide a well-known URL and discover the Key Manager information.", "KeyManagers.AddEditKeyManager.general.details": "General Details", "KeyManagers.AddEditKeyManager.general.details.description": "Provide name and description of the Key Manager.", "KeyManagers.AddEditKeyManager.grant.types": "Grant Types", "KeyManagers.AddEditKeyManager.grant.types.description": "Add the supported grant types by the Key Manager. Press enter to add each grant.", "KeyManagers.AddEditKeyManager.import.button.message": "Import", "KeyManagers.AddEditKeyManager.importing.message": "Importing...", "KeyManagers.AddEditKeyManager.is.empty.error": "is empty", "KeyManagers.AddEditKeyManager.is.empty.error.key.config": "Required field is empty.", "KeyManagers.AddEditKeyManager.org.description": "Make this key manager available to selected organizations.", "KeyManagers.AddEditKeyManager.orgs": "Available Organizations", "KeyManagers.AddEditKeyManager.resident.endpoints.description": "Configure display endpoints such as display token endpoint, display revoke endpoint for this Key Manager.", "KeyManagers.AddEditKeyManager.selfvalidate": "Self validate JWT", "KeyManagers.AddEditKeyManager.space.error": "Key Manager name contains white spaces.", "KeyManagers.AddEditKeyManager.title.edit": "Key Manager - Edit", "KeyManagers.AddEditKeyManager.title.editGlobal": "Global Key Manager - Edit", "KeyManagers.AddEditKeyManager.title.new": "Key Manager - C<PERSON> new", "KeyManagers.AddEditKeyManager.title.newGlobal": "Global Key Manager - Create new", "KeyManagers.AddEditKeyManager.token.handling.options": "Token Handling Options", "KeyManagers.AddEditKeyManager.token.validation.method": "Token Validation Method", "KeyManagers.AddEditKeyManager.usages": "Key Manager <PERSON><PERSON>", "KeyManagers.AddEditKeyManager.useIntrospect": "Use introspect", "KeyManagers.AddEditTokenExchangeIDP.form.Issuer": "Issuer", "KeyManagers.AddEditTokenExchangeIDP.form.issuer.help": "E.g.,: https://localhost:9443/oauth2/token", "KeyManagers.AddEditTokenExchangeIDP.form.tokenEndpoint": "Token Endpoint", "KeyManagers.AddEditTokenExchangeIDP.form.tokenEndpoint.help": "E.g., https://localhost:9443/oauth2/token", "KeyManagers.AddEditTokenExchangeIDP.form.wellKnownUrl": "Well-known URL", "KeyManagers.AddEditTokenExchangeIDP.form.wellKnownUrl.help": "Provide a well-known URL and discover the Key Manager information.", "KeyManagers.Certificates.browse.files.to.upload": "Browse File to Upload", "KeyManagers.Certificates.drag.and.drop.message": "Drag and Drop files here {break} or {break}", "KeyManagers.Certificates.file.error": "Error reading file", "KeyManagers.Certificates.jwks.url": "URL", "KeyManagers.Certificates.override.message": "Upload new file to override the current certificate", "KeyManagers.Certificates.paste.label": "Paste the content of the PEM file", "KeyManagers.ListKeyManagerAPIUsages.error": "Unable to get Key Manager API usage details", "KeyManagers.ListKeyManagerApplicationUsages.error": "Unable to get Key Manager application usage details", "KeyManagers.ListKeyManagerUsages.API.usages.count.multiple": "{count} APIs are using this key manager specifically", "KeyManagers.ListKeyManagerUsages.API.usages.count.one": "1 API is using this key manager specifically.", "KeyManagers.ListKeyManagerUsages.Application.usages.count.multiple": "{count} Applications are using this key manager specifically", "KeyManagers.ListKeyManagerUsages.Application.usages.count.one": "1 Application is using this key manager specifically.", "KeyManagers.ListKeyManagerUsages.empty.content": "There are no Key Manger usages.", "KeyManagers.ListKeyManagerUsages.empty.title": "Key Manager Usages", "KeyManagers.ListKeyManagerUsages.permission.denied.content": "You dont have enough permission to view Key Manager Usages. Please contact the site administrator.", "KeyManagers.ListKeyManagerUsages.permission.denied.title": "Permission Denied", "KeyManagers.ListKeyManagerUsages.tab.API.usages": "API Usages", "KeyManagers.ListKeyManagerUsages.tab.Application.usages": "Application Usages", "KeyManagers.ListKeyManagers.List.title": "Key Managers", "KeyManagers.ListKeyManagers.addButtonProps.triggerButtonText": "Add Key Manager", "KeyManagers.ListKeyManagers.addGlobalKeyManager": "Add Global Key Manager", "KeyManagers.ListKeyManagers.edit.success": "Key Manager updated successfully.", "KeyManagers.ListKeyManagers.empty.title": "Key Managers", "KeyManagers.ListKeyManagers.table.global.delete.tooltip": "Global Key Manager only can be deleted by the super admin user", "KeyManagers.ListKeyManagers.table.header.label.description": "Description", "KeyManagers.ListKeyManagers.table.header.label.name": "Name", "KeyManagers.ListKeyManagers.table.header.label.provider": "Provider", "KeyManagers.ListKeyManagers.table.header.label.tokenType": "Type", "KeyManagers.ListKeyManagers.table.header.label.usage": "Usage", "KeyManagers.ListKeyManagers.table.is.used.delete.tooltip": "Key manager is used by an API or an Application", "Keymanager.Claim.Action": "Action", "Keymanager.KeyValidation.Action": "Action", "Keymanager.KeyValidation.ClaimKey": "<PERSON><PERSON><PERSON>", "Keymanager.KeyValidation.ClaimValue.Regex": "Claim Value Regex", "Keymanager.Local.Claim": "Local Claim", "Keymanager.Local.Claim.remove": "Remove", "Keymanager.Remote.Claim": "<PERSON><PERSON>", "Labels.AddEditLabel.api.no.usages": "No API usages for this Label specifically.", "Labels.AddEditLabel.usages": "Label Usage", "Labels.ListLabelUsages.API.usages.count.multiple": "{count} APIs are using this label specifically", "Labels.ListLabelUsages.API.usages.count.one": "1 API is using this Label specifically.", "Labels.ListLabelUsages.permission.denied.content": "You dont have enough permission to view Label Usages. Please contact the site administrator.", "Labels.ListLabelUsages.permission.denied.title": "Permission Denied", "Labels.ListLabelsAPIUsages.error": "Unable to get Label API usage details", "LoginDenied.logout": "Logout", "LoginDenied.message": "The server could not verify that you are authorized to access the requested resource.", "LoginDenied.retry": "Retry", "LoginDenied.title": "Error 403 : Forbidden", "Mui.data.table.filter.icon.label": "Filter Table", "Mui.data.table.pagination.display.rows": "of", "Mui.data.table.pagination.rows.per.page": "Rows per page:", "Mui.data.table.search.icon.label": "Search", "Mui.data.table.search.no.records.found": "Sorry, no matching records found", "PERMISSION_TREE.apim.admin": "Manage all admin operations", "PERMISSION_TREE.apim.admin_alert_manage": "Manage admin alerts", "PERMISSION_TREE.apim.admin_application_view": "View Applications", "PERMISSION_TREE.apim.admin_operations": "Manage API categories and Key Managers related operations", "PERMISSION_TREE.apim.admin_settings": "Retrieve admin settings", "PERMISSION_TREE.apim.admin_tier_manage": "Update and delete throttling policies", "PERMISSION_TREE.apim.admin_tier_view": "View throttling policies", "PERMISSION_TREE.apim.api_category": "Manage API categories", "PERMISSION_TREE.apim.api_create": "Create API", "PERMISSION_TREE.apim.api_definition_view": "View, Retrieve API definition", "PERMISSION_TREE.apim.api_delete": "Delete API", "PERMISSION_TREE.apim.api_generate_key": "Generate Internal Key", "PERMISSION_TREE.apim.api_import_export": "Import and export APIs related operations", "PERMISSION_TREE.apim.api_key": "Generate API Keys", "PERMISSION_TREE.apim.api_list_view": "View, Retrieve API list", "PERMISSION_TREE.apim.api_manage": "Manage all API related operations", "PERMISSION_TREE.apim.api_mediation_policy_manage": "View, create, update and remove API specific mediation policies", "PERMISSION_TREE.apim.api_product_import_export": "Import and export API Products related operations", "PERMISSION_TREE.apim.api_provider_change": "Retrieve and manage applications", "PERMISSION_TREE.apim.api_publish": "Publish API", "PERMISSION_TREE.apim.api_view": "View API", "PERMISSION_TREE.apim.api_workflow_approve": "Manage workflows", "PERMISSION_TREE.apim.api_workflow_view": "Retrive workflow requests", "PERMISSION_TREE.apim.app_import_export": "Import and export applications related operations", "PERMISSION_TREE.apim.app_manage": "Retrieve, Manage and Import, Export applications", "PERMISSION_TREE.apim.app_owner_change": "Retrieve and manage applications", "PERMISSION_TREE.apim.bl_manage": "Update and delete deny policies", "PERMISSION_TREE.apim.bl_view": "View deny policies", "PERMISSION_TREE.apim.bot_data": "Retrieve bot detection data", "PERMISSION_TREE.apim.client_certificates_add": "Add client certificates", "PERMISSION_TREE.apim.client_certificates_manage": "View, create, update and remove client certificates", "PERMISSION_TREE.apim.client_certificates_update": "Update and delete client certificates", "PERMISSION_TREE.apim.client_certificates_view": "View client certificates", "PERMISSION_TREE.apim.comment_manage": "Read and Write comments", "PERMISSION_TREE.apim.comment_view": "Read permission to comments", "PERMISSION_TREE.apim.comment_write": "Write permission to comments", "PERMISSION_TREE.apim.common_operation_policy_manage": "Add, Update and Delete common operation policies", "PERMISSION_TREE.apim.common_operation_policy_view": "View common operation policies", "PERMISSION_TREE.apim.document_create": "Create API documents", "PERMISSION_TREE.apim.document_manage": "Create, update and delete API documents", "PERMISSION_TREE.apim.environment_manage": "Manage gateway environments", "PERMISSION_TREE.apim.environment_read": "Retrieve gateway environments", "PERMISSION_TREE.apim.ep_certificates_add": "Add backend endpoint certificates", "PERMISSION_TREE.apim.ep_certificates_manage": "View, create, update and remove endpoint certificates", "PERMISSION_TREE.apim.ep_certificates_update": "Update and delete backend endpoint certificates", "PERMISSION_TREE.apim.ep_certificates_view": "View backend endpoint certificates", "PERMISSION_TREE.apim.gateway_policy_manage": "Add, Update and Delete gateway policies", "PERMISSION_TREE.apim.gateway_policy_view": "View gateway policies", "PERMISSION_TREE.apim.keymanagers_manage": "Manage Key Managers", "PERMISSION_TREE.apim.mediation_policy_create": "Create mediation policies", "PERMISSION_TREE.apim.mediation_policy_manage": "Update and delete mediation policies", "PERMISSION_TREE.apim.mediation_policy_view": "View mediation policies", "PERMISSION_TREE.apim.monetization_usage_publish": "Retrieve and publish Monetization related usage records", "PERMISSION_TREE.apim.policies_import_export": "Export and import policies related operations", "PERMISSION_TREE.apim.pub_alert_manage": "Get/ subscribe/ configure publisher alerts", "PERMISSION_TREE.apim.publisher_settings": "Retrieve store settings", "PERMISSION_TREE.apim.role_manage": "Manage system roles", "PERMISSION_TREE.apim.scope_manage": "Manage system scopes", "PERMISSION_TREE.apim.shared_scope_manage": "Manage shared scopes", "PERMISSION_TREE.apim.store_settings": "Retrieve Developer Portal settings", "PERMISSION_TREE.apim.sub_alert_manage": "Retrieve, subscribe and configure Developer Portal alert types", "PERMISSION_TREE.apim.sub_manage": "Retrieve, Manage subscriptions", "PERMISSION_TREE.apim.subscribe": "Subscribe API", "PERMISSION_TREE.apim.subscription_block": "Block Subscription", "PERMISSION_TREE.apim.subscription_manage": "Manage all Subscription related operations", "PERMISSION_TREE.apim.subscription_view": "View Subscription", "PERMISSION_TREE.apim.tenantInfo": "Retrieve tenant related information", "PERMISSION_TREE.apim.tenant_theme_manage": "Manage tenant themes", "PERMISSION_TREE.apim.threat_protection_policy_create": "Create threat protection policies", "PERMISSION_TREE.apim.threat_protection_policy_manage": "Update and delete threat protection policies", "PERMISSION_TREE.apim.tier_manage": "View, update and delete throttling policies", "PERMISSION_TREE.apim.tier_view": "View throttling policies", "PERMISSION_TREE.service_catalog.service_view": "view access to services in service catalog", "PERMISSION_TREE.service_catalog.service_write": "write access to services in service catalog", "Permissions.tree.scope.assignments.title": "Scope Assignments ({totalPermissions})", "Role.permissions.Role.Permissions.Admin.Table.displayed.rows.more.than.label": "more than {to}", "Role.permissions.Role.Permissions.Admin.Table.displayed.rows.range.label": "{from}-{to} of {count}", "Role.permissions.Role.Permissions.Admin.Table.row.per.page.label": "Rows per page:", "RolePermissions.Common.AddRoleWizard.add.dialog.back": "Back", "RolePermissions.Common.AddRoleWizard.add.dialog.cancel": "Cancel", "RolePermissions.Common.AddRoleWizard.add.mapping.button": "Add scope mapping", "RolePermissions.Common.AddRoleWizard.add.mapping.title": "Add new scope mapping", "RolePermissions.Common.AddRoleWizard.add.provide.role.next.btn": "Next", "RolePermissions.Common.AddRoleWizard.add.provide.role.save.btn": "Save", "RolePermissions.Common.AddRoleWizard.add.provide.role.text": "Provide role name", "RolePermissions.Common.AddRoleWizard.add.provide.select.permissions": "Select permissions", "RolePermissions.Common.AddRoleWizard.add.role.alias.label": "Role alias", "RolePermissions.Common.AddRoleWizard.add.role.custom.scope.assignments": "Custom scope assignments", "RolePermissions.Common.AddRoleWizard.add.role.mapping.label": "Mapping role", "RolePermissions.Common.AddRoleWizard.add.role.warn.empty": "Role name can not be empty!", "RolePermissions.Common.AddRoleWizard.add.scope.error": "Something went wrong while adding new scope mapping", "RolePermissions.Common.AddRoleWizard.add.scope.error.add.role.alias": "Something went wrong while adding new role alias", "RolePermissions.Common.AddRoleWizard.add.scope.error.add.role.alias.success": "Add new alias for {newRole} successfully", "RolePermissions.Common.AddRoleWizard.add.scope.error.empty.permission": "You need to select at least one permission!", "RolePermissions.Common.AddRoleWizard.add.scope.error.role.empty": "Mapped role selection can't be empty!", "RolePermissions.Common.AddRoleWizard.add.scope.success": "Added scope mapping for {newRole} successfully", "RolePermissions.Common.AddRoleWizard.add.type.role": "Type existing user role, If not create a new role from carbon console first", "RolePermissions.Common.AddRoleWizard.add.type.role.label": "Role Name", "RolePermissions.Common.AddRoleWizard.selected.role": "Role {role_value} will be mapped to the selected role", "RolePermissions.Common.DeletePermission.delete.scope.error": "Something went wrong while deleting the scope assignments", "RolePermissions.ListRoles.error.retrieving.perm": "Error while retrieving permission info", "RolePermissions.ListRoles.page.description": "Scope assignments are only related to internal, APIM-specific scope assignments. They are not related to role permission assignments in the Management Console.", "RolePermissions.ListRoles.permission.delete.button": "Delete", "RolePermissions.ListRoles.permission.denied.content": "You do not have enough permission to view <PERSON>ope Assignments. Please contact the site administrator.", "RolePermissions.ListRoles.permission.denied.title": "Permission Denied", "RolePermissions.ListRoles.scope.assignment.button": "Add scope mappings", "RolePermissions.ListRoles.scope.assignment.cancel.button": "Cancel", "RolePermissions.ListRoles.scope.assignment.delete.button": "Delete", "RolePermissions.ListRoles.scope.assignment.delete.dialog.content": "Are you sure you want to delete scope assignments for {role} ?", "RolePermissions.ListRoles.scope.assignment.delete.dialog.title": "Delete scope assignments of {role} ?", "RolePermissions.ListRoles.scope.assignment.delete.scope.success": "Scope Assignments {role} deleted successfully", "RolePermissions.ListRoles.table.column.role": "Roles", "RolePermissions.ListRoles.table.column.scope.assignments": "Scope Assignments", "RolePermissions.ListRoles.title.role.permissions": "Scope Assignments", "RolePermissions.TreeView.PermissionsSelector.scope.assignment.button": "Scope Assignments", "RolePermissions.TreeView.PermissionsSelector.scope.assignment.cancel.btn": "Cancel", "RolePermissions.TreeView.PermissionsSelector.scope.assignment.save.btn": "Save", "RolePermissions.TreeView.PermissionsSelector.scope.assignment.title": "Select Scope Assignments", "RolePermissions.TreeView.PermissionsSelector.update.scope.error": "Something went wrong while updating the permission", "RolePermissions.TreeView.PermissionsSelector.update.scope.success": "Update permissions for {role} successfully", "ScopeAssignments.List.search.default": "Search by Role Name", "Settings.Advanced.TenantConf.edit.success": "Advanced Configuration saved successfully", "Settings.Advanced.TenantConfSave.form.cancel": "Cancel", "Settings.Advanced.TenantConfSave.form.save": "Save", "Settings.Advanced.TenantConfSave.title.save": "Advanced Configurations", "Settings.UsageReport.title": "Usage Report", "TenantTheme.Upload.Theme.browse.files.to.upload": "Browse File to Upload", "TenantTheme.Upload.Theme.button.upload": "Upload", "TenantTheme.Upload.Theme.download.error": "Error downloading Tenant theme ZIP file", "TenantTheme.Upload.Theme.drag.and.drop.message": "Drag & Drop files here {break} or {break}", "TenantTheme.Upload.Theme.help.link.one": "Tenant theming", "TenantTheme.Upload.Theme.info.message": "The theme should be a zip file containing CSS and images compliant with the", "TenantTheme.Upload.Theme.info.message.link": "API Manager theme format", "TenantTheme.Upload.Theme.page.heading": "Manage Tenant Theme", "TenantTheme.Upload.Theme.upload.files": "Upload/Download Theme", "TenantTheme.Upload.Theme.upload.successful": "Theme uploaded successfully", "TenantTheme.Upload.Theme.warning.message": "Zip file contains unsupported files. Upload only supported files.", "Throttling.Advanced.AddEdit.ConditionalGroups.form.description.help": "Description of this group", "Throttling.Advanced.AddEdit.add.conditional.group": "Conditional groups", "Throttling.Advanced.AddEdit.add.conditional.group.add": "Add Conditional Group", "Throttling.Advanced.AddEdit.add.success": "Policy Added Successfully", "Throttling.Advanced.AddEdit.conditional.group.description": "To add throttling limits with different parameters base on IP, Header, Query Param, and JWT Claim conditions, click Add Conditional Group.", "Throttling.Advanced.AddEdit.default.limits": "De<PERSON>ult <PERSON>", "Throttling.Advanced.AddEdit.default.limits.description": "Request Count and Request Bandwidth are the two options for default limit. You can use the option according to your requirement.", "Throttling.Advanced.AddEdit.edit.success": "Policy Updated Successfully", "Throttling.Advanced.AddEdit.empty.error": "contains white spaces.", "Throttling.Advanced.AddEdit.form.actions.label": "Actions", "Throttling.Advanced.AddEdit.form.add.btn": "Add", "Throttling.Advanced.AddEdit.form.bandwidth.allowed.help": "Bandwidth allowed", "Throttling.Advanced.AddEdit.form.cancel": "Cancel", "Throttling.Advanced.AddEdit.form.dataAmount.label": "Data Bandwidth", "Throttling.Advanced.AddEdit.form.description": "Description", "Throttling.Advanced.AddEdit.form.description.help": "Description of the throttle policy.", "Throttling.Advanced.AddEdit.form.has.errors": "One or more fields contain errors.", "Throttling.Advanced.AddEdit.form.name.help": "Name of the throttle policy.", "Throttling.Advanced.AddEdit.form.policyName": "Name", "Throttling.Advanced.AddEdit.form.request.count.allowed.help": "Number of requests allowed", "Throttling.Advanced.AddEdit.form.requestCount.label": "Request Count", "Throttling.Advanced.AddEdit.form.timeUnit.day": "Day(s)", "Throttling.Advanced.AddEdit.form.timeUnit.hour": "Hour(s)", "Throttling.Advanced.AddEdit.form.timeUnit.minute": "Minute(s)", "Throttling.Advanced.AddEdit.form.timeUnit.month": "Month(s)", "Throttling.Advanced.AddEdit.form.unit.time.help": "Time configuration", "Throttling.Advanced.AddEdit.form.unit.time.label": "Unit Time", "Throttling.Advanced.AddEdit.form.update.btn": "Update", "Throttling.Advanced.AddEdit.general.details": "General Details", "Throttling.Advanced.AddEdit.general.details.description": "Provide name and description of the policy.The policy can be refered from the name.", "Throttling.Advanced.AddEdit.is.empty.error": "is empty", "Throttling.Advanced.AddEdit.policy.name.too.long.error.msg": "Throttling policy name is too long", "Throttling.Advanced.AddEdit.special.characters.error": "contains invalid characters.", "Throttling.Advanced.AddEdit.title.edit": "Advance Rate Limiting Policy - Edit", "Throttling.Advanced.AddEdit.title.new": "Advanced Rate Limiting Policy - Create new", "Throttling.Advanced.AddEditConditionPolicy.dialog.btn.save": "Save", "Throttling.Advanced.AddEditConditionPolicy.dialog.tilte.add.new": "Add New", "Throttling.Advanced.AddEditConditionPolicy.dialog.tilte.edit": "Edit", "Throttling.Advanced.AddEditConditionPolicy.dialog.trigger.add": "Add", "Throttling.Advanced.AddEditConditionPolicy.form.name": "Name", "Throttling.Advanced.AddEditConditionPolicy.form.name.help": "Provide Name", "Throttling.Advanced.AddEditConditionPolicy.form.value": "Value", "Throttling.Advanced.AddEditConditionPolicy.form.value.help": "Provide Value", "Throttling.Advanced.AddEditConditionPolicyIP.dialog.btn.save": "Save", "Throttling.Advanced.AddEditConditionPolicyIP.dialog.tilte.add.new": "Add New IP Condition Policy", "Throttling.Advanced.AddEditConditionPolicyIP.dialog.title.edit": "Edit IP Condition Policy", "Throttling.Advanced.AddEditConditionPolicyIP.dialog.trigger.add": "Add", "Throttling.Advanced.AddEditConditionPolicyIP.form.end.ip": "End IP", "Throttling.Advanced.AddEditConditionPolicyIP.form.end.ip.help": "Provide Valid IP", "Throttling.Advanced.AddEditConditionPolicyIP.form.specific.ip": "Specific IP", "Throttling.Advanced.AddEditConditionPolicyIP.form.specific.ip.help": "Provide Valid IP", "Throttling.Advanced.AddEditConditionPolicyIP.form.start.ip": "Start IP", "Throttling.Advanced.AddEditConditionPolicyIP.form.start.ip.help": "Provide Valid IP", "Throttling.Advanced.AddEditConditionPolicyIP.ip.condition.type": "IP Condition Type", "Throttling.Advanced.AddEditConditionPolicyIP.ip.range": "IP Range", "Throttling.Advanced.AddEditConditionPolicyIP.is.empty.error": "is empty", "Throttling.Advanced.AddEditConditionPolicyIP.specific.ip": "Specific IP", "Throttling.Advanced.AddEditConditionPolicyIP.valid.ip.address.error": "Invalid IP Addresss", "Throttling.Advanced.AddEditConditionPolicyIP.valid.ip.range.error": "Invalid IP Range", "Throttling.Advanced.AddEditExecution.default.limit.option": "Default Limit Option", "Throttling.Advanced.AddEditExecution.default.limit.option.request.bandwith.label": "Request Bandwidth", "Throttling.Advanced.AddEditExecution.default.limit.option.request.count.label": "Request Count", "Throttling.Advanced.ConditionalGroup.alert": "To add rate limiting with different parameters for Query Params, Header Data and JWT token claim, you must enable them in the deployment.toml file.", "Throttling.Advanced.ConditionalGroup.alert.title": "Warning", "Throttling.Advanced.ConditionalGroup.condition.policies": "Condition Policies", "Throttling.Advanced.ConditionalGroup.execution.policy": "Execution Policy", "Throttling.Advanced.ConditionalGroup.expand.label": "Expand to edit", "Throttling.Advanced.ConditionalGroup.from": "From:", "Throttling.Advanced.ConditionalGroup.header": "Header Condition Policy", "Throttling.Advanced.ConditionalGroup.header.help": "This configuration is used to throttle based on Headers.", "Throttling.Advanced.ConditionalGroup.header.name": "Name", "Throttling.Advanced.ConditionalGroup.header.value": "Value", "Throttling.Advanced.ConditionalGroup.hide.label": "Hide group", "Throttling.Advanced.ConditionalGroup.invert.condition": "Invert Condition", "Throttling.Advanced.ConditionalGroup.ip": "IP Condition Policy", "Throttling.Advanced.ConditionalGroup.ip.header.name": "IP Condition Type", "Throttling.Advanced.ConditionalGroup.ip.header.value": "IP Address", "Throttling.Advanced.ConditionalGroup.ip.help": "This configuration is used to throttle by IP address.", "Throttling.Advanced.ConditionalGroup.ip.iprange": "IP Range", "Throttling.Advanced.ConditionalGroup.ip.specific": "Specific IP", "Throttling.Advanced.ConditionalGroup.jwt": "JWT Condition Policy", "Throttling.Advanced.ConditionalGroup.jwt.help": "This configuration is used to define JWT claims conditions", "Throttling.Advanced.ConditionalGroup.query.param": "Query Param Condition Policy", "Throttling.Advanced.ConditionalGroup.query.param.help": "This configuration is used to throttle based on query parameters.", "Throttling.Advanced.ConditionalGroup.to": "To:", "Throttling.Advanced.ConditionalGroups.form.description": "Description", "Throttling.Advanced.Delete.confirm.text": "Policy deletion might affect current subscriptions. Are you sure you want to delete this policy?", "Throttling.Advanced.Delete.ip.iprange": "IP Range", "Throttling.Advanced.Delete.ip.specific": "Specific IP", "Throttling.Advanced.Delete.save.text": "Delete", "Throttling.Advanced.Delete.success": "Policy Deleted Successfully", "Throttling.Advanced.Delete.title": "Delete Policy", "Throttling.Advanced.Delete.will.be.deleted": "will be deleted.", "Throttling.Advanced.DeleteConditionGroup.question": "Do you want to remove this condition group?", "Throttling.Advanced.DeleteConditionGroup.title": "Delete Condition Group", "Throttling.Advanced.List.add.new.polcy": "Add New Policy", "Throttling.Advanced.List.addButtonProps.title": "Add Policy", "Throttling.Advanced.List.addButtonProps.triggerButtonText": "Add Policy", "Throttling.Advanced.List.empty.content": "It is possible to create a Microgateway distribution for a group of APIs. In order to group APIs, a label needs to be created and attached to the APIs that need to be in a single group.", "Throttling.Advanced.List.empty.title": "Advanced Throttling Policies", "Throttling.Advanced.List.help.link.one": "Introducing Rate Limiting Use-Cases", "Throttling.Advanced.List.help.link.two": "Adding a new advanced rate limiting policy", "Throttling.Advanced.List.search.default": "Search by Advanced Policy name", "Throttling.Advanced.List.title.main": "Advanced Rate Limiting Policies", "Throttling.Application.AddEdit.burst.control.limit": "Number of requests for burst control", "Throttling.Application.AddEdit.burst.rate.limit.min": "Requests/min", "Throttling.Application.AddEdit.burst.rate.limit.seconds": "Requests/sec", "Throttling.Application.AddEdit.form.request.rate": "Request Rate", "Throttling.Application.Policy..List.search.default": "Search by Application Policy name", "Throttling.Application.Policy.List.addButtonProps.title": "Add Policy", "Throttling.Application.Policy.List.addButtonProps.triggerButtonText": "Add Policy", "Throttling.Application.Policy.List.empty.content.application.policies": "Application-level throttling policies are applicable per access token generated for an application.", "Throttling.Application.Policy.List.empty.title.application.policies": "Application Policies", "Throttling.Application.Policy.List.help.link.one": "Create an Application Rate Limiting Policy", "Throttling.Application.Policy.List.help.link.two": "Setting an Application Rate Limiting Policy", "Throttling.Application.Policy.policy.add.success": "Application Rate Limiting Policy added successfully.", "Throttling.Application.Policy.policy.data.amount.empty": "Data Amount is Empty", "Throttling.Application.Policy.policy.delete.error": "Application Rate Limiting Policy could not be deleted.", "Throttling.Application.Policy.policy.delete.success": "Application Rate Limiting Policy successfully deleted.", "Throttling.Application.Policy.policy.dialog.delete.btn": "Delete", "Throttling.Application.Policy.policy.dialog.delete.error": "Application Rate Limiting Policy will be deleted.", "Throttling.Application.Policy.policy.dialog.delete.title": "Delete Application Policy?", "Throttling.Application.Policy.policy.edit.success": "Application Rate Limiting Policy edited successfully.", "Throttling.Application.Policy.policy.name.empty": "Name is Empty", "Throttling.Application.Policy.policy.name.invalid.character": "Name contains one or more illegal characters", "Throttling.Application.Policy.policy.name.space": "Name contains spaces", "Throttling.Application.Policy.policy.name.too.long": "Application policy name is too long", "Throttling.Application.Policy.policy.request.count.empty": "Request Count is Empty", "Throttling.Application.Policy.policy.unit.time.empty": "Unit Time is Empty", "Throttling.Application.Policy.policy.unit.time.negative": "Invalid Time Value", "Throttling.Application.Policy.search.default": "Application Rate Limiting Policies", "Throttling.Blacklist.Policy.List.addButtonProps.title": "Select Item to Deny", "Throttling.Blacklist.Policy.List.addButtonProps.triggerButtonText": "Add Policy", "Throttling.Blacklist.Policy.List.empty.content.blacklist.policies and abuse by": "Denying requests from malicious entities helps you to keep your servers safe", "Throttling.Blacklist.Policy.List.empty.title.blacklist.policies": "<PERSON><PERSON>", "Throttling.Blacklist.Policy.List.help.link.one": "Denying requests", "Throttling.Blacklist.Policy.List.search.default": "Search by Deny Policy name", "Throttling.Blacklist.Policy.policy.add.success": "Deny Policy added successfully.", "Throttling.Blacklist.Policy.policy.delete.dialog.title": "Delete Deny Policy?", "Throttling.Blacklist.Policy.policy.delete.error": "Deny Policy could not be deleted.", "Throttling.Blacklist.Policy.policy.delete.success": "Deny Policy successfully deleted.", "Throttling.Blacklist.Policy.policy.dialog.delete.error": "Deny Policy will be deleted.", "Throttling.Blacklist.Policy.policy.endingIp.blank": "Ending Ip address field is empty.", "Throttling.Blacklist.Policy.policy.endingIp.invalid": "Starting Ip address entered is not valid.", "Throttling.Blacklist.Policy.policy.endingIp.white.spaces": "Starting Ip address field contains white spaces.", "Throttling.Blacklist.Policy.policy.fixedIp.blank": "Ending Ip address field is empty.", "Throttling.Blacklist.Policy.policy.fixedIp.invalid": "Starting Ip address entered is not valid.", "Throttling.Blacklist.Policy.policy.fixedIp.white.spaces": "Starting Ip address field contains white spaces.", "Throttling.Blacklist.Policy.policy.startingIp.blank": "Starting Ip address field is empty.", "Throttling.Blacklist.Policy.policy.startingIp.invalid": "Starting Ip address entered is not valid.", "Throttling.Blacklist.Policy.policy.startingIp.white.spaces": "Starting Ip address field contains white spaces.", "Throttling.Blacklist.Policy.policy.update.success": "Condition status has been updated successfully.", "Throttling.Blacklist.Policy.search.default": "<PERSON><PERSON>", "Throttling.Custom.AddEdit.form.add": "Add", "Throttling.Custom.AddEdit.form.cancel": "Cancel", "Throttling.Custom.AddEdit.form.edit": "Edit", "Throttling.Custom.AddEdit.title.add": "Custom Rate Limiting Policy - Define Policy", "Throttling.Custom.AddEdit.title.edit": "Custom Rate Limiting Policy - Edit", "Throttling.Custom.List.add.new.polcy": "Define Policy", "Throttling.Custom.Policy.List.addButtonProps.title": "Define Custom Policy", "Throttling.Custom.Policy.List.addButtonProps.triggerButtonText": "Define Policy", "Throttling.Custom.Policy.List.empty.content.custom.policies and abuse by": "Custom throttling allows system administrators to define dynamic rules for specific use cases, which are applied globally across all tenants.", "Throttling.Custom.Policy.List.empty.title.custom.policies": "Custom Policies", "Throttling.Custom.Policy.List.help.link.one": "Custom Throttling Policy", "Throttling.Custom.Policy.List.search.default": "Search by Custom Policy name", "Throttling.Custom.Policy.policy.add.success": "Custom Policy added successfully.", "Throttling.Custom.Policy.policy.delete.btn": "Delete", "Throttling.Custom.Policy.policy.delete.error": "Custom Policy could not be deleted.", "Throttling.Custom.Policy.policy.delete.success": "Custom Policy successfully deleted.", "Throttling.Custom.Policy.policy.delete.title": "Delete Custom Policy?", "Throttling.Custom.Policy.policy.dialog.delete.error": "Custom Policy will be deleted.", "Throttling.Custom.Policy.policy.edit.success": "Custom Policy edited successfully", "Throttling.Custom.Policy.policy.invalid.key.template": "Invalid Key Template", "Throttling.Custom.Policy.policy.name.empty": "Name is Empty", "Throttling.Custom.Policy.policy.name.invalid.character": "Name contains one or more illegal characters", "Throttling.Custom.Policy.policy.name.space": "Name contains spaces", "Throttling.Custom.Policy.policy.name.too.long.error.msg": "Custom policy name is too long", "Throttling.Custom.Policy.search.default": "Custom Rate Limiting Policies", "Throttling.Subscription.AddEdit.burst.control.add.description": "Define Burst Control Limits for the subscription policy. This is optional.", "Throttling.Subscription.AddEdit.burst.control.limit": "Number of requests for burst control", "Throttling.Subscription.AddEdit.burst.control.limit.time.unit.minute": "Requests/min", "Throttling.Subscription.AddEdit.burst.control.limit.time.unit.second": "Requests/s", "Throttling.Subscription.AddEdit.burst.control.limits": "Burst Control (Rate Limiting)", "Throttling.Subscription.AddEdit.custom.attributes.add.description": "Define custom attributes for the subscription policy.", "Throttling.Subscription.AddEdit.form.add": "Save", "Throttling.Subscription.AddEdit.form.add.data.amount.helper.text": "Bandwidth allowed", "Throttling.Subscription.AddEdit.form.cancel": "Cancel", "Throttling.Subscription.AddEdit.form.completionTokenCount.count": "Completion Token Count", "Throttling.Subscription.AddEdit.form.dataAmount.name": "Data Bandwidth", "Throttling.Subscription.AddEdit.form.description": "Description", "Throttling.Subscription.AddEdit.form.description.help": "Description of the rate limiting policy", "Throttling.Subscription.AddEdit.form.eventCount.count": "Event Count", "Throttling.Subscription.AddEdit.form.eventCount.count.helper.text": "Number of events allowed", "Throttling.Subscription.AddEdit.form.max.complexity": "Max Complexity", "Throttling.Subscription.AddEdit.form.max.depth": "<PERSON>", "Throttling.Subscription.AddEdit.form.max.webhooks.connections": "Max Subscriptions", "Throttling.Subscription.AddEdit.form.name.help": "Name of the rate limiting policy", "Throttling.Subscription.AddEdit.form.policyName": "Name", "Throttling.Subscription.AddEdit.form.promptTokenCount.count": "Prompt Token Count", "Throttling.Subscription.AddEdit.form.request.rate": "Request Rate", "Throttling.Subscription.AddEdit.form.requestCount.count": "Request Count", "Throttling.Subscription.AddEdit.form.totalTokenCount.count": "Total Token Count", "Throttling.Subscription.AddEdit.form.unit.time.label": "Unit Time", "Throttling.Subscription.AddEdit.general.details": "General Details", "Throttling.Subscription.AddEdit.general.details.description": "Provide the name and description of the subscription policy.", "Throttling.Subscription.AddEdit.graphql.add.description": "Provide the Maximum Complexity and Maximum depth values for GraphQL APIs using this policy.", "Throttling.Subscription.AddEdit.permissions.add.description": "Define the permissions for the subscription policy.", "Throttling.Subscription.AddEdit.permissions.add.role.label": "Roles", "Throttling.Subscription.AddEdit.permissions.add.role.placeholder": "Type roles and press Enter", "Throttling.Subscription.AddEdit.policy.flags.add.description": "Define the billing plan for the subscription policy. Enable stop on quota reach to block invoking an API when the defined quota is reached.", "Throttling.Subscription.AddEdit.quota.limits": "<PERSON><PERSON><PERSON>", "Throttling.Subscription.AddEdit.quota.limits.ai.description": "Specify the quota limits for AI API Subscription policy.", "Throttling.Subscription.AddEdit.quota.policies.add.description": "Request Count and Request Bandwidth are the two options for Quota Limit. You can use the option according to your requirement.", "Throttling.Subscription.AddEdit.quota.policies.add.limits.event.count": "Event Based (Async API)", "Throttling.Subscription.AddEdit.quota.policies.add.limits.request.bandwidth": "Request Bandwidth", "Throttling.Subscription.AddEdit.quota.policies.add.limits.request.count": "Request Count", "Throttling.Subscription.AddEdit.subscription.count.add.description": "Maximum number of webhooks allowed for a Webhooks API using this policy.", "Throttling.Subscription.AddEdit.title.AIPolicy.add": "AI API Subscription Rate Limiting Policy - Create new", "Throttling.Subscription.AddEdit.title.AIPolicy.edit": "AI API Subscription Rate Limiting Policy - Edit", "Throttling.Subscription.AddEdit.title.add": "Subscription Rate Limiting Policy - Create new", "Throttling.Subscription.AddEdit.title.edit": "Subscription Rate Limiting Policy - Edit", "Throttling.Subscription.AddEdit.unitTime": "Unit Time", "Throttling.Subscription.Billing.Plan": "Billing Plan", "Throttling.Subscription.Billing.Plan.type.commercial": "Commercial", "Throttling.Subscription.Billing.Plan.type.free": "Free", "Throttling.Subscription.Fixed.Rate": "Fixed Rate", "Throttling.Subscription.GraphQL": "GraphQL", "Throttling.Subscription.Policy..List.search.default": "Search by Subscription Policy name", "Throttling.Subscription.Policy.Flags": "Policy Flags", "Throttling.Subscription.Policy.List.addButtonProps.title": "Add Policy", "Throttling.Subscription.Policy.List.empty.title.subscription.policies": "Subscription Policies", "Throttling.Subscription.Policy.List.help.link.one": "Creating a Subscription Rate Limiting Policy", "Throttling.Subscription.Policy.List.help.link.three": "Setting a Subscription Rate Limiting Policy as an API Subscriber", "Throttling.Subscription.Policy.List.help.link.two": "Setting a Subscription Rate Limiting Policy as an API Publisher", "Throttling.Subscription.Policy.policy.add.success": "Subscription Rate Limiting Policy added successfully.", "Throttling.Subscription.Policy.policy.data.amount.empty.error.msg": "Data Bandwidth amount is Empty", "Throttling.Subscription.Policy.policy.delete.btn": "Delete", "Throttling.Subscription.Policy.policy.delete.error": "Subscription Rate Limiting Policy could not be deleted.", "Throttling.Subscription.Policy.policy.delete.success": "Subscription Rate Limiting Policy successfully deleted.", "Throttling.Subscription.Policy.policy.delete.title": "Delete Subscription Policy?", "Throttling.Subscription.Policy.policy.dialog.delete.error": "Subscription Rate Limiting Policy will be deleted.", "Throttling.Subscription.Policy.policy.edit.success": "Subscription Rate Limiting Policy updated successfully.", "Throttling.Subscription.Policy.policy.event.count.empty.error.msg": "Event Count is Empty", "Throttling.Subscription.Policy.policy.name.empty.error.msg": "Name is Empty", "Throttling.Subscription.Policy.policy.name.invalid.character.error.msg": "Name contains one or more illegal characters", "Throttling.Subscription.Policy.policy.name.space.error.msg": "Name contains spaces", "Throttling.Subscription.Policy.policy.name.too.long.error.msg": "Subscription policy name is too long", "Throttling.Subscription.Policy.policy.request.count.empty.error.msg": "Request Count is Empty", "Throttling.Subscription.Policy.policy.unit.time.empty.error.msg": "Unit Time is Empty", "Throttling.Subscription.Policy.policy.unit.time.negative.error.msg": "Invalid Time Value", "Throttling.Subscription.Policy.search.default": "Subscription Rate Limiting Policies", "Throttling.Subscription.Properties.Properties.show.add.property.property.name": "Name", "Throttling.Subscription.Properties.property.value": "Value", "Throttling.Subscription.Roles.Invalid": "A Role is invalid", "Throttling.Subscription.Subscriber.Count": "Webhooks", "Throttling.Subscription.attribute.delete.tooltip": "Delete", "Throttling.Subscription.billing.cycle": "Billing Cycle", "Throttling.Subscription.billing.cycle.menu.month": "Month", "Throttling.Subscription.billing.cycle.menu.week": "Week", "Throttling.Subscription.billing.cycle.menu.year": "Year", "Throttling.Subscription.currency": "<PERSON><PERSON><PERSON><PERSON>", "Throttling.Subscription.custom.attributes": "Custom Attributes", "Throttling.Subscription.custom.attributes.add": "Add Custom Attribute", "Throttling.Subscription.dynamic.usage": "Price Per Request", "Throttling.Subscription.dynamic.usage.tooltip": "Price per request for the given billing cycle in the given currency", "Throttling.Subscription.enter.permission.allowed": "This policy is \"Allowed\" for above roles.", "Throttling.Subscription.enter.permission.denied": "This policy is \"Denied\" for above roles.", "Throttling.Subscription.fixed.rate.tooltip": "Fixed rate for the given billing cycle", "Throttling.Subscription.monetization.plan": "Monetization Plan", "Throttling.Subscription.monetization.plan.dynamic.usage": "Dynamic Usage", "Throttling.Subscription.monetization.plan.fixed.rate": "Fixed Rate", "Throttling.Subscription.monetization.plan.tooltip": "Monetization category type", "Throttling.Subscription.permissions": "Permissions", "Throttling.Subscription.price.per.request": "Price Per Request", "Throttling.Subscription.stop.quota.reach": "Stop On Quota Reach", "Throttling.Subsription.Policy.List.empty.content.subscription.policies": "Subscription-level throttling policies are applicable per access token generated for an application.", "TransactionList.download.report": "Download Report", "TransactionList.total.transactions": "Total Transaction Count :", "TransactionList.view.report": "View", "UnexpectedError.logout": "Logout", "UnexpectedError.message": "Error occurred due to invalid request", "UnexpectedError.title": "Internal Server Error", "Workflow.APIProductStateChange.List.empty.content.apiProduct.statechange": "There are no pending workflow requests for API Product state change", "Workflow.APIProductStateChange.List.empty.title.apistatechange": "API Product State Change", "Workflow.APIProductStateChange.apicall.has.errors": "Unable to get workflow pending requests for API Product State Change", "Workflow.APIProductStateChange.title.apistatechange": "API Product State Change - Approval Tasks", "Workflow.APIProductStateChange.updateStatus.has.errors": "Unable to complete API Product state change approve/reject process.", "Workflow.APIRevisionDeployment.List.empty.content.revisiondeployments": "There are no pending workflow requests for revision deployment.", "Workflow.APIRevisionDeployment.List.empty.title.revisiondeployments": "Revision Deployment", "Workflow.APIRevisionDeployment.apiCall.has.errors": "Unable to get workflow pending requests for Revision Deployment", "Workflow.APIRevisionDeployment.help.link.one": "Create a Revision Deployment approval workflow Request", "Workflow.APIRevisionDeployment.permission.denied.content": "You do not have enough permission to view Revision Deployment - Approval Tasks. Please contact the site administrator.", "Workflow.APIRevisionDeployment.permission.denied.title": "Permission Denied", "Workflow.APIRevisionDeployment.search.default": "Search by Revision Id, API name, Environment or Created by", "Workflow.APIRevisionDeployment.table.button.approve": "Approve", "Workflow.APIRevisionDeployment.table.button.reject": "Reject", "Workflow.APIRevisionDeployment.table.header.Action": "Action", "Workflow.APIRevisionDeployment.table.header.apiName": "API", "Workflow.APIRevisionDeployment.table.header.apiVersion": "API", "Workflow.APIRevisionDeployment.table.header.description": "Description", "Workflow.APIRevisionDeployment.table.header.environment": "Environment", "Workflow.APIRevisionDeployment.table.header.revisionId": "Revision Id", "Workflow.APIRevisionDeployment.table.header.userName": "Created by", "Workflow.APIRevisionDeployment.title.revisiondeployment": "Revision Deployment - Approval Tasks", "Workflow.APIRevisionDeployment.update.success": "Workflow status is updated successfully", "Workflow.APIRevisionDeployment.updateStatus.has.errors": "Unable to complete revision deployment approve/reject process.", "Workflow.APIStateChange.List.empty.content.apistatechange": "There are no pending workflow requests for API state change.", "Workflow.APIStateChange.List.empty.title.apistatechange": "API State Change", "Workflow.APIStateChange.apicall.has.errors": "Unable to get workflow pending requests for API State Change", "Workflow.APIStateChange.table.button.approve": "Approve", "Workflow.APIStateChange.table.button.reject": "Reject", "Workflow.APIStateChange.table.header.Action": "Action", "Workflow.APIStateChange.table.header.ApiProvider": "Created by", "Workflow.APIStateChange.table.header.CurrentState": "Current State", "Workflow.APIStateChange.table.header.Description": "Description", "Workflow.APIStateChange.table.header.RequestState": "Request State", "Workflow.APIStateChange.title.apistatechange": "API State Change - Approval Tasks", "Workflow.APIStateChange.update.success": "Workflow status is updated successfully.", "Workflow.APIStateChange.updateStatus.has.errors": "Unable to complete API state change approve/reject process.", "Workflow.APIStatechange.help.link.one": "Create a API State change approval workflow request", "Workflow.ApiProduct.StateChange.permission.denied.content": "You dont have enough permission to view API Product State Change - Approval Tasks. Please contact the site administrator.", "Workflow.ApiStateChange.permission.denied.content": "You dont have enough permission to view API State Change - Approval Tasks. Please contact the site administrator.", "Workflow.ApiStateChange.permission.denied.title": "Permission Denied", "Workflow.ApplicationCreation.List.empty.content.applicationcreations": "There are no pending workflow requests for application creation.", "Workflow.ApplicationCreation.List.empty.title.applicationcreations": "Application Creation", "Workflow.ApplicationCreation.apicall.has.errors": "Unable to get workflow pending requests for Application Creation", "Workflow.ApplicationCreation.help.link.one": "Create a Application Creation approval workflow Request", "Workflow.ApplicationCreation.permission.denied.content": "You dont have enough permission to view Application Creation - Approval Tasks. Please contact the site administrator.", "Workflow.ApplicationCreation.permission.denied.title": "Permission Denied", "Workflow.ApplicationCreation.table.button.approve": "Approve", "Workflow.ApplicationCreation.table.button.reject": "Reject", "Workflow.ApplicationCreation.table.header.Action": "Action", "Workflow.ApplicationCreation.table.header.ApplicationTier": "Throttling Policy", "Workflow.ApplicationCreation.table.header.applicationName": "Application", "Workflow.ApplicationCreation.table.header.description": "Description", "Workflow.ApplicationCreation.table.header.userName": "Created by", "Workflow.ApplicationCreation.title.applicationcreation": "Application Creation - Approval Tasks", "Workflow.ApplicationCreation.update.success": "Workflow status is updated successfully", "Workflow.ApplicationCreation.updateStatus.has.errors": "Unable to complete application creation approve/reject process.", "Workflow.ApplicationDeletion.List.empty.content.applicationdeletions": "There are no pending workflow requests for application creation.", "Workflow.ApplicationDeletion.List.empty.title.applicationdeletions": "Application Deletion", "Workflow.ApplicationDeletion.apicall.has.errors": "Unable to get workflow pending requests for Application Deletion", "Workflow.ApplicationDeletion.help.link.one": "Create a Application Creation approval workflow Request", "Workflow.ApplicationDeletion.permission.denied.content": "You dont have enough permission to view Application Creation - Approval Tasks. Please contact the site administrator.", "Workflow.ApplicationDeletion.permission.denied.title": "Permission Denied", "Workflow.ApplicationDeletion.table.button.approve": "Approve", "Workflow.ApplicationDeletion.table.button.reject": "Reject", "Workflow.ApplicationDeletion.table.header.Action": "Action", "Workflow.ApplicationDeletion.table.header.ApplicationTier": "Throttling Policy", "Workflow.ApplicationDeletion.table.header.applicationName": "Application", "Workflow.ApplicationDeletion.table.header.description": "Description", "Workflow.ApplicationDeletion.table.header.userName": "Created by", "Workflow.ApplicationDeletion.title.applicationdeletion": "Application Deletion - Approval Tasks", "Workflow.ApplicationDeletion.update.success": "Workflow status is updated successfully", "Workflow.ApplicationDeletion.updateStatus.has.errors": "Unable to complete application creation approve/reject process.", "Workflow.ApplicationRegistration.List.empty.content.applicationregistrations": "There are no pending workflow requests for application registration (key generation).", "Workflow.ApplicationRegistration.List.empty.title.applicationregistrations": "Application Registration", "Workflow.ListUserCreation.search.default": "Search by user name or domain", "Workflow.ListUserCreation.title.usercreation": "User Creation - Approval Tasks", "Workflow.RegistrationCreation.apicall.has.errors": "Unable to get workflow pending requests for Registration Creation", "Workflow.RegistrationCreation.link.one": "Create a application registration workflow request", "Workflow.RegistrationCreation.permission.denied.content": "You dont have enough permission to view Application Registration - Approval Tasks. Please contact the site administrator.", "Workflow.RegistrationCreation.permission.denied.title": "Permission Denied", "Workflow.RegistrationCreation.search.default": "Search by Application, Throttling Policy, Key type or Creator", "Workflow.RegistrationCreation.table.button.approve": "Approve", "Workflow.RegistrationCreation.table.button.reject": "Reject", "Workflow.RegistrationCreation.table.header.Action": "Action", "Workflow.RegistrationCreation.table.header.Application": "Application", "Workflow.RegistrationCreation.table.header.ApplicationTier": "Throttling Policy", "Workflow.RegistrationCreation.table.header.Creater": "Created by", "Workflow.RegistrationCreation.table.header.Description": "Description", "Workflow.RegistrationCreation.table.header.KeyType": "Key Type", "Workflow.RegistrationCreation.title.registrationcreation": "Application Registration - Approval Tasks", "Workflow.RegistrationCreation.update.success": "Workflow status is updated successfully", "Workflow.RegistrationCreation.updateStatus.has.errors": "Unable to complete registration creation approve/reject process.", "Workflow.SubscriptionCreation.List.empty.content.subscriptioncreations": "There are no pending workflow requests for subscription creation.", "Workflow.SubscriptionCreation.List.empty.title.subscriptioncreations": "Subscription Creation", "Workflow.SubscriptionCreation.apicall.has.errors": "Unable to get workflow pending requests for Subscription Creation", "Workflow.SubscriptionCreation.help.link.one": "Create a subscription creation request", "Workflow.SubscriptionCreation.permission.denied.content": "You dont have enough permission to view Subscription Creation - Approval Tasks. Please contact the site administrator.", "Workflow.SubscriptionCreation.permission.denied.title": "Permission Denied", "Workflow.SubscriptionCreation.search.default": "Search by API, Application or Subscriber", "Workflow.SubscriptionCreation.table.button.approve": "Approve", "Workflow.SubscriptionCreation.table.button.reject": "Reject", "Workflow.SubscriptionCreation.table.header.API": "API", "Workflow.SubscriptionCreation.table.header.Action": "Action", "Workflow.SubscriptionCreation.table.header.Application": "Application", "Workflow.SubscriptionCreation.table.header.Description": "Description", "Workflow.SubscriptionCreation.table.header.Subscriber": "Subscriber", "Workflow.SubscriptionCreation.title.subscriptioncreation": "Subscription Creation - Approval Tasks", "Workflow.SubscriptionCreation.title.subscriptionupdate": "Subscription Tier Update - Approval Tasks", "Workflow.SubscriptionCreation.update.success": "Workflow status is updated successfully", "Workflow.SubscriptionCreation.updateStatus.has.errors": "Unable to complete subscription creation approve/reject process.", "Workflow.SubscriptionDeletion.List.empty.content.subscriptiondeletions": "There are no pending workflow requests for subscription deletion.", "Workflow.SubscriptionDeletion.List.empty.title.subscriptiondeletions": "Subscription Deletion", "Workflow.SubscriptionDeletion.apicall.has.errors": "Unable to get workflow pending requests for Subscription Deletion", "Workflow.SubscriptionDeletion.help.link.one": "Create a subscription deletion request", "Workflow.SubscriptionDeletion.permission.denied.content": "You dont have enough permission to view Subscription Deletion - Approval Tasks. Please contact the site administrator.", "Workflow.SubscriptionDeletion.permission.denied.title": "Permission Denied", "Workflow.SubscriptionDeletion.search.default": "Search by API, Application or Subscriber", "Workflow.SubscriptionDeletion.table.button.approve": "Approve", "Workflow.SubscriptionDeletion.table.button.reject": "Reject", "Workflow.SubscriptionDeletion.table.header.API": "API", "Workflow.SubscriptionDeletion.table.header.Action": "Action", "Workflow.SubscriptionDeletion.table.header.Application": "Application", "Workflow.SubscriptionDeletion.table.header.Description": "Description", "Workflow.SubscriptionDeletion.table.header.Subscriber": "Subscriber", "Workflow.SubscriptionDeletion.title.subscriptiondeletion": "Subscription Deletion - Approval Tasks", "Workflow.SubscriptionDeletion.update.success": "Workflow status is updated successfully", "Workflow.SubscriptionUpdate.List.empty.content.subscriptionUpdates": "There are no pending workflow requests for subscription updates.", "Workflow.SubscriptionUpdate.List.empty.title.subscriptionUpdate": "Subscription Update", "Workflow.SubscriptionUpdate.apicall.has.errors": "Unable to get workflow pending requests for Subscription Update", "Workflow.SubscriptionUpdate.help.link.one": "Create a subscription update request", "Workflow.SubscriptionUpdate.permission.denied.content": "You dont have enough permission to view Subscription Tier Update - Approval Tasks. Please contact the site administrator.", "Workflow.SubscriptionUpdate.permission.denied.title": "Permission Denied", "Workflow.SubscriptionUpdate.search.default": "Search by API, Application or Subscriber", "Workflow.SubscriptionUpdate.table.button.approve": "Approve", "Workflow.SubscriptionUpdate.table.button.reject": "Reject", "Workflow.SubscriptionUpdate.table.header.API": "API", "Workflow.SubscriptionUpdate.table.header.Action": "Action", "Workflow.SubscriptionUpdate.table.header.Application": "Application", "Workflow.SubscriptionUpdate.table.header.Description": "Description", "Workflow.SubscriptionUpdate.table.header.Subscriber": "Subscriber", "Workflow.SubscriptionUpdate.update.success": "Workflow status is updated successfully", "Workflow.SubscriptionUpdate.updateStatus.has.errors": "Unable to complete subscription update approve/reject process.", "Workflow.SubsriptionDeletion.updateStatus.has.errors": "Unable to complete subscription deletion approve/reject process.", "Workflow.UserCreation.List.empty.content.usercreations": "There are no workflow pending requests for user creation.It is possible to approve or reject workflow pending requests of user sign up. Workflow Approval Executor needs to be enabled to approve or reject the requests.", "Workflow.UserCreation.List.empty.title.usercreations": "User Creation", "Workflow.UserCreation.apicall.has.errors": "Unable to get workflow pending requests for User Creation", "Workflow.UserCreation.help.link.one": "Create a user self sign up request", "Workflow.UserCreation.permission.denied.content": "You dont have enough permission to view User Creation - Approval Tasks. Please contact the site administrator.", "Workflow.UserCreation.permission.denied.title": "Permission Denied", "Workflow.UserCreation.table.button.approve": "Approve", "Workflow.UserCreation.table.button.reject": "Reject", "Workflow.UserCreation.table.header.Action": "Action", "Workflow.UserCreation.table.header.Description": "Description", "Workflow.UserCreation.table.header.ElapsedTime": "Elapsed time", "Workflow.UserCreation.table.header.TenantDomain": "Tenant Domain", "Workflow.UserCreation.table.header.TenantName": "User Name", "Workflow.UserCreation.update.success": "Workflow status is updated successfully", "Workflow.UserCreation.updateStatus.has.errors": "Unable to complete User creation approve/reject process.", "Workflow.api.statechange.search.default": "Search by API, Request state, Current state or Creator", "Workflow.apiProduct.statechange.search.default": "Search by API Product, Request state, Current state or Creator", "Workflow.applicationcreation.search.default": "Search by Application, Throttling Policy or Creator", "Workflow.applicationdeletion.search.default": "Search by Application, Throttling Policy or Creator", "dsdds": "The specific combination of attributes being checked in the policy need to be defined as the key template. Allowed values are : $userId, $apiContext, $apiVersion, $resourceKey, $appTenant, $apiTenant, $appId, $clientIp, $customProperty, $customProperty.somevalue", "error.list.401": "401 : Authorization Required.", "error.list.401.description": "The server could not verify that you are authorized to access the requested resource", "error.list.403": "403 : Forbidden.", "error.list.403.description": "You do not have permission to access anything with that kind of request.", "error.list.404": "404 : The page cannot be found.", "error.list.404.description": "The page you are looking for might have been removed, had its name changed or is temporarily unavailable.", "error.list.500": "500 : The page cannot be displayed.", "error.list.500.description": "The server encountered an internal error or misconfiguration and was unable to complete your request."}