/*
 * Copyright (c) 2017, WSO2 Inc. (http://www.wso2.org) All Rights Reserved.
 *
 * WSO2 Inc. licenses this file to you under the Apache License,
 * Version 2.0 (the "License"); you may not use this file except
 * in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

import React from 'react';
import PropTypes from 'prop-types';

const Progress = (props) => {
    const { message } = props;
    return (
        <div className='apim-dual-ring'>
            <span style={{ width: '200px', display: 'block' }}>{message}</span>
        </div>
    );
};

Progress.defaultProps = {
    message: '',
};

Progress.propTypes = {
    message: PropTypes.string,
};
export default Progress;
