## What is this directory ?

// todo: modify this accordingly

This directory contains the data fetching related source files.So these are vanilla JS source codes. Please use the `.js`
extension in the files created under

```
<PUBLISHER_UI_ROOT>/source/src/app/data/
```

directory.

If es-lint suggest to rename the file extension to `.jsx` (That means you have jsx syntax there),
 You are most probably putting the file in wrong directory. Consider moving it to

```
<PUBLISHER_UI_ROOT>/source/src/app/components/
```

directory.
