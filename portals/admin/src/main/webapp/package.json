{"name": "@wso2apim/admin", "version": "4.0.1", "description": "WSO2 API Manager Admin portal Web App Note: `eslint-plugin-react` have a fixed version due to implementing prop-types does mark nested destructed props from `eslint-plugin-react` next version onwards\n --max_old_space_size: This is max size of the old space( managed heap) in Mbytes make sure not to increase it more than the memory size of builder machines(Prod build)", "main": "app.jsx", "engines": {"npm": ">=10.0.0", "node": ">=22.0.0"}, "scripts": {"test:ci": "echo To be implemented", "test:coverage": "jest --coverage", "build:prod": "rimraf site/public/dist/ && npm run i18n:en && npm run i18n:fr && NODE_ENV=production NODE_OPTIONS=--max_old_space_size=4096 webpack --mode production --stats=errors-only", "build:dev": "NODE_ENV=development && npm run i18n:en && rimraf site/public/dist/ && webpack --mode development --watch", "analysis": "NODE_ENV=analysis NODE_OPTIONS=--max_old_space_size=8172 webpack --mode production --progress", "lint": "eslint --ignore-pattern '*.test.js' --ignore-pattern '*.test.jsx' --quiet -c .eslintrc.js --ext .jsx,.js source", "i18n": "extract-messages -l=en,fr -o site/public/locales/ -d en --extractFromFormatMessageCall --flat 'source/src/app/**/*.jsx'", "extract-i18n": "formatjs extract", "i18n:en": "npm run extract-i18n -- 'source/src/app/**/*.jsx' --ignore='**/*.d.{js,ts,tsx,jsx}' --out-file site/public/locales/en.json --id-interpolation-pattern '[sha512:contenthash:base64:6]' --format=simple", "i18n:fr": "npm run extract-i18n -- 'source/src/app/**/*.jsx' --ignore='**/*.d.{js,ts,tsx,jsx}' --out-file site/public/locales/fr.json --id-interpolation-pattern '[sha512:contenthash:base64:6]' --format=simple", "check-unused": "webpack --mode production --env.unused true --display errors-only", "start": "ESLINT_NO_DEV_ERRORS=true npm run i18n:en && webpack serve --compress --port 8083 --mode development --watch"}, "repository": {"type": "git", "url": "git://github.com/wso2/carbon-apimgt"}, "author": "WSO2 Org", "license": "Apache-2.0", "dependencies": {"@apidevtools/swagger-parser": "^10.1.0", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@hapi/joi": "^15.1.1", "@monaco-editor/react": "^4.6.0", "@mui/icons-material": "^5.15.4", "@mui/lab": "^5.0.0-alpha.160", "@mui/material": "^5.15.4", "@mui/system": "^5.15.4", "@mui/x-charts": "^7.24.0", "@mui/x-date-pickers": "^7.14.0", "@mui/x-tree-view": "^6.17.0", "@react-pdf/renderer": "^3.4.4", "async-mutex": "^0.5.0", "async-react-component": "^0.7.0", "autosuggest-highlight": "^3.3.4", "base64url": "^3.0.1", "dayjs": "^1.11.7", "fastestsmallesttextencoderdecoder": "^1.0.22", "lodash.clonedeep": "^4.5.0", "lodash.isempty": "^4.4.0", "lodash.sortby": "^4.7.0", "moment": "^2.29.4", "mui-chips-input": "^2.1.3", "mui-datatables": "^4.3.0", "prop-types": "^15.8.1", "qs": "^6.11.0", "rc-notification": "^4.6.1", "react": "^18.0.0", "react-app-polyfill": "^3.0.0", "react-dom": "^18.0.0", "react-dropzone": "^12.1.0", "react-hot-toast": "^2.4.0", "react-intl": "^6.6.8", "react-pdf": "^9.1.0", "react-router": "^5.3.4", "react-router-dom": "^5.3.4", "react-spring": "^8.0.27", "sql-formatter": "^4.0.2", "swagger-client": "^3.18.5", "url": "^0.11.3", "url-loader": "^4.1.1"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/eslint-parser": "^7.24.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-class-properties": "^7.24.7", "@babel/plugin-transform-object-rest-spread": "^7.24.7", "@babel/plugin-transform-spread": "^7.24.7", "@babel/preset-env": "^7.24.7", "@babel/preset-react": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@babel/register": "^7.24.6", "@formatjs/cli": "^6.2.12", "@stoplight/prism-http": "^5.8.1", "babel-jest": "^29.7.0", "babel-loader": "^9.1.0", "babel-plugin-dynamic-import-node": "^2.3.3", "buffer": "^6.0.3", "crypto-browserify": "^3.12.0", "css-loader": "^7.1.2", "enzyme": "^3.11.0", "eslint": "^7.32.0", "eslint-config-airbnb": "^18.1.0", "eslint-plugin-import": "^2.21.1", "eslint-plugin-jest": "^23.13.2", "eslint-plugin-jsx-a11y": "^6.0.3", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-react": "^7.20.0", "eslint-plugin-react-hooks": "^4.5.0", "eslint-webpack-plugin": "^2.5.1", "html-webpack-plugin": "^5.6.0", "jest": "^29.7.0", "less": "^4.2.0", "less-loader": "^11.1.3", "mock-local-storage": "^1.1.24", "prettier": "^2.5.1", "style-loader": "^4.0.0", "underscore-template-loader": "^1.2.0", "util": "^0.12.5", "webpack": "^5.92.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-deadcode-plugin": "^0.1.17", "webpack-dev-server": "^5.0.4"}}